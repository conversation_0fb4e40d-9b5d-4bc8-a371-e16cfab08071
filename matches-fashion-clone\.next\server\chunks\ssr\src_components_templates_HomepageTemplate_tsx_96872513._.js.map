{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/HomepageTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface HomepageTemplateProps {\n  children: React.ReactNode;\n}\n\nconst HomepageTemplate: React.FC<HomepageTemplateProps> = ({ children }) => {\n  return (\n    <div className=\"bg-white\">\n      {children}\n    </div>\n  );\n};\n\nexport default HomepageTemplate;\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IACrE,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}]}