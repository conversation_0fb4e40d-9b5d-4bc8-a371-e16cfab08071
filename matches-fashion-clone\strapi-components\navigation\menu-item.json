{"collectionName": "components_navigation_menu_items", "info": {"displayName": "<PERSON><PERSON>", "description": "Navigation menu item"}, "options": {}, "attributes": {"label": {"type": "string", "required": true, "maxLength": 50}, "url": {"type": "string", "required": true}, "is_external": {"type": "boolean", "default": false}, "target": {"type": "enumeration", "enum": ["_self", "_blank"], "default": "_self"}, "icon": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "description": {"type": "text", "maxLength": 200}, "sort_order": {"type": "integer", "default": 0}, "is_featured": {"type": "boolean", "default": false}, "submenu": {"type": "component", "repeatable": true, "component": "navigation.submenu-item"}, "page": {"type": "relation", "relation": "oneToOne", "target": "api::page.page"}}}