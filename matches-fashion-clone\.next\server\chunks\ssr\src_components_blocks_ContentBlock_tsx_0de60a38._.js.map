{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/blocks/ContentBlock.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface ContentBlockProps {\n  data: {\n    title?: string;\n    content?: string;\n    image?: {\n      data?: {\n        attributes: {\n          url: string;\n          alternativeText?: string;\n        };\n      };\n    };\n    cta_text?: string;\n    cta_link?: string;\n    layout?: 'text-only' | 'image-left' | 'image-right' | 'image-top';\n    background_color?: string;\n    text_color?: string;\n  };\n  context: 'main' | 'sidebar';\n}\n\nconst ContentBlock: React.FC<ContentBlockProps> = ({ data, context }) => {\n  const {\n    title,\n    content,\n    image,\n    cta_text,\n    cta_link,\n    layout = 'text-only',\n    background_color,\n    text_color\n  } = data;\n\n  const imageUrl = image?.data?.attributes?.url;\n  const altText = image?.data?.attributes?.alternativeText || title || 'Content image';\n\n  const containerClasses = `\n    ${context === 'sidebar' ? 'p-4' : 'p-6 md:p-8'}\n    ${background_color ? '' : 'bg-white'}\n    ${text_color ? '' : 'text-gray-900'}\n  `;\n\n  const style: React.CSSProperties = {\n    ...(background_color && { backgroundColor: background_color }),\n    ...(text_color && { color: text_color })\n  };\n\n  const renderContent = () => (\n    <div className=\"space-y-4\">\n      {title && (\n        <h2 className={`font-bold ${context === 'sidebar' ? 'text-lg' : 'text-2xl md:text-3xl'}`}>\n          {title}\n        </h2>\n      )}\n      \n      {content && (\n        <div \n          className={`prose ${context === 'sidebar' ? 'prose-sm' : 'prose-lg'} max-w-none`}\n          dangerouslySetInnerHTML={{ __html: content }}\n        />\n      )}\n      \n      {cta_text && cta_link && (\n        <Link\n          href={cta_link}\n          className=\"inline-block px-6 py-3 bg-black text-white font-medium uppercase tracking-wide hover:bg-gray-800 transition-colors duration-300\"\n        >\n          {cta_text}\n        </Link>\n      )}\n    </div>\n  );\n\n  const renderImage = () => (\n    imageUrl && (\n      <div className={`${context === 'sidebar' ? 'aspect-video' : 'aspect-[4/3]'} overflow-hidden`}>\n        <img\n          src={imageUrl}\n          alt={altText}\n          className=\"w-full h-full object-cover\"\n        />\n      </div>\n    )\n  );\n\n  if (layout === 'text-only' || !imageUrl) {\n    return (\n      <div className={containerClasses} style={style}>\n        {renderContent()}\n      </div>\n    );\n  }\n\n  if (layout === 'image-top') {\n    return (\n      <div className={containerClasses} style={style}>\n        {renderImage()}\n        <div className=\"mt-6\">\n          {renderContent()}\n        </div>\n      </div>\n    );\n  }\n\n  if (layout === 'image-left' || layout === 'image-right') {\n    return (\n      <div className={containerClasses} style={style}>\n        <div className={`grid grid-cols-1 ${context === 'sidebar' ? 'gap-4' : 'md:grid-cols-2 gap-6 md:gap-8'} items-center`}>\n          {layout === 'image-left' && (\n            <>\n              {renderImage()}\n              {renderContent()}\n            </>\n          )}\n          {layout === 'image-right' && (\n            <>\n              {renderContent()}\n              {renderImage()}\n            </>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={containerClasses} style={style}>\n      {renderContent()}\n    </div>\n  );\n};\n\nexport default ContentBlock;\n"], "names": [], "mappings": ";;;;AACA;;;AAuBA,MAAM,eAA4C,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IAClE,MAAM,EACJ,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,WAAW,EACpB,gBAAgB,EAChB,UAAU,EACX,GAAG;IAEJ,MAAM,WAAW,OAAO,MAAM,YAAY;IAC1C,MAAM,UAAU,OAAO,MAAM,YAAY,mBAAmB,SAAS;IAErE,MAAM,mBAAmB,CAAC;IACxB,EAAE,YAAY,YAAY,QAAQ,aAAa;IAC/C,EAAE,mBAAmB,KAAK,WAAW;IACrC,EAAE,aAAa,KAAK,gBAAgB;EACtC,CAAC;IAED,MAAM,QAA6B;QACjC,GAAI,oBAAoB;YAAE,iBAAiB;QAAiB,CAAC;QAC7D,GAAI,cAAc;YAAE,OAAO;QAAW,CAAC;IACzC;IAEA,MAAM,gBAAgB,kBACpB,8OAAC;YAAI,WAAU;;gBACZ,uBACC,8OAAC;oBAAG,WAAW,CAAC,UAAU,EAAE,YAAY,YAAY,YAAY,wBAAwB;8BACrF;;;;;;gBAIJ,yBACC,8OAAC;oBACC,WAAW,CAAC,MAAM,EAAE,YAAY,YAAY,aAAa,WAAW,WAAW,CAAC;oBAChF,yBAAyB;wBAAE,QAAQ;oBAAQ;;;;;;gBAI9C,YAAY,0BACX,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAM;oBACN,WAAU;8BAET;;;;;;;;;;;;IAMT,MAAM,cAAc,IAClB,0BACE,8OAAC;YAAI,WAAW,GAAG,YAAY,YAAY,iBAAiB,eAAe,gBAAgB,CAAC;sBAC1F,cAAA,8OAAC;gBACC,KAAK;gBACL,KAAK;gBACL,WAAU;;;;;;;;;;;IAMlB,IAAI,WAAW,eAAe,CAAC,UAAU;QACvC,qBACE,8OAAC;YAAI,WAAW;YAAkB,OAAO;sBACtC;;;;;;IAGP;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAW;YAAkB,OAAO;;gBACtC;8BACD,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;IAIT;IAEA,IAAI,WAAW,gBAAgB,WAAW,eAAe;QACvD,qBACE,8OAAC;YAAI,WAAW;YAAkB,OAAO;sBACvC,cAAA,8OAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,YAAY,YAAY,UAAU,gCAAgC,aAAa,CAAC;;oBACjH,WAAW,8BACV;;4BACG;4BACA;;;oBAGJ,WAAW,+BACV;;4BACG;4BACA;;;;;;;;;;;;;;IAMb;IAEA,qBACE,8OAAC;QAAI,WAAW;QAAkB,OAAO;kBACtC;;;;;;AAGP;uCAEe", "debugId": null}}]}