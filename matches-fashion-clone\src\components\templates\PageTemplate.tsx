import React, { Suspense } from 'react';
import { StrapiPage, BreadcrumbItem } from '../../../lib/page-manager';
import { Loading } from '../ui/Loading';

interface PageTemplateProps {
  page: StrapiPage;
  breadcrumbs: BreadcrumbItem[];
  templateComponent: Promise<{ default: React.ComponentType<any> }>;
  children: React.ReactNode;
}

const PageTemplate: React.FC<PageTemplateProps> = ({ 
  page, 
  breadcrumbs, 
  templateComponent, 
  children 
}) => {
  const { attributes } = page;

  return (
    <div className="min-h-screen bg-white">
      {/* Page-specific template wrapper */}
      <Suspense fallback={<Loading size="lg" text="Loading page template..." />}>
        <TemplateWrapper templateComponent={templateComponent}>
          {children}
        </TemplateWrapper>
      </Suspense>

      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            name: attributes.title,
            description: attributes.meta_description,
            url: typeof window !== 'undefined' ? window.location.href : '',
            breadcrumb: {
              '@type': 'BreadcrumbList',
              itemListElement: breadcrumbs.map((item, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: item.label,
                item: item.url
              }))
            }
          })
        }}
      />
    </div>
  );
};

// Helper component to handle dynamic template loading
const TemplateWrapper: React.FC<{
  templateComponent: Promise<{ default: React.ComponentType<any> }>;
  children: React.ReactNode;
}> = ({ templateComponent, children }) => {
  const [TemplateComponent, setTemplateComponent] = React.useState<React.ComponentType<any> | null>(null);

  React.useEffect(() => {
    templateComponent.then(module => {
      setTemplateComponent(() => module.default);
    }).catch(() => {
      // Fallback to default template
      setTemplateComponent(() => ({ children }: { children: React.ReactNode }) => (
        <div className="max-w-7xl mx-auto px-4 py-8">
          {children}
        </div>
      ));
    });
  }, [templateComponent]);

  if (!TemplateComponent) {
    return <Loading size="lg" text="Loading template..." />;
  }

  return <TemplateComponent>{children}</TemplateComponent>;
};

export default PageTemplate;
