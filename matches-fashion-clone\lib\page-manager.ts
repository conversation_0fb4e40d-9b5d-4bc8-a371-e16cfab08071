import { fetchStrapiAPI } from './strapi';
import { notFound } from 'next/navigation';

// Types for Strapi page management
export interface StrapiPage {
  id: number;
  attributes: {
    title: string;
    slug: string;
    meta_title?: string;
    meta_description?: string;
    page_type: 'homepage' | 'category_index' | 'product_listing' | 'content_page' | 'landing_page' | 'collection_page';
    template: 'default' | 'homepage' | 'category-grid' | 'product-list' | 'full-width' | 'sidebar-left' | 'sidebar-right';
    status: 'draft' | 'published' | 'archived';
    featured_image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    content_blocks?: ContentBlock[];
    sidebar_content?: ContentBlock[];
    breadcrumbs?: BreadcrumbItem[];
    parent_page?: {
      data?: {
        attributes: {
          title: string;
          slug: string;
        };
      };
    };
    medusa_category_id?: string;
    medusa_collection_id?: string;
    custom_fields?: Record<string, unknown>;
    sort_order: number;
    is_menu_item: boolean;
    menu_label?: string;
  };
}

export interface ContentBlock {
  __component: string;
  id: number;
  [key: string]: unknown;
}

export interface BreadcrumbItem {
  label: string;
  url: string;
  isActive?: boolean;
}

export interface NavigationMenu {
  id: number;
  attributes: {
    main_menu: MenuItem[];
    footer_menu: MenuItem[];
    mobile_menu: MenuItem[];
    breadcrumb_settings?: Record<string, unknown>;
  };
}

export interface MenuItem {
  id: number;
  label: string;
  url: string;
  is_external: boolean;
  target: '_self' | '_blank';
  icon?: {
    data?: {
      attributes: {
        url: string;
        alternativeText?: string;
      };
    };
  };
  description?: string;
  sort_order: number;
  is_featured: boolean;
  submenu?: MenuItem[];
  page?: {
    data?: {
      attributes: {
        title: string;
        slug: string;
      };
    };
  };
}

// Page Manager Class
export class PageManager {
  private static instance: PageManager;
  private pageCache = new Map<string, StrapiPage>();
  private navigationCache: NavigationMenu | null = null;

  static getInstance(): PageManager {
    if (!PageManager.instance) {
      PageManager.instance = new PageManager();
    }
    return PageManager.instance;
  }

  // Get page by slug
  async getPageBySlug(slug: string): Promise<StrapiPage | null> {
    // Check cache first
    if (this.pageCache.has(slug)) {
      return this.pageCache.get(slug)!;
    }

    try {
      const result = await fetchStrapiAPI('/pages', {
        'filters[slug][$eq]': slug,
        'filters[status][$eq]': 'published',
        populate: 'deep'
      });

      if (result.success && result.data?.data && result.data.data.length > 0) {
        const page = result.data.data[0] as StrapiPage;
        this.pageCache.set(slug, page);
        return page;
      }

      return null;
    } catch (error) {
      console.error('Error fetching page:', error);
      return null;
    }
  }

  // Get homepage
  async getHomepage(): Promise<StrapiPage | null> {
    try {
      const result = await fetchStrapiAPI('/pages', {
        'filters[page_type][$eq]': 'homepage',
        'filters[status][$eq]': 'published',
        populate: 'deep'
      });

      if (result.success && result.data?.data && result.data.data.length > 0) {
        return result.data.data[0] as StrapiPage;
      }

      return null;
    } catch (error) {
      console.error('Error fetching homepage:', error);
      return null;
    }
  }

  // Get all pages for sitemap generation
  async getAllPages(): Promise<StrapiPage[]> {
    try {
      const result = await fetchStrapiAPI('/pages', {
        'filters[status][$eq]': 'published',
        populate: 'deep',
        'pagination[limit]': 100
      });

      if (result.success && result.data?.data) {
        return result.data.data as StrapiPage[];
      }

      return [];
    } catch (error) {
      console.error('Error fetching all pages:', error);
      return [];
    }
  }

  // Get navigation
  async getNavigation(): Promise<NavigationMenu | null> {
    if (this.navigationCache) {
      return this.navigationCache;
    }

    try {
      const result = await fetchStrapiAPI('/navigation', {
        populate: 'deep'
      });

      if (result.success && result.data?.data) {
        this.navigationCache = result.data.data as NavigationMenu;
        return this.navigationCache;
      }

      return null;
    } catch (error) {
      console.error('Error fetching navigation:', error);
      return null;
    }
  }

  // Get pages by type
  async getPagesByType(pageType: string): Promise<StrapiPage[]> {
    try {
      const result = await fetchStrapiAPI('/pages', {
        'filters[page_type][$eq]': pageType,
        'filters[status][$eq]': 'published',
        populate: 'deep',
        sort: 'sort_order:asc'
      });

      if (result.success && result.data?.data) {
        return result.data.data as StrapiPage[];
      }

      return [];
    } catch (error) {
      console.error('Error fetching pages by type:', error);
      return [];
    }
  }

  // Generate breadcrumbs
  generateBreadcrumbs(page: StrapiPage, basePath: string = ''): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', url: '/' }
    ];

    // Add parent pages
    if (page.attributes.parent_page?.data) {
      const parent = page.attributes.parent_page.data.attributes;
      breadcrumbs.push({
        label: parent.title,
        url: `${basePath}/${parent.slug}`
      });
    }

    // Add current page
    breadcrumbs.push({
      label: page.attributes.title,
      url: `${basePath}/${page.attributes.slug}`,
      isActive: true
    });

    return breadcrumbs;
  }

  // Clear cache
  clearCache(): void {
    this.pageCache.clear();
    this.navigationCache = null;
  }
}

// Utility functions
export const pageManager = PageManager.getInstance();

export async function getPageData(slug: string): Promise<StrapiPage> {
  const page = await pageManager.getPageBySlug(slug);
  
  if (!page) {
    notFound();
  }

  return page;
}

export async function generatePageMetadata(page: StrapiPage) {
  return {
    title: page.attributes.meta_title || page.attributes.title,
    description: page.attributes.meta_description || `${page.attributes.title} - Matches Fashion`,
    openGraph: {
      title: page.attributes.meta_title || page.attributes.title,
      description: page.attributes.meta_description || `${page.attributes.title} - Matches Fashion`,
      images: page.attributes.featured_image?.data ? [
        {
          url: page.attributes.featured_image.data.attributes.url,
          alt: page.attributes.featured_image.data.attributes.alternativeText || page.attributes.title,
        }
      ] : [],
    },
  };
}
