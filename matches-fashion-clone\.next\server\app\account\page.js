(()=>{var e={};e.id=298,e.ids=[298],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11504:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\page.tsx","default")},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33638:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(60687),a=r(85814),o=r.n(a),n=r(18780),i=r(16189);function l(){let{user:e,logout:s}=(0,n.A)();return((0,i.useRouter)(),e)?(0,t.jsxs)("div",{className:"max-w-xl mx-auto py-10",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"My Account"}),(0,t.jsxs)("p",{className:"mb-6",children:["Signed in as ",(0,t.jsx)("strong",{children:e.email})]}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsx)(o(),{href:"/account/profile",className:"luxury-button-outline",children:"Profile"}),(0,t.jsx)(o(),{href:"/account/orders",className:"luxury-button-outline",children:"Orders"}),(0,t.jsx)(o(),{href:"/account/addresses",className:"luxury-button-outline",children:"Addresses"}),(0,t.jsx)(o(),{href:"/account/wishlists",className:"luxury-button-outline",children:"Wishlists"})]}),(0,t.jsxs)("div",{className:"space-x-4",children:[(0,t.jsx)("button",{className:"luxury-button",onClick:s,children:"Sign Out"}),(0,t.jsx)(o(),{href:"/returns",className:"luxury-button-outline",children:"Returns"})]})]}):null}r(43210)},33873:e=>{"use strict";e.exports=require("path")},42622:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var t=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let u={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11504)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},45656:(e,s,r)=>{Promise.resolve().then(r.bind(r,11504))},55384:(e,s,r)=>{Promise.resolve().then(r.bind(r,33638))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,20,137],()=>r(42622));module.exports=t})();