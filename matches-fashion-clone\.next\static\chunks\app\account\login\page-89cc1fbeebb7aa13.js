(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[700],{2657:(e,t,r)=>{Promise.resolve().then(r.bind(r,7134))},7134:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(5155),a=r(2115),l=r(5695),u=r(6874),n=r.n(u),i=r(9576);function o(){let[e,t]=(0,a.useState)(""),[r,u]=(0,a.useState)(""),[o,c]=(0,a.useState)(""),{login:d,user:m}=(0,i.A)(),h=(0,l.useRouter)();return m&&h.replace("/account"),(0,s.jsxs)("div",{className:"max-w-md mx-auto py-10",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Sign In"}),(0,s.jsxs)("form",{onSubmit:t=>{t.preventDefault(),d(e,r)||c("Invalid credentials")},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-1",children:"Email"}),(0,s.jsx)("input",{type:"email",value:e,onChange:e=>t(e.target.value),className:"w-full border border-gray-300 p-2",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-1",children:"Password"}),(0,s.jsx)("input",{type:"password",value:r,onChange:e=>u(e.target.value),className:"w-full border border-gray-300 p-2",required:!0})]}),o&&(0,s.jsx)("p",{className:"text-red-600 text-sm",children:o}),(0,s.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Sign In"})]}),(0,s.jsxs)("p",{className:"mt-4 text-sm",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/account/register",className:"underline",children:"Register"})]})]})}},9576:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>u});var s=r(5155),a=r(2115);let l=(0,a.createContext)(void 0),u=e=>{let{children:t}=e,[r,u]=(0,a.useState)(null);return(0,a.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&u(JSON.parse(e))},[]),(0,s.jsx)(l.Provider,{value:{user:r,login:(e,t)=>{let r=localStorage.getItem("authUser");if(r){let s=JSON.parse(r);if(s.email===e&&s.password===t)return u(s),!0}return!1},register:(e,t)=>{let r={email:e,password:t};return localStorage.setItem("authUser",JSON.stringify(r)),u(r),!0},logout:()=>{u(null),localStorage.removeItem("authUser")}},children:t})},n=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(2657)),_N_E=e.O()}]);