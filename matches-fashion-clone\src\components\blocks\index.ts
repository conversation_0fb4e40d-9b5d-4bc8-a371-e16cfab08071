// Placeholder components for content blocks
// These can be expanded with full functionality as needed

import React from 'react';

// Simple placeholder component factory
const createPlaceholderComponent = (name: string, description: string) => {
  return ({ data, context }: { data: any; context: 'main' | 'sidebar' }) => (
    <div className={`p-6 border-2 border-dashed border-gray-300 rounded-lg ${context === 'sidebar' ? 'text-sm' : ''}`}>
      <h3 className="font-bold text-lg mb-2">{name}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      {data && Object.keys(data).length > 0 && (
        <details className="text-xs">
          <summary className="cursor-pointer text-gray-500">View Data</summary>
          <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
};

// Export placeholder components
export const ImageGallery = createPlaceholderComponent(
  'Image Gallery',
  'A gallery of images with lightbox functionality'
);

export const CallToAction = createPlaceholderComponent(
  'Call to Action',
  'Prominent call-to-action section with button'
);

export const FeaturedProducts = createPlaceholderComponent(
  'Featured Products',
  'Showcase of hand-picked featured products'
);

export const CategoryShowcase = createPlaceholderComponent(
  'Category Showcase',
  'Display of product categories with images'
);

export const Testimonials = createPlaceholderComponent(
  'Testimonials',
  'Customer testimonials and reviews'
);

export const NewsletterSignup = createPlaceholderComponent(
  'Newsletter Signup',
  'Email newsletter subscription form'
);

export const CategoryMenu = createPlaceholderComponent(
  'Category Menu',
  'Hierarchical category navigation menu'
);

export const FilterWidget = createPlaceholderComponent(
  'Filter Widget',
  'Product filtering options for sidebar'
);

// Default exports for dynamic imports
export default {
  ImageGallery,
  CallToAction,
  FeaturedProducts,
  CategoryShowcase,
  Testimonials,
  NewsletterSignup,
  CategoryMenu,
  FilterWidget,
};
