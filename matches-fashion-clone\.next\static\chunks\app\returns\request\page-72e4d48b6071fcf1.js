(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[244],{163:(e,s,l)=>{"use strict";l.d(s,{default:()=>t});var r=l(5155),a=l(2115);function t(){let[e,s]=(0,a.useState)({orderId:"",email:"",reason:"",details:""}),[l,t]=(0,a.useState)(!1),n=e=>{let{name:l,value:r}=e.target;s(e=>({...e,[l]:r}))};return l?(0,r.jsxs)("div",{className:"p-6 bg-gray-50 text-center space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Return request submitted"}),(0,r.jsx)("p",{children:"We'll email you with next steps shortly."})]}):(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(!0)},className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Order Number"}),(0,r.jsx)("input",{required:!0,name:"orderId",value:e.orderId,onChange:n,className:"w-full border border-gray-300 p-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Email"}),(0,r.jsx)("input",{required:!0,type:"email",name:"email",value:e.email,onChange:n,className:"w-full border border-gray-300 p-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Reason for Return"}),(0,r.jsxs)("select",{name:"reason",value:e.reason,onChange:n,className:"w-full border border-gray-300 p-2",children:[(0,r.jsx)("option",{value:"",children:"Select..."}),(0,r.jsx)("option",{children:"Too small"}),(0,r.jsx)("option",{children:"Too large"}),(0,r.jsx)("option",{children:"Not as described"}),(0,r.jsx)("option",{children:"Ordered by mistake"}),(0,r.jsx)("option",{children:"Other"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Additional Details (optional)"}),(0,r.jsx)("textarea",{name:"details",value:e.details,onChange:n,className:"w-full border border-gray-300 p-2",rows:4})]}),(0,r.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Submit Request"})]})}},4653:(e,s,l)=>{Promise.resolve().then(l.t.bind(l,6874,23)),Promise.resolve().then(l.bind(l,163))}},e=>{var s=s=>e(e.s=s);e.O(0,[96,358],()=>s(4653)),_N_E=e.O()}]);