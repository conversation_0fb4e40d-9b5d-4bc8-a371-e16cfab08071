import React from 'react';
import { StrapiPage, pageManager } from '../../lib/page-manager';
import ContentBlockRenderer from './ContentBlockRenderer';
import Breadcrumbs from './Breadcrumbs';
import PageTemplate from './templates/PageTemplate';

interface DynamicPageRendererProps {
  page: StrapiPage;
}

const DynamicPageRenderer: React.FC<DynamicPageRendererProps> = ({ page }) => {
  const { attributes } = page;

  // Generate breadcrumbs
  const breadcrumbs = pageManager.generateBreadcrumbs(page);

  // Determine template component based on page template
  const getTemplateComponent = () => {
    switch (attributes.template) {
      case 'homepage':
        return import('./templates/HomepageTemplate');
      case 'category-grid':
        return import('./templates/CategoryGridTemplate');
      case 'product-list':
        return import('./templates/ProductListTemplate');
      case 'full-width':
        return import('./templates/FullWidthTemplate');
      case 'sidebar-left':
        return import('./templates/SidebarLeftTemplate');
      case 'sidebar-right':
        return import('./templates/SidebarRightTemplate');
      default:
        return import('./templates/DefaultTemplate');
    }
  };

  return (
    <PageTemplate
      page={page}
      breadcrumbs={breadcrumbs}
      templateComponent={getTemplateComponent()}
    >
      {/* Page Header */}
      {attributes.featured_image?.data && (
        <div className="relative w-full h-64 md:h-96 mb-8">
          <img
            src={attributes.featured_image.data.attributes.url}
            alt={attributes.featured_image.data.attributes.alternativeText || attributes.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-6xl font-bold mb-4">
                {attributes.title}
              </h1>
            </div>
          </div>
        </div>
      )}

      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbs} />

      {/* Page Title (if no featured image) */}
      {!attributes.featured_image?.data && (
        <div className="mb-8">
          <h1 className="text-4xl md:text-6xl font-bold text-center">
            {attributes.title}
          </h1>
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Sidebar Left */}
        {(attributes.template === 'sidebar-left' || attributes.template === 'sidebar-right') && 
         attributes.sidebar_content && attributes.sidebar_content.length > 0 && (
          <aside className={`lg:col-span-3 ${attributes.template === 'sidebar-right' ? 'lg:order-2' : ''}`}>
            <div className="space-y-6">
              {attributes.sidebar_content.map((block, index) => (
                <ContentBlockRenderer
                  key={`sidebar-${block.id || index}`}
                  block={block}
                  context="sidebar"
                />
              ))}
            </div>
          </aside>
        )}

        {/* Main Content Area */}
        <main className={`
          ${attributes.template === 'sidebar-left' || attributes.template === 'sidebar-right' 
            ? 'lg:col-span-9' 
            : 'lg:col-span-12'
          }
        `}>
          {/* Content Blocks */}
          {attributes.content_blocks && attributes.content_blocks.length > 0 ? (
            <div className="space-y-8">
              {attributes.content_blocks.map((block, index) => (
                <ContentBlockRenderer
                  key={`content-${block.id || index}`}
                  block={block}
                  context="main"
                  pageType={attributes.page_type}
                  medusaCategoryId={attributes.medusa_category_id}
                  medusaCollectionId={attributes.medusa_collection_id}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600">No content blocks configured for this page.</p>
            </div>
          )}
        </main>
      </div>

      {/* Custom Fields Data (for developers) */}
      {process.env.NODE_ENV === 'development' && attributes.custom_fields && (
        <div className="mt-12 p-4 bg-gray-100 rounded">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Custom Fields (Dev Only)</h3>
          <pre className="text-xs text-gray-600 overflow-auto">
            {JSON.stringify(attributes.custom_fields, null, 2)}
          </pre>
        </div>
      )}
    </PageTemplate>
  );
};

export default DynamicPageRenderer;
