(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[949],{4043:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(5155),l=s(9576),i=s(4303),a=s(5695),u=s(2115),n=s(6874),d=s.n(n);function o(){let{user:e}=(0,l.A)(),{addresses:t,addAddress:s,removeAddress:n}=(0,i.F)(),o=(0,a.useRouter)(),[c,h]=(0,u.useState)({line1:"",city:"",state:"",zip:"",country:""});return((0,u.useEffect)(()=>{e||o.replace("/account/login")},[e,o]),e)?(0,r.jsxs)("div",{className:"max-w-xl mx-auto py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Saved Addresses"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(c),h({line1:"",city:"",state:"",zip:"",country:""})},className:"space-y-2 mb-6",children:[(0,r.jsx)("input",{placeholder:"Address",value:c.line1,onChange:e=>h({...c,line1:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("input",{placeholder:"City",value:c.city,onChange:e=>h({...c,city:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("input",{placeholder:"State",value:c.state,onChange:e=>h({...c,state:e.target.value}),className:"w-full border p-2"}),(0,r.jsx)("input",{placeholder:"Zip",value:c.zip,onChange:e=>h({...c,zip:e.target.value}),className:"w-full border p-2"}),(0,r.jsx)("input",{placeholder:"Country",value:c.country,onChange:e=>h({...c,country:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Add Address"})]}),0===t.length?(0,r.jsx)("p",{children:"No saved addresses."}):(0,r.jsx)("ul",{className:"space-y-4",children:t.map(e=>(0,r.jsxs)("li",{className:"border p-4 flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:e.line1}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.city," ",e.state," ",e.zip]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.country})]}),(0,r.jsx)("button",{onClick:()=>n(e.id),className:"text-sm underline",children:"Remove"})]},e.id))}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(d(),{href:"/account",className:"luxury-button-outline",children:"Back to account"})})]}):null}},4303:(e,t,s)=>{"use strict";s.d(t,{AccountProvider:()=>d,F:()=>o});var r=s(5155),l=s(2115),i=s(9576);let a={addresses:[],orders:[],wishlists:[]},u=(0,l.createContext)(void 0);function n(){return Math.random().toString(36).slice(2)+Date.now().toString(36)}let d=e=>{let{children:t}=e,{user:s}=(0,i.A)(),[d,o]=(0,l.useState)(a);return(0,l.useEffect)(()=>{if(s){let e=localStorage.getItem("accountData-"+s.email);e?o(JSON.parse(e)):o(a)}else o(a)},[s]),(0,l.useEffect)(()=>{s&&localStorage.setItem("accountData-"+s.email,JSON.stringify(d))},[d,s]),(0,r.jsx)(u.Provider,{value:{...d,addAddress:e=>{o(t=>({...t,addresses:[...t.addresses,{...e,id:n()}]}))},removeAddress:e=>{o(t=>({...t,addresses:t.addresses.filter(t=>t.id!==e)}))},addWishlist:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";o(s=>({...s,wishlists:[...s.wishlists,{id:n(),name:e,note:t,items:[]}]}))},removeWishlist:e=>{o(t=>({...t,wishlists:t.wishlists.filter(t=>t.id!==e)}))},updateWishlistNote:(e,t)=>{o(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,note:t}:s)}))},addWishlistItem:(e,t)=>{o(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:[...s.items,{id:n(),title:t}]}:s)}))},removeWishlistItem:(e,t)=>{o(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:s.items.filter(e=>e.id!==t)}:s)}))}},children:t})},o=()=>{let e=(0,l.useContext)(u);if(!e)throw Error("useAccount must be used within AccountProvider");return e}},4960:(e,t,s)=>{Promise.resolve().then(s.bind(s,4043))},9576:(e,t,s)=>{"use strict";s.d(t,{A:()=>u,AuthProvider:()=>a});var r=s(5155),l=s(2115);let i=(0,l.createContext)(void 0),a=e=>{let{children:t}=e,[s,a]=(0,l.useState)(null);return(0,l.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&a(JSON.parse(e))},[]),(0,r.jsx)(i.Provider,{value:{user:s,login:(e,t)=>{let s=localStorage.getItem("authUser");if(s){let r=JSON.parse(s);if(r.email===e&&r.password===t)return a(r),!0}return!1},register:(e,t)=>{let s={email:e,password:t};return localStorage.setItem("authUser",JSON.stringify(s)),a(s),!0},logout:()=>{a(null),localStorage.removeItem("authUser")}},children:t})},u=()=>{let e=(0,l.useContext)(i);if(!e)throw Error("useAuth must be used within AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(4960)),_N_E=e.O()}]);