(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[373],{4303:(e,t,s)=>{"use strict";s.d(t,{AccountProvider:()=>d,F:()=>u});var i=s(5155),l=s(2115),a=s(9576);let r={addresses:[],orders:[],wishlists:[]},n=(0,l.createContext)(void 0);function o(){return Math.random().toString(36).slice(2)+Date.now().toString(36)}let d=e=>{let{children:t}=e,{user:s}=(0,a.A)(),[d,u]=(0,l.useState)(r);return(0,l.useEffect)(()=>{if(s){let e=localStorage.getItem("accountData-"+s.email);e?u(JSON.parse(e)):u(r)}else u(r)},[s]),(0,l.useEffect)(()=>{s&&localStorage.setItem("accountData-"+s.email,JSON.stringify(d))},[d,s]),(0,i.jsx)(n.Provider,{value:{...d,addAddress:e=>{u(t=>({...t,addresses:[...t.addresses,{...e,id:o()}]}))},removeAddress:e=>{u(t=>({...t,addresses:t.addresses.filter(t=>t.id!==e)}))},addWishlist:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";u(s=>({...s,wishlists:[...s.wishlists,{id:o(),name:e,note:t,items:[]}]}))},removeWishlist:e=>{u(t=>({...t,wishlists:t.wishlists.filter(t=>t.id!==e)}))},updateWishlistNote:(e,t)=>{u(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,note:t}:s)}))},addWishlistItem:(e,t)=>{u(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:[...s.items,{id:o(),title:t}]}:s)}))},removeWishlistItem:(e,t)=>{u(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:s.items.filter(e=>e.id!==t)}:s)}))}},children:t})},u=()=>{let e=(0,l.useContext)(n);if(!e)throw Error("useAccount must be used within AccountProvider");return e}},5843:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var i=s(5155),l=s(9576),a=s(4303),r=s(5695),n=s(2115),o=s(6874),d=s.n(o);function u(){let{user:e}=(0,l.A)(),{wishlists:t,addWishlist:s,removeWishlist:o,updateWishlistNote:u,addWishlistItem:c,removeWishlistItem:h}=(0,a.F)(),m=(0,r.useRouter)(),[x,f]=(0,n.useState)(""),[p,v]=(0,n.useState)(""),[N,g]=(0,n.useState)({});if((0,n.useEffect)(()=>{e||m.replace("/account/login")},[e,m]),!e)return null;let w=e=>{N[e]&&(c(e,N[e]),g(t=>({...t,[e]:""})))},b=e=>{let s=t.find(t=>t.id===e);if(!s)return;let i=function(e){let t=btoa(JSON.stringify(e));return"".concat(window.location.origin,"/wishlist-share?data=").concat(encodeURIComponent(t))}({name:s.name,note:s.note,items:s.items});navigator.clipboard.writeText(i),alert("Share link copied to clipboard")};return(0,i.jsxs)("div",{className:"max-w-xl mx-auto py-10 space-y-6",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Wishlists"}),(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),x&&(s(x,p),f(""),v(""))},className:"space-y-2",children:[(0,i.jsx)("input",{value:x,onChange:e=>f(e.target.value),placeholder:"Wishlist name",className:"w-full border p-2",required:!0}),(0,i.jsx)("textarea",{value:p,onChange:e=>v(e.target.value),placeholder:"Notes",className:"w-full border p-2"}),(0,i.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Create Wishlist"})]}),0===t.length?(0,i.jsx)("p",{children:"No wishlists yet."}):(0,i.jsx)("div",{className:"space-y-8",children:t.map(e=>(0,i.jsxs)("div",{className:"border p-4 space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h2",{className:"font-medium text-lg",children:e.name}),(0,i.jsx)("button",{onClick:()=>o(e.id),className:"text-sm underline",children:"Delete"})]}),(0,i.jsx)("textarea",{value:e.note,onChange:t=>u(e.id,t.target.value),className:"w-full border p-2",placeholder:"Add note"}),0===e.items.length?(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"No items."}):(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1",children:e.items.map(t=>(0,i.jsxs)("li",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:t.title}),(0,i.jsx)("button",{onClick:()=>h(e.id,t.id),className:"text-xs underline",children:"Remove"})]},t.id))}),(0,i.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,i.jsx)("input",{value:N[e.id]||"",onChange:t=>g(s=>({...s,[e.id]:t.target.value})),placeholder:"Add item",className:"flex-1 border p-2"}),(0,i.jsx)("button",{type:"button",onClick:()=>w(e.id),className:"luxury-button",children:"Add"}),(0,i.jsx)("button",{type:"button",onClick:()=>b(e.id),className:"luxury-button-outline",children:"Share"})]})]},e.id))}),(0,i.jsx)("div",{children:(0,i.jsx)(d(),{href:"/account",className:"luxury-button-outline",children:"Back to account"})})]})}},7592:(e,t,s)=>{Promise.resolve().then(s.bind(s,5843))},9576:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,AuthProvider:()=>r});var i=s(5155),l=s(2115);let a=(0,l.createContext)(void 0),r=e=>{let{children:t}=e,[s,r]=(0,l.useState)(null);return(0,l.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&r(JSON.parse(e))},[]),(0,i.jsx)(a.Provider,{value:{user:s,login:(e,t)=>{let s=localStorage.getItem("authUser");if(s){let i=JSON.parse(s);if(i.email===e&&i.password===t)return r(i),!0}return!1},register:(e,t)=>{let s={email:e,password:t};return localStorage.setItem("authUser",JSON.stringify(s)),r(s),!0},logout:()=>{r(null),localStorage.removeItem("authUser")}},children:t})},n=()=>{let e=(0,l.useContext)(a);if(!e)throw Error("useAuth must be used within AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(7592)),_N_E=e.O()}]);