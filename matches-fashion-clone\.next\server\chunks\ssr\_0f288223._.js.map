{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/lib/strapi.js"], "sourcesContent": ["// Use environment variables directly to avoid import issues\r\nconst STRAPI_API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || \"http://localhost:1337\";\r\nconst STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;\r\nconst isDevelopment = () => process.env.NODE_ENV === 'development';\r\n\r\n// Environment variables are already defined above\r\n\r\n// Enhanced error class for Strapi API errors\r\nexport class StrapiApiError extends Error {\r\n  constructor(message, status, statusText, url, responseBody, code) {\r\n    super(message);\r\n    this.name = 'StrapiApiError';\r\n    this.status = status;\r\n    this.statusText = statusText;\r\n    this.url = url;\r\n    this.responseBody = responseBody;\r\n    this.code = code;\r\n  }\r\n}\r\n\r\n// Retry configuration\r\nconst RETRY_CONFIG = {\r\n  maxRetries: 3,\r\n  retryDelay: 1000, // 1 second\r\n  retryableStatuses: [408, 429, 500, 502, 503, 504],\r\n};\r\n\r\n// Helper function to determine if an error is retryable\r\nconst isRetryableError = (status) => {\r\n  return RETRY_CONFIG.retryableStatuses.includes(status);\r\n};\r\n\r\n// Helper function to wait for a specified delay\r\nconst delay = (ms) => {\r\n  return new Promise(resolve => setTimeout(resolve, ms));\r\n};\r\n\r\n// Enhanced fetch function with retry logic and better error handling\r\nexport async function fetchStrapiAPI(path, params = {}, options = {}) {\r\n  const headers = {\r\n    'Content-Type': 'application/json',\r\n    ...options.headers,\r\n  };\r\n\r\n  if (STRAPI_API_TOKEN) {\r\n    headers['Authorization'] = `Bearer ${STRAPI_API_TOKEN}`;\r\n  }\r\n\r\n  const mergedOptions = {\r\n    ...options,\r\n    headers,\r\n  };\r\n\r\n  const requestPath = path.startsWith('/') ? path : `/${path}`;\r\n  const queryString = new URLSearchParams(params).toString();\r\n  const requestUrl = `${STRAPI_API_URL}/api${requestPath}${queryString ? `?${queryString}` : ''}`;\r\n\r\n  let lastError = null;\r\n\r\n  // Retry logic\r\n  for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {\r\n    try {\r\n      if (isDevelopment() && attempt > 0) {\r\n        console.log(`🔄 Retrying Strapi API request (attempt ${attempt + 1}/${RETRY_CONFIG.maxRetries + 1}): ${requestUrl}`);\r\n      }\r\n\r\n      const response = await fetch(requestUrl, mergedOptions);\r\n\r\n      if (!response.ok) {\r\n        const errorBody = await response.text();\r\n        const error = new StrapiApiError(\r\n          `Strapi API request failed: ${response.status} ${response.statusText}`,\r\n          response.status,\r\n          response.statusText,\r\n          requestUrl,\r\n          errorBody\r\n        );\r\n\r\n        // Log error details\r\n        console.error(\"Strapi API Error:\", {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          url: requestUrl,\r\n          body: errorBody,\r\n          attempt: attempt + 1,\r\n        });\r\n\r\n        // Check if we should retry\r\n        if (attempt < RETRY_CONFIG.maxRetries && isRetryableError(response.status)) {\r\n          lastError = error;\r\n          await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt)); // Exponential backoff\r\n          continue;\r\n        }\r\n\r\n        // Return structured error response\r\n        return {\r\n          data: undefined,\r\n          error: {\r\n            message: getUserFriendlyErrorMessage(response.status),\r\n            code: `STRAPI_${response.status}`,\r\n            status: response.status,\r\n            details: {\r\n              originalMessage: error.message,\r\n              url: requestUrl,\r\n              body: errorBody,\r\n            },\r\n          },\r\n          success: false,\r\n        };\r\n      }\r\n\r\n      // Success - parse and return data\r\n      const data = await response.json();\r\n\r\n      if (isDevelopment()) {\r\n        console.log(`✅ Strapi API success: ${requestUrl}`);\r\n      }\r\n\r\n      return {\r\n        data,\r\n        success: true,\r\n      };\r\n\r\n    } catch (error) {\r\n      const networkError = error;\r\n      lastError = networkError;\r\n\r\n      console.error(\"Network error fetching from Strapi API:\", {\r\n        error: networkError.message,\r\n        url: requestUrl,\r\n        attempt: attempt + 1,\r\n      });\r\n\r\n      // Retry on network errors\r\n      if (attempt < RETRY_CONFIG.maxRetries) {\r\n        await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt));\r\n        continue;\r\n      }\r\n\r\n      // Return structured error response for network errors\r\n      return {\r\n        data: undefined,\r\n        error: {\r\n          message: \"Unable to connect to the content management system. Please check your internet connection and try again.\",\r\n          code: 'STRAPI_NETWORK_ERROR',\r\n          status: 0,\r\n          details: {\r\n            originalMessage: networkError.message,\r\n            url: requestUrl,\r\n          },\r\n        },\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  // This should never be reached, but just in case\r\n  return {\r\n    data: undefined,\r\n    error: {\r\n      message: \"An unexpected error occurred while fetching data.\",\r\n      code: 'STRAPI_UNKNOWN_ERROR',\r\n      status: 0,\r\n      details: {\r\n        originalMessage: lastError?.message || 'Unknown error',\r\n        url: requestUrl,\r\n      },\r\n    },\r\n    success: false,\r\n  };\r\n}\r\n\r\n// Helper function to provide user-friendly error messages\r\nconst getUserFriendlyErrorMessage = (status) => {\r\n  switch (status) {\r\n    case 400:\r\n      return \"The request was invalid. Please try again.\";\r\n    case 401:\r\n      return \"Authentication failed. Please refresh the page and try again.\";\r\n    case 403:\r\n      return \"Access denied. You don't have permission to access this content.\";\r\n    case 404:\r\n      return \"The requested content was not found.\";\r\n    case 408:\r\n      return \"The request timed out. Please try again.\";\r\n    case 429:\r\n      return \"Too many requests. Please wait a moment and try again.\";\r\n    case 500:\r\n      return \"A server error occurred. Please try again later.\";\r\n    case 502:\r\n      return \"The content management system is temporarily unavailable. Please try again later.\";\r\n    case 503:\r\n      return \"The service is temporarily unavailable. Please try again later.\";\r\n    case 504:\r\n      return \"The request timed out. Please try again.\";\r\n    default:\r\n      return \"An unexpected error occurred. Please try again later.\";\r\n  }\r\n};\r\n\r\n// Legacy function for backward compatibility\r\nexport async function fetchStrapiAPILegacy(path, params = {}, options = {}) {\r\n  const result = await fetchStrapiAPI(path, params, options);\r\n\r\n  if (!result.success) {\r\n    throw new Error(result.error?.message || 'Strapi API request failed');\r\n  }\r\n\r\n  return result.data;\r\n}\r\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;AAC5D,MAAM,iBAAiB,6DAA0C;AACjE,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB;AACrD,MAAM,gBAAgB,IAAM,oDAAyB;AAK9C,MAAM,uBAAuB;IAClC,YAAY,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,CAAE;QAChE,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,sBAAsB;AACtB,MAAM,eAAe;IACnB,YAAY;IACZ,YAAY;IACZ,mBAAmB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;AACnD;AAEA,wDAAwD;AACxD,MAAM,mBAAmB,CAAC;IACxB,OAAO,aAAa,iBAAiB,CAAC,QAAQ,CAAC;AACjD;AAEA,gDAAgD;AAChD,MAAM,QAAQ,CAAC;IACb,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,eAAe,eAAe,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAClE,MAAM,UAAU;QACd,gBAAgB;QAChB,GAAG,QAAQ,OAAO;IACpB;IAEA,IAAI,kBAAkB;QACpB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,kBAAkB;IACzD;IAEA,MAAM,gBAAgB;QACpB,GAAG,OAAO;QACV;IACF;IAEA,MAAM,cAAc,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC5D,MAAM,cAAc,IAAI,gBAAgB,QAAQ,QAAQ;IACxD,MAAM,aAAa,GAAG,eAAe,IAAI,EAAE,cAAc,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE/F,IAAI,YAAY;IAEhB,cAAc;IACd,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAU,EAAE,UAAW;QACnE,IAAI;YACF,IAAI,mBAAmB,UAAU,GAAG;gBAClC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,UAAU,GAAG,EAAE,GAAG,EAAE,YAAY;YACrH;YAEA,MAAM,WAAW,MAAM,MAAM,YAAY;YAEzC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,QAAQ,IAAI,eAChB,CAAC,2BAA2B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACtE,SAAS,MAAM,EACf,SAAS,UAAU,EACnB,YACA;gBAGF,oBAAoB;gBACpB,QAAQ,KAAK,CAAC,qBAAqB;oBACjC,QAAQ,SAAS,MAAM;oBACvB,YAAY,SAAS,UAAU;oBAC/B,KAAK;oBACL,MAAM;oBACN,SAAS,UAAU;gBACrB;gBAEA,2BAA2B;gBAC3B,IAAI,UAAU,aAAa,UAAU,IAAI,iBAAiB,SAAS,MAAM,GAAG;oBAC1E,YAAY;oBACZ,MAAM,MAAM,aAAa,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,sBAAsB;oBACnF;gBACF;gBAEA,mCAAmC;gBACnC,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS,4BAA4B,SAAS,MAAM;wBACpD,MAAM,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;wBACjC,QAAQ,SAAS,MAAM;wBACvB,SAAS;4BACP,iBAAiB,MAAM,OAAO;4BAC9B,KAAK;4BACL,MAAM;wBACR;oBACF;oBACA,SAAS;gBACX;YACF;YAEA,kCAAkC;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,iBAAiB;gBACnB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY;YACnD;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe;YACrB,YAAY;YAEZ,QAAQ,KAAK,CAAC,2CAA2C;gBACvD,OAAO,aAAa,OAAO;gBAC3B,KAAK;gBACL,SAAS,UAAU;YACrB;YAEA,0BAA0B;YAC1B,IAAI,UAAU,aAAa,UAAU,EAAE;gBACrC,MAAM,MAAM,aAAa,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG;gBAClD;YACF;YAEA,sDAAsD;YACtD,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,SAAS;wBACP,iBAAiB,aAAa,OAAO;wBACrC,KAAK;oBACP;gBACF;gBACA,SAAS;YACX;QACF;IACF;IAEA,iDAAiD;IACjD,OAAO;QACL,MAAM;QACN,OAAO;YACL,SAAS;YACT,MAAM;YACN,QAAQ;YACR,SAAS;gBACP,iBAAiB,WAAW,WAAW;gBACvC,KAAK;YACP;QACF;QACA,SAAS;IACX;AACF;AAEA,0DAA0D;AAC1D,MAAM,8BAA8B,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,eAAe,qBAAqB,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACxE,MAAM,SAAS,MAAM,eAAe,MAAM,QAAQ;IAElD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;IAC3C;IAEA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/lib/page-manager.ts"], "sourcesContent": ["import { fetchStrapiAPI } from './strapi';\nimport { notFound } from 'next/navigation';\n\n// Types for Strapi page management\nexport interface StrapiPage {\n  id: number;\n  attributes: {\n    title: string;\n    slug: string;\n    meta_title?: string;\n    meta_description?: string;\n    page_type: 'homepage' | 'category_index' | 'product_listing' | 'content_page' | 'landing_page' | 'collection_page';\n    template: 'default' | 'homepage' | 'category-grid' | 'product-list' | 'full-width' | 'sidebar-left' | 'sidebar-right';\n    status: 'draft' | 'published' | 'archived';\n    featured_image?: {\n      data?: {\n        attributes: {\n          url: string;\n          alternativeText?: string;\n        };\n      };\n    };\n    content_blocks?: ContentBlock[];\n    sidebar_content?: ContentBlock[];\n    breadcrumbs?: BreadcrumbItem[];\n    parent_page?: {\n      data?: {\n        attributes: {\n          title: string;\n          slug: string;\n        };\n      };\n    };\n    medusa_category_id?: string;\n    medusa_collection_id?: string;\n    custom_fields?: Record<string, unknown>;\n    sort_order: number;\n    is_menu_item: boolean;\n    menu_label?: string;\n  };\n}\n\nexport interface ContentBlock {\n  __component: string;\n  id: number;\n  [key: string]: unknown;\n}\n\nexport interface BreadcrumbItem {\n  label: string;\n  url: string;\n  isActive?: boolean;\n}\n\nexport interface NavigationMenu {\n  id: number;\n  attributes: {\n    main_menu: MenuItem[];\n    footer_menu: MenuItem[];\n    mobile_menu: MenuItem[];\n    breadcrumb_settings?: Record<string, unknown>;\n  };\n}\n\nexport interface MenuItem {\n  id: number;\n  label: string;\n  url: string;\n  is_external: boolean;\n  target: '_self' | '_blank';\n  icon?: {\n    data?: {\n      attributes: {\n        url: string;\n        alternativeText?: string;\n      };\n    };\n  };\n  description?: string;\n  sort_order: number;\n  is_featured: boolean;\n  submenu?: MenuItem[];\n  page?: {\n    data?: {\n      attributes: {\n        title: string;\n        slug: string;\n      };\n    };\n  };\n}\n\n// Page Manager Class\nexport class PageManager {\n  private static instance: PageManager;\n  private pageCache = new Map<string, StrapiPage>();\n  private navigationCache: NavigationMenu | null = null;\n\n  static getInstance(): PageManager {\n    if (!PageManager.instance) {\n      PageManager.instance = new PageManager();\n    }\n    return PageManager.instance;\n  }\n\n  // Get page by slug\n  async getPageBySlug(slug: string): Promise<StrapiPage | null> {\n    // Check cache first\n    if (this.pageCache.has(slug)) {\n      return this.pageCache.get(slug)!;\n    }\n\n    try {\n      const result = await fetchStrapiAPI('/pages', {\n        'filters[slug][$eq]': slug,\n        'filters[status][$eq]': 'published',\n        populate: 'deep'\n      });\n\n      if (result.success && result.data?.data && result.data.data.length > 0) {\n        const page = result.data.data[0] as StrapiPage;\n        this.pageCache.set(slug, page);\n        return page;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Error fetching page:', error);\n      return null;\n    }\n  }\n\n  // Get homepage\n  async getHomepage(): Promise<StrapiPage | null> {\n    try {\n      const result = await fetchStrapiAPI('/pages', {\n        'filters[page_type][$eq]': 'homepage',\n        'filters[status][$eq]': 'published',\n        populate: 'deep'\n      });\n\n      if (result.success && result.data?.data && result.data.data.length > 0) {\n        return result.data.data[0] as StrapiPage;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Error fetching homepage:', error);\n      return null;\n    }\n  }\n\n  // Get all pages for sitemap generation\n  async getAllPages(): Promise<StrapiPage[]> {\n    try {\n      const result = await fetchStrapiAPI('/pages', {\n        'filters[status][$eq]': 'published',\n        populate: 'deep',\n        'pagination[limit]': 100\n      });\n\n      if (result.success && result.data?.data) {\n        return result.data.data as StrapiPage[];\n      }\n\n      return [];\n    } catch (error) {\n      console.error('Error fetching all pages:', error);\n      return [];\n    }\n  }\n\n  // Get navigation\n  async getNavigation(): Promise<NavigationMenu | null> {\n    if (this.navigationCache) {\n      return this.navigationCache;\n    }\n\n    try {\n      const result = await fetchStrapiAPI('/navigation', {\n        populate: 'deep'\n      });\n\n      if (result.success && result.data?.data) {\n        this.navigationCache = result.data.data as NavigationMenu;\n        return this.navigationCache;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Error fetching navigation:', error);\n      return null;\n    }\n  }\n\n  // Get pages by type\n  async getPagesByType(pageType: string): Promise<StrapiPage[]> {\n    try {\n      const result = await fetchStrapiAPI('/pages', {\n        'filters[page_type][$eq]': pageType,\n        'filters[status][$eq]': 'published',\n        populate: 'deep',\n        sort: 'sort_order:asc'\n      });\n\n      if (result.success && result.data?.data) {\n        return result.data.data as StrapiPage[];\n      }\n\n      return [];\n    } catch (error) {\n      console.error('Error fetching pages by type:', error);\n      return [];\n    }\n  }\n\n  // Generate breadcrumbs\n  generateBreadcrumbs(page: StrapiPage, basePath: string = ''): BreadcrumbItem[] {\n    const breadcrumbs: BreadcrumbItem[] = [\n      { label: 'Home', url: '/' }\n    ];\n\n    // Add parent pages\n    if (page.attributes.parent_page?.data) {\n      const parent = page.attributes.parent_page.data.attributes;\n      breadcrumbs.push({\n        label: parent.title,\n        url: `${basePath}/${parent.slug}`\n      });\n    }\n\n    // Add current page\n    breadcrumbs.push({\n      label: page.attributes.title,\n      url: `${basePath}/${page.attributes.slug}`,\n      isActive: true\n    });\n\n    return breadcrumbs;\n  }\n\n  // Clear cache\n  clearCache(): void {\n    this.pageCache.clear();\n    this.navigationCache = null;\n  }\n}\n\n// Utility functions\nexport const pageManager = PageManager.getInstance();\n\nexport async function getPageData(slug: string): Promise<StrapiPage> {\n  const page = await pageManager.getPageBySlug(slug);\n  \n  if (!page) {\n    notFound();\n  }\n\n  return page;\n}\n\nexport async function generatePageMetadata(page: StrapiPage) {\n  return {\n    title: page.attributes.meta_title || page.attributes.title,\n    description: page.attributes.meta_description || `${page.attributes.title} - Matches Fashion`,\n    openGraph: {\n      title: page.attributes.meta_title || page.attributes.title,\n      description: page.attributes.meta_description || `${page.attributes.title} - Matches Fashion`,\n      images: page.attributes.featured_image?.data ? [\n        {\n          url: page.attributes.featured_image.data.attributes.url,\n          alt: page.attributes.featured_image.data.attributes.alternativeText || page.attributes.title,\n        }\n      ] : [],\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AA4FO,MAAM;IACX,OAAe,SAAsB;IAC7B,YAAY,IAAI,MAA0B;IAC1C,kBAAyC,KAAK;IAEtD,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,mBAAmB;IACnB,MAAM,cAAc,IAAY,EAA8B;QAC5D,oBAAoB;QACpB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC5B;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC5C,sBAAsB;gBACtB,wBAAwB;gBACxB,UAAU;YACZ;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;gBACtE,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM;gBACzB,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA,eAAe;IACf,MAAM,cAA0C;QAC9C,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC5C,2BAA2B;gBAC3B,wBAAwB;gBACxB,UAAU;YACZ;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;gBACtE,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAqC;QACzC,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC5C,wBAAwB;gBACxB,UAAU;gBACV,qBAAqB;YACvB;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM;gBACvC,OAAO,OAAO,IAAI,CAAC,IAAI;YACzB;YAEA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,EAAE;QACX;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgD;QACpD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,OAAO,IAAI,CAAC,eAAe;QAC7B;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;gBACjD,UAAU;YACZ;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM;gBACvC,IAAI,CAAC,eAAe,GAAG,OAAO,IAAI,CAAC,IAAI;gBACvC,OAAO,IAAI,CAAC,eAAe;YAC7B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,eAAe,QAAgB,EAAyB;QAC5D,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAC5C,2BAA2B;gBAC3B,wBAAwB;gBACxB,UAAU;gBACV,MAAM;YACR;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM;gBACvC,OAAO,OAAO,IAAI,CAAC,IAAI;YACzB;YAEA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA,uBAAuB;IACvB,oBAAoB,IAAgB,EAAE,WAAmB,EAAE,EAAoB;QAC7E,MAAM,cAAgC;YACpC;gBAAE,OAAO;gBAAQ,KAAK;YAAI;SAC3B;QAED,mBAAmB;QACnB,IAAI,KAAK,UAAU,CAAC,WAAW,EAAE,MAAM;YACrC,MAAM,SAAS,KAAK,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU;YAC1D,YAAY,IAAI,CAAC;gBACf,OAAO,OAAO,KAAK;gBACnB,KAAK,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE;YACnC;QACF;QAEA,mBAAmB;QACnB,YAAY,IAAI,CAAC;YACf,OAAO,KAAK,UAAU,CAAC,KAAK;YAC5B,KAAK,GAAG,SAAS,CAAC,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE;YAC1C,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,cAAc;IACd,aAAmB;QACjB,IAAI,CAAC,SAAS,CAAC,KAAK;QACpB,IAAI,CAAC,eAAe,GAAG;IACzB;AACF;AAGO,MAAM,cAAc,YAAY,WAAW;AAE3C,eAAe,YAAY,IAAY;IAC5C,MAAM,OAAO,MAAM,YAAY,aAAa,CAAC;IAE7C,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;AACT;AAEO,eAAe,qBAAqB,IAAgB;IACzD,OAAO;QACL,OAAO,KAAK,UAAU,CAAC,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK;QAC1D,aAAa,KAAK,UAAU,CAAC,gBAAgB,IAAI,GAAG,KAAK,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAC7F,WAAW;YACT,OAAO,KAAK,UAAU,CAAC,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK;YAC1D,aAAa,KAAK,UAAU,CAAC,gBAAgB,IAAI,GAAG,KAAK,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAC7F,QAAQ,KAAK,UAAU,CAAC,cAAc,EAAE,OAAO;gBAC7C;oBACE,KAAK,KAAK,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;oBACvD,KAAK,KAAK,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,KAAK,UAAU,CAAC,KAAK;gBAC9F;aACD,GAAG,EAAE;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ContentBlockRenderer.tsx"], "sourcesContent": ["import React from 'react';\nimport { ContentBlock } from '../../lib/page-manager';\nimport dynamic from 'next/dynamic';\n\n// Dynamically import content block components\nconst HeroSection = dynamic(() => import('./blocks/HeroSection'));\nconst ProductGrid = dynamic(() => import('./blocks/ProductGrid'));\nconst ContentBlock = dynamic(() => import('./blocks/ContentBlock'));\n\n// Import placeholder components\nconst ImageGallery = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.ImageGallery })));\nconst CallToAction = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CallToAction })));\nconst FeaturedProducts = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.FeaturedProducts })));\nconst CategoryShowcase = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CategoryShowcase })));\nconst Testimonials = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.Testimonials })));\nconst NewsletterSignup = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.NewsletterSignup })));\nconst CategoryMenu = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CategoryMenu })));\nconst FilterWidget = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.FilterWidget })));\n\ninterface ContentBlockRendererProps {\n  block: ContentBlock;\n  context: 'main' | 'sidebar';\n  pageType?: string;\n  medusaCategoryId?: string;\n  medusaCollectionId?: string;\n}\n\nconst ContentBlockRenderer: React.FC<ContentBlockRendererProps> = ({\n  block,\n  context,\n  pageType,\n  medusaCategoryId,\n  medusaCollectionId,\n}) => {\n  const { __component } = block;\n\n  // Common props to pass to all components\n  const commonProps = {\n    data: block,\n    context,\n    pageType,\n    medusaCategoryId,\n    medusaCollectionId,\n  };\n\n  // Render the appropriate component based on the component type\n  const renderComponent = () => {\n    switch (__component) {\n      case 'blocks.hero-section':\n        return <HeroSection {...commonProps} />;\n      \n      case 'blocks.product-grid':\n        return <ProductGrid {...commonProps} />;\n      \n      case 'blocks.content-block':\n        return <ContentBlock {...commonProps} />;\n      \n      case 'blocks.image-gallery':\n        return <ImageGallery {...commonProps} />;\n      \n      case 'blocks.call-to-action':\n        return <CallToAction {...commonProps} />;\n      \n      case 'blocks.featured-products':\n        return <FeaturedProducts {...commonProps} />;\n      \n      case 'blocks.category-showcase':\n        return <CategoryShowcase {...commonProps} />;\n      \n      case 'blocks.testimonials':\n        return <Testimonials {...commonProps} />;\n      \n      case 'blocks.newsletter-signup':\n        return <NewsletterSignup {...commonProps} />;\n      \n      case 'blocks.category-menu':\n        return <CategoryMenu {...commonProps} />;\n      \n      case 'blocks.filter-widget':\n        return <FilterWidget {...commonProps} />;\n      \n      default:\n        // Fallback for unknown components\n        if (process.env.NODE_ENV === 'development') {\n          return (\n            <div className=\"p-4 border-2 border-dashed border-yellow-400 bg-yellow-50 rounded\">\n              <p className=\"text-yellow-800 font-medium\">\n                Unknown component: {__component}\n              </p>\n              <pre className=\"mt-2 text-xs text-yellow-700 overflow-auto\">\n                {JSON.stringify(block, null, 2)}\n              </pre>\n            </div>\n          );\n        }\n        return null;\n    }\n  };\n\n  return (\n    <div \n      className={`content-block content-block--${__component.replace('blocks.', '').replace('-', '_')}`}\n      data-component={__component}\n      data-context={context}\n    >\n      {renderComponent()}\n    </div>\n  );\n};\n\nexport default ContentBlockRenderer;\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;AAEA,8CAA8C;AAC9C,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAC5B,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAC5B,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAE7B,gCAAgC;AAChC,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;;AACtG,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;;AACtG,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,gBAAgB;QAAC,CAAC;;;;;;;AAC9G,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,gBAAgB;QAAC,CAAC;;;;;;;AAC9G,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;;AACtG,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,gBAAgB;QAAC,CAAC;;;;;;;AAC9G,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;;AACtG,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,sJAAyB,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,YAAY;QAAC,CAAC;;;;;;;AAUtG,MAAM,uBAA4D,CAAC,EACjE,KAAK,EACL,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EACnB;IACC,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,yCAAyC;IACzC,MAAM,cAAc;QAClB,MAAM;QACN;QACA;QACA;QACA;IACF;IAEA,+DAA+D;IAC/D,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAa,GAAG,WAAW;;;;;;YAErC,KAAK;gBACH,qBAAO,8OAAC;oBAAa,GAAG,WAAW;;;;;;YAErC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC,KAAK;gBACH,qBAAO,8OAAC;oBAAkB,GAAG,WAAW;;;;;;YAE1C,KAAK;gBACH,qBAAO,8OAAC;oBAAkB,GAAG,WAAW;;;;;;YAE1C,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC,KAAK;gBACH,qBAAO,8OAAC;oBAAkB,GAAG,WAAW;;;;;;YAE1C,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,GAAG,WAAW;;;;;;YAEtC;gBACE,kCAAkC;gBAClC,wCAA4C;oBAC1C,qBACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAA8B;oCACrB;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,OAAO,MAAM;;;;;;;;;;;;gBAIrC;;QAEJ;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,6BAA6B,EAAE,YAAY,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,KAAK,MAAM;QACjG,kBAAgB;QAChB,gBAAc;kBAEb;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/Breadcrumbs.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { BreadcrumbItem } from '../../lib/page-manager';\n\ninterface BreadcrumbsProps {\n  items: BreadcrumbItem[];\n  className?: string;\n}\n\nconst Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, className = '' }) => {\n  if (!items || items.length <= 1) {\n    return null;\n  }\n\n  return (\n    <nav \n      className={`flex items-center space-x-2 text-sm text-gray-600 mb-6 ${className}`}\n      aria-label=\"Breadcrumb\"\n    >\n      <ol className=\"flex items-center space-x-2\">\n        {items.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 mx-2 text-gray-400\" />\n            )}\n            \n            {item.isActive ? (\n              <span className=\"text-gray-900 font-medium\" aria-current=\"page\">\n                {index === 0 && <Home className=\"w-4 h-4 inline mr-1\" />}\n                {item.label}\n              </span>\n            ) : (\n              <Link \n                href={item.url}\n                className=\"hover:text-gray-900 transition-colors duration-200\"\n              >\n                {index === 0 && <Home className=\"w-4 h-4 inline mr-1\" />}\n                {item.label}\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  );\n};\n\nexport default Breadcrumbs;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAQA,MAAM,cAA0C,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACxE,IAAI,CAAC,SAAS,MAAM,MAAM,IAAI,GAAG;QAC/B,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,uDAAuD,EAAE,WAAW;QAChF,cAAW;kBAEX,cAAA,8OAAC;YAAG,WAAU;sBACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,QAAQ,iBACZ,8OAAC;4BAAK,WAAU;4BAA4B,gBAAa;;gCACtD,UAAU,mBAAK,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAC/B,KAAK,KAAK;;;;;;iDAGb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,GAAG;4BACd,WAAU;;gCAET,UAAU,mBAAK,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAC/B,KAAK,KAAK;;;;;;;;mBAhBR;;;;;;;;;;;;;;;AAwBnB;uCAEe", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ui/Loading.tsx"], "sourcesContent": ["import React from 'react';\nimport { clsx } from 'clsx';\nimport { Loader2, ShoppingBag, Package, Truck } from 'lucide-react';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  variant?: 'spinner' | 'dots' | 'bars' | 'pulse';\n  color?: 'primary' | 'secondary' | 'white' | 'gray';\n  text?: string;\n  fullScreen?: boolean;\n  overlay?: boolean;\n  className?: string;\n}\n\nexport const Loading: React.FC<LoadingProps> = ({\n  size = 'md',\n  variant = 'spinner',\n  color = 'primary',\n  text,\n  fullScreen = false,\n  overlay = false,\n  className,\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n    xl: 'w-12 h-12',\n  };\n\n  const colorClasses = {\n    primary: 'text-black',\n    secondary: 'text-gray-600',\n    white: 'text-white',\n    gray: 'text-gray-400',\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl',\n  };\n\n  const renderSpinner = () => (\n    <Loader2\n      className={clsx(\n        'animate-spin',\n        sizeClasses[size],\n        colorClasses[color]\n      )}\n    />\n  );\n\n  const renderDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((i) => (\n        <div\n          key={i}\n          className={clsx(\n            'rounded-full animate-pulse',\n            size === 'sm' && 'w-1 h-1',\n            size === 'md' && 'w-2 h-2',\n            size === 'lg' && 'w-3 h-3',\n            size === 'xl' && 'w-4 h-4',\n            color === 'primary' && 'bg-black',\n            color === 'secondary' && 'bg-gray-600',\n            color === 'white' && 'bg-white',\n            color === 'gray' && 'bg-gray-400'\n          )}\n          style={{\n            animationDelay: `${i * 0.2}s`,\n            animationDuration: '1.4s',\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const renderBars = () => (\n    <div className=\"flex items-end space-x-1\">\n      {[0, 1, 2, 3].map((i) => (\n        <div\n          key={i}\n          className={clsx(\n            'animate-pulse',\n            size === 'sm' && 'w-1',\n            size === 'md' && 'w-1.5',\n            size === 'lg' && 'w-2',\n            size === 'xl' && 'w-3',\n            color === 'primary' && 'bg-black',\n            color === 'secondary' && 'bg-gray-600',\n            color === 'white' && 'bg-white',\n            color === 'gray' && 'bg-gray-400'\n          )}\n          style={{\n            height: `${12 + (i * 4)}px`,\n            animationDelay: `${i * 0.1}s`,\n            animationDuration: '1s',\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const renderPulse = () => (\n    <div\n      className={clsx(\n        'rounded-full animate-pulse',\n        sizeClasses[size],\n        color === 'primary' && 'bg-black',\n        color === 'secondary' && 'bg-gray-600',\n        color === 'white' && 'bg-white',\n        color === 'gray' && 'bg-gray-400'\n      )}\n    />\n  );\n\n  const renderLoader = () => {\n    switch (variant) {\n      case 'dots':\n        return renderDots();\n      case 'bars':\n        return renderBars();\n      case 'pulse':\n        return renderPulse();\n      default:\n        return renderSpinner();\n    }\n  };\n\n  const content = (\n    <div\n      className={clsx(\n        'flex flex-col items-center justify-center space-y-3',\n        fullScreen && 'min-h-screen',\n        className\n      )}\n    >\n      {renderLoader()}\n      {text && (\n        <p\n          className={clsx(\n            'font-medium',\n            textSizeClasses[size],\n            colorClasses[color]\n          )}\n        >\n          {text}\n        </p>\n      )}\n    </div>\n  );\n\n  if (overlay) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg p-8\">\n          {content}\n        </div>\n      </div>\n    );\n  }\n\n  return content;\n};\n\n// Specialized loading components\nexport const PageLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (\n  <Loading fullScreen size=\"lg\" text={text} />\n);\n\nexport const ButtonLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'sm' }) => (\n  <Loading size={size} variant=\"spinner\" />\n);\n\nexport const InlineLoading: React.FC<{ text?: string; size?: 'sm' | 'md' }> = ({\n  text,\n  size = 'sm',\n}) => (\n  <div className=\"flex items-center space-x-2\">\n    <Loading size={size} variant=\"spinner\" />\n    {text && <span className=\"text-sm text-gray-600\">{text}</span>}\n  </div>\n);\n\nexport const OverlayLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (\n  <Loading overlay size=\"lg\" text={text} />\n);\n\n// Context-specific loading components\nexport const ShoppingLoading: React.FC<{ text?: string }> = ({\n  text = 'Loading products...',\n}) => (\n  <div className=\"flex flex-col items-center justify-center space-y-4 py-12\">\n    <ShoppingBag className=\"w-12 h-12 text-gray-400 animate-pulse\" />\n    <p className=\"text-gray-600 font-medium\">{text}</p>\n  </div>\n);\n\nexport const CheckoutLoading: React.FC<{ text?: string }> = ({\n  text = 'Processing your order...',\n}) => (\n  <div className=\"flex flex-col items-center justify-center space-y-4 py-12\">\n    <Package className=\"w-12 h-12 text-gray-400 animate-pulse\" />\n    <p className=\"text-gray-600 font-medium\">{text}</p>\n  </div>\n);\n\nexport const ShippingLoading: React.FC<{ text?: string }> = ({\n  text = 'Calculating shipping...',\n}) => (\n  <div className=\"flex flex-col items-center justify-center space-y-4 py-8\">\n    <Truck className=\"w-8 h-8 text-gray-400 animate-pulse\" />\n    <p className=\"text-gray-600 text-sm\">{text}</p>\n  </div>\n);\n\n// Loading states for different sections\nexport const ProductGridLoading: React.FC = () => (\n  <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n    {Array.from({ length: 8 }).map((_, index) => (\n      <div key={index} className=\"space-y-3\">\n        <div className=\"aspect-[3/4] bg-gray-200 animate-pulse rounded\" />\n        <div className=\"space-y-2\">\n          <div className=\"h-4 bg-gray-200 animate-pulse rounded\" />\n          <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n          <div className=\"h-5 bg-gray-200 animate-pulse rounded w-1/2\" />\n        </div>\n      </div>\n    ))}\n  </div>\n);\n\nexport const ProductDetailLoading: React.FC = () => (\n  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n    <div className=\"space-y-4\">\n      <div className=\"aspect-square bg-gray-200 animate-pulse rounded\" />\n      <div className=\"grid grid-cols-4 gap-2\">\n        {Array.from({ length: 4 }).map((_, index) => (\n          <div key={index} className=\"aspect-square bg-gray-200 animate-pulse rounded\" />\n        ))}\n      </div>\n    </div>\n    <div className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <div className=\"h-4 bg-gray-200 animate-pulse rounded w-1/3\" />\n        <div className=\"h-8 bg-gray-200 animate-pulse rounded w-2/3\" />\n        <div className=\"h-6 bg-gray-200 animate-pulse rounded w-1/4\" />\n      </div>\n      <div className=\"space-y-4\">\n        <div className=\"h-5 bg-gray-200 animate-pulse rounded w-1/5\" />\n        <div className=\"grid grid-cols-4 gap-2\">\n          {Array.from({ length: 4 }).map((_, index) => (\n            <div key={index} className=\"h-10 bg-gray-200 animate-pulse rounded\" />\n          ))}\n        </div>\n      </div>\n      <div className=\"space-y-2\">\n        <div className=\"h-12 bg-gray-200 animate-pulse rounded\" />\n        <div className=\"h-10 bg-gray-200 animate-pulse rounded\" />\n      </div>\n    </div>\n  </div>\n);\n\nexport const CarouselLoading: React.FC<{ itemCount?: number }> = ({ itemCount = 4 }) => (\n  <div className=\"flex space-x-4 overflow-hidden\">\n    {Array.from({ length: itemCount }).map((_, index) => (\n      <div key={index} className=\"flex-shrink-0 w-64 space-y-3\">\n        <div className=\"aspect-[3/4] bg-gray-200 animate-pulse rounded\" />\n        <div className=\"space-y-2\">\n          <div className=\"h-4 bg-gray-200 animate-pulse rounded\" />\n          <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n          <div className=\"h-5 bg-gray-200 animate-pulse rounded w-1/2\" />\n        </div>\n      </div>\n    ))}\n  </div>\n);\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAYO,MAAM,UAAkC,CAAC,EAC9C,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,QAAQ,SAAS,EACjB,IAAI,EACJ,aAAa,KAAK,EAClB,UAAU,KAAK,EACf,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB,kBACpB,8OAAC,iNAAA,CAAA,UAAO;YACN,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,gBACA,WAAW,CAAC,KAAK,EACjB,YAAY,CAAC,MAAM;;;;;;IAKzB,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oBAEC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,8BACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,UAAU,aAAa,YACvB,UAAU,eAAe,eACzB,UAAU,WAAW,YACrB,UAAU,UAAU;oBAEtB,OAAO;wBACL,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;wBAC7B,mBAAmB;oBACrB;mBAfK;;;;;;;;;;IAqBb,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;oBAEC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,iBACA,SAAS,QAAQ,OACjB,SAAS,QAAQ,SACjB,SAAS,QAAQ,OACjB,SAAS,QAAQ,OACjB,UAAU,aAAa,YACvB,UAAU,eAAe,eACzB,UAAU,WAAW,YACrB,UAAU,UAAU;oBAEtB,OAAO;wBACL,QAAQ,GAAG,KAAM,IAAI,EAAG,EAAE,CAAC;wBAC3B,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;wBAC7B,mBAAmB;oBACrB;mBAhBK;;;;;;;;;;IAsBb,MAAM,cAAc,kBAClB,8OAAC;YACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,8BACA,WAAW,CAAC,KAAK,EACjB,UAAU,aAAa,YACvB,UAAU,eAAe,eACzB,UAAU,WAAW,YACrB,UAAU,UAAU;;;;;;IAK1B,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBACJ,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,uDACA,cAAc,gBACd;;YAGD;YACA,sBACC,8OAAC;gBACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,eACA,eAAe,CAAC,KAAK,EACrB,YAAY,CAAC,MAAM;0BAGpB;;;;;;;;;;;;IAMT,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;IAIT;IAEA,OAAO;AACT;AAGO,MAAM,cAA2C,CAAC,EAAE,OAAO,YAAY,EAAE,iBAC9E,8OAAC;QAAQ,UAAU;QAAC,MAAK;QAAK,MAAM;;;;;;AAG/B,MAAM,gBAAyD,CAAC,EAAE,OAAO,IAAI,EAAE,iBACpF,8OAAC;QAAQ,MAAM;QAAM,SAAQ;;;;;;AAGxB,MAAM,gBAAiE,CAAC,EAC7E,IAAI,EACJ,OAAO,IAAI,EACZ,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAQ,MAAM;gBAAM,SAAQ;;;;;;YAC5B,sBAAQ,8OAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;AAI/C,MAAM,iBAA8C,CAAC,EAAE,OAAO,YAAY,EAAE,iBACjF,8OAAC;QAAQ,OAAO;QAAC,MAAK;QAAK,MAAM;;;;;;AAI5B,MAAM,kBAA+C,CAAC,EAC3D,OAAO,qBAAqB,EAC7B,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIvC,MAAM,kBAA+C,CAAC,EAC3D,OAAO,0BAA0B,EAClC,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIvC,MAAM,kBAA+C,CAAC,EAC3D,OAAO,yBAAyB,EACjC,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;0BACjB,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAKnC,MAAM,qBAA+B,kBAC1C,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eALT;;;;;;;;;;AAYT,MAAM,uBAAiC,kBAC5C,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;+BAAjB;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wCAAgB,WAAU;uCAAjB;;;;;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMhB,MAAM,kBAAoD,CAAC,EAAE,YAAY,CAAC,EAAE,iBACjF,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAU,GAAG,GAAG,CAAC,CAAC,GAAG,sBACzC,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eALT;;;;;;;;;;uCAYD", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/PageTemplate.tsx"], "sourcesContent": ["import React, { Suspense } from 'react';\nimport { StrapiPage, BreadcrumbItem } from '../../../lib/page-manager';\nimport { Loading } from '../ui/Loading';\n\ninterface PageTemplateProps {\n  page: StrapiPage;\n  breadcrumbs: BreadcrumbItem[];\n  templateComponent: Promise<{ default: React.ComponentType<any> }>;\n  children: React.ReactNode;\n}\n\nconst PageTemplate: React.FC<PageTemplateProps> = ({ \n  page, \n  breadcrumbs, \n  templateComponent, \n  children \n}) => {\n  const { attributes } = page;\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Page-specific template wrapper */}\n      <Suspense fallback={<Loading size=\"lg\" text=\"Loading page template...\" />}>\n        <TemplateWrapper templateComponent={templateComponent}>\n          {children}\n        </TemplateWrapper>\n      </Suspense>\n\n      {/* Schema.org structured data */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify({\n            '@context': 'https://schema.org',\n            '@type': 'WebPage',\n            name: attributes.title,\n            description: attributes.meta_description,\n            url: typeof window !== 'undefined' ? window.location.href : '',\n            breadcrumb: {\n              '@type': 'BreadcrumbList',\n              itemListElement: breadcrumbs.map((item, index) => ({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.label,\n                item: item.url\n              }))\n            }\n          })\n        }}\n      />\n    </div>\n  );\n};\n\n// Helper component to handle dynamic template loading\nconst TemplateWrapper: React.FC<{\n  templateComponent: Promise<{ default: React.ComponentType<any> }>;\n  children: React.ReactNode;\n}> = ({ templateComponent, children }) => {\n  const [TemplateComponent, setTemplateComponent] = React.useState<React.ComponentType<any> | null>(null);\n\n  React.useEffect(() => {\n    templateComponent.then(module => {\n      setTemplateComponent(() => module.default);\n    }).catch(() => {\n      // Fallback to default template\n      setTemplateComponent(() => ({ children }: { children: React.ReactNode }) => (\n        <div className=\"max-w-7xl mx-auto px-4 py-8\">\n          {children}\n        </div>\n      ));\n    });\n  }, [templateComponent]);\n\n  if (!TemplateComponent) {\n    return <Loading size=\"lg\" text=\"Loading template...\" />;\n  }\n\n  return <TemplateComponent>{children}</TemplateComponent>;\n};\n\nexport default PageTemplate;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AASA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACT;IACC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC,mIAAA,CAAA,UAAO;oBAAC,MAAK;oBAAK,MAAK;;;;;;0BAC1C,cAAA,8OAAC;oBAAgB,mBAAmB;8BACjC;;;;;;;;;;;0BAKL,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;wBACrB,YAAY;wBACZ,SAAS;wBACT,MAAM,WAAW,KAAK;wBACtB,aAAa,WAAW,gBAAgB;wBACxC,KAAK,6EAAuD;wBAC5D,YAAY;4BACV,SAAS;4BACT,iBAAiB,YAAY,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oCACjD,SAAS;oCACT,UAAU,QAAQ;oCAClB,MAAM,KAAK,KAAK;oCAChB,MAAM,KAAK,GAAG;gCAChB,CAAC;wBACH;oBACF;gBACF;;;;;;;;;;;;AAIR;AAEA,sDAAsD;AACtD,MAAM,kBAGD,CAAC,EAAE,iBAAiB,EAAE,QAAQ,EAAE;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAkC;IAElG,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,kBAAkB,IAAI,CAAC,CAAA;YACrB,qBAAqB,IAAM,OAAO,OAAO;QAC3C,GAAG,KAAK,CAAC;YACP,+BAA+B;YAC/B,qBAAqB,IAAM,CAAC,EAAE,QAAQ,EAAiC,iBACrE,8OAAC;wBAAI,WAAU;kCACZ;;;;;;QAGP;IACF,GAAG;QAAC;KAAkB;IAEtB,IAAI,CAAC,mBAAmB;QACtB,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;;;;;;IACjC;IAEA,qBAAO,8OAAC;kBAAmB;;;;;;AAC7B;uCAEe", "debugId": null}}, {"offset": {"line": 1401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/DynamicPageRenderer.tsx"], "sourcesContent": ["import React from 'react';\nimport { StrapiPage, pageManager } from '../../lib/page-manager';\nimport ContentBlockRenderer from './ContentBlockRenderer';\nimport Breadcrumbs from './Breadcrumbs';\nimport PageTemplate from './templates/PageTemplate';\n\ninterface DynamicPageRendererProps {\n  page: StrapiPage;\n}\n\nconst DynamicPageRenderer: React.FC<DynamicPageRendererProps> = ({ page }) => {\n  const { attributes } = page;\n\n  // Generate breadcrumbs\n  const breadcrumbs = pageManager.generateBreadcrumbs(page);\n\n  // Determine template component based on page template\n  const getTemplateComponent = () => {\n    switch (attributes.template) {\n      case 'homepage':\n        return import('./templates/HomepageTemplate');\n      case 'category-grid':\n        return import('./templates/CategoryGridTemplate');\n      case 'product-list':\n        return import('./templates/ProductListTemplate');\n      case 'full-width':\n        return import('./templates/FullWidthTemplate');\n      case 'sidebar-left':\n        return import('./templates/SidebarLeftTemplate');\n      case 'sidebar-right':\n        return import('./templates/SidebarRightTemplate');\n      default:\n        return import('./templates/DefaultTemplate');\n    }\n  };\n\n  return (\n    <PageTemplate\n      page={page}\n      breadcrumbs={breadcrumbs}\n      templateComponent={getTemplateComponent()}\n    >\n      {/* Page Header */}\n      {attributes.featured_image?.data && (\n        <div className=\"relative w-full h-64 md:h-96 mb-8\">\n          <img\n            src={attributes.featured_image.data.attributes.url}\n            alt={attributes.featured_image.data.attributes.alternativeText || attributes.title}\n            className=\"w-full h-full object-cover\"\n          />\n          <div className=\"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center\">\n            <div className=\"text-center text-white\">\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\n                {attributes.title}\n              </h1>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Breadcrumbs */}\n      <Breadcrumbs items={breadcrumbs} />\n\n      {/* Page Title (if no featured image) */}\n      {!attributes.featured_image?.data && (\n        <div className=\"mb-8\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-center\">\n            {attributes.title}\n          </h1>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8\">\n        {/* Sidebar Left */}\n        {(attributes.template === 'sidebar-left' || attributes.template === 'sidebar-right') && \n         attributes.sidebar_content && attributes.sidebar_content.length > 0 && (\n          <aside className={`lg:col-span-3 ${attributes.template === 'sidebar-right' ? 'lg:order-2' : ''}`}>\n            <div className=\"space-y-6\">\n              {attributes.sidebar_content.map((block, index) => (\n                <ContentBlockRenderer\n                  key={`sidebar-${block.id || index}`}\n                  block={block}\n                  context=\"sidebar\"\n                />\n              ))}\n            </div>\n          </aside>\n        )}\n\n        {/* Main Content Area */}\n        <main className={`\n          ${attributes.template === 'sidebar-left' || attributes.template === 'sidebar-right' \n            ? 'lg:col-span-9' \n            : 'lg:col-span-12'\n          }\n        `}>\n          {/* Content Blocks */}\n          {attributes.content_blocks && attributes.content_blocks.length > 0 ? (\n            <div className=\"space-y-8\">\n              {attributes.content_blocks.map((block, index) => (\n                <ContentBlockRenderer\n                  key={`content-${block.id || index}`}\n                  block={block}\n                  context=\"main\"\n                  pageType={attributes.page_type}\n                  medusaCategoryId={attributes.medusa_category_id}\n                  medusaCollectionId={attributes.medusa_collection_id}\n                />\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-600\">No content blocks configured for this page.</p>\n            </div>\n          )}\n        </main>\n      </div>\n\n      {/* Custom Fields Data (for developers) */}\n      {process.env.NODE_ENV === 'development' && attributes.custom_fields && (\n        <div className=\"mt-12 p-4 bg-gray-100 rounded\">\n          <h3 className=\"text-sm font-medium text-gray-700 mb-2\">Custom Fields (Dev Only)</h3>\n          <pre className=\"text-xs text-gray-600 overflow-auto\">\n            {JSON.stringify(attributes.custom_fields, null, 2)}\n          </pre>\n        </div>\n      )}\n    </PageTemplate>\n  );\n};\n\nexport default DynamicPageRenderer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAMA,MAAM,sBAA0D,CAAC,EAAE,IAAI,EAAE;IACvE,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,uBAAuB;IACvB,MAAM,cAAc,sHAAA,CAAA,cAAW,CAAC,mBAAmB,CAAC;IAEpD,sDAAsD;IACtD,MAAM,uBAAuB;QAC3B,OAAQ,WAAW,QAAQ;YACzB,KAAK;gBACH;YACF,KAAK;gBACH;YACF,KAAK;gBACH;YACF,KAAK;gBACH;YACF,KAAK;gBACH;YACF,KAAK;gBACH;YACF;gBACE;QACJ;IACF;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAY;QACX,MAAM;QACN,aAAa;QACb,mBAAmB;;YAGlB,WAAW,cAAc,EAAE,sBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK,WAAW,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;wBAClD,KAAK,WAAW,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,WAAW,KAAK;wBAClF,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,8OAAC,iIAAA,CAAA,UAAW;gBAAC,OAAO;;;;;;YAGnB,CAAC,WAAW,cAAc,EAAE,sBAC3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,WAAW,KAAK;;;;;;;;;;;0BAMvB,8OAAC;gBAAI,WAAU;;oBAEZ,CAAC,WAAW,QAAQ,KAAK,kBAAkB,WAAW,QAAQ,KAAK,eAAe,KAClF,WAAW,eAAe,IAAI,WAAW,eAAe,CAAC,MAAM,GAAG,mBACjE,8OAAC;wBAAM,WAAW,CAAC,cAAc,EAAE,WAAW,QAAQ,KAAK,kBAAkB,eAAe,IAAI;kCAC9F,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtC,8OAAC,0IAAA,CAAA,UAAoB;oCAEnB,OAAO;oCACP,SAAQ;mCAFH,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,OAAO;;;;;;;;;;;;;;;kCAU7C,8OAAC;wBAAK,WAAW,CAAC;UAChB,EAAE,WAAW,QAAQ,KAAK,kBAAkB,WAAW,QAAQ,KAAK,kBAChE,kBACA,iBACH;QACH,CAAC;kCAEE,WAAW,cAAc,IAAI,WAAW,cAAc,CAAC,MAAM,GAAG,kBAC/D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,8OAAC,0IAAA,CAAA,UAAoB;oCAEnB,OAAO;oCACP,SAAQ;oCACR,UAAU,WAAW,SAAS;oCAC9B,kBAAkB,WAAW,kBAAkB;oCAC/C,oBAAoB,WAAW,oBAAoB;mCAL9C,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,OAAO;;;;;;;;;iDAUzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;YAOpC,oDAAyB,iBAAiB,WAAW,aAAa,kBACjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,WAAW,aAAa,EAAE,MAAM;;;;;;;;;;;;;;;;;;AAM5D;uCAEe", "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/page.tsx"], "sourcesContent": ["import { pageManager } from \"../../lib/page-manager\";\r\nimport DynamicPageRenderer from \"@/components/DynamicPageRenderer\";\r\nimport { notFound } from 'next/navigation';\r\n\r\nexport default async function Home() {\r\n  try {\r\n    // Try to get homepage from Strapi page management system\r\n    const homepage = await pageManager.getHomepage();\r\n\r\n    if (homepage) {\r\n      // Use the new dynamic page system\r\n      return <DynamicPageRenderer page={homepage} />;\r\n    }\r\n\r\n    // If no homepage found in page management, show fallback\r\n    return (\r\n      <div className=\"min-h-screen bg-white flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-4xl font-bold mb-4\">Welcome to Matches Fashion</h1>\r\n          <p className=\"text-gray-600 mb-8\">\r\n            The Strapi page management system is not configured yet.\r\n          </p>\r\n          <div className=\"space-y-2 text-sm text-gray-500\">\r\n            <p>To set up dynamic page management:</p>\r\n            <p>1. Set up Strapi with the provided content types</p>\r\n            <p>2. Create a homepage in Strapi with page_type: &quot;homepage&quot;</p>\r\n            <p>3. See STRAPI-PAGE-MANAGEMENT-SETUP.md for details</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error loading homepage:\", error);\r\n\r\n    // Show fallback instead of not found\r\n    return (\r\n      <div className=\"min-h-screen bg-white flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-4xl font-bold mb-4\">Matches Fashion</h1>\r\n          <p className=\"text-gray-600 mb-4\">\r\n            Unable to load page content. Please check your Strapi configuration.\r\n          </p>\r\n          <p className=\"text-sm text-gray-500\">\r\n            Error: {error instanceof Error ? error.message : 'Unknown error'}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGe,eAAe;IAC5B,IAAI;QACF,yDAAyD;QACzD,MAAM,WAAW,MAAM,sHAAA,CAAA,cAAW,CAAC,WAAW;QAE9C,IAAI,UAAU;YACZ,kCAAkC;YAClC,qBAAO,8OAAC,yIAAA,CAAA,UAAmB;gBAAC,MAAM;;;;;;QACpC;QAEA,yDAAyD;QACzD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,qCAAqC;QACrC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC3B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;IAK3D;AACF", "debugId": null}}]}