(()=>{var e={};e.id=698,e.ids=[698],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5417:(e,s,r)=>{Promise.resolve().then(r.bind(r,60414))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},60414:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\account\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\register\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67832:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var t=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let u={children:["",{children:["account",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60414)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\register\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\register\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/register/page",pathname:"/account/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},70569:(e,s,r)=>{Promise.resolve().then(r.bind(r,81708))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81708:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(60687),a=r(43210),o=r(16189),n=r(85814),i=r.n(n),l=r(18780);function u(){let[e,s]=(0,a.useState)(""),[r,n]=(0,a.useState)(""),{register:u,user:c}=(0,l.A)(),d=(0,o.useRouter)();return c&&d.replace("/account"),(0,t.jsxs)("div",{className:"max-w-md mx-auto py-10",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Create Account"}),(0,t.jsxs)("form",{onSubmit:s=>{s.preventDefault(),u(e,r),d.replace("/account")},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block mb-1",children:"Email"}),(0,t.jsx)("input",{type:"email",value:e,onChange:e=>s(e.target.value),className:"w-full border border-gray-300 p-2",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block mb-1",children:"Password"}),(0,t.jsx)("input",{type:"password",value:r,onChange:e=>n(e.target.value),className:"w-full border border-gray-300 p-2",required:!0})]}),(0,t.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Register"})]}),(0,t.jsxs)("p",{className:"mt-4 text-sm",children:["Already have an account?"," ",(0,t.jsx)(i(),{href:"/account/login",className:"underline",children:"Sign In"})]})]})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,20,137],()=>r(67832));module.exports=t})();