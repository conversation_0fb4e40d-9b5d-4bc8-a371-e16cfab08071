import React from 'react';
import Link from 'next/link';

interface HeroSectionProps {
  data: {
    title?: string;
    subtitle?: string;
    description?: string;
    background_image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    cta_text?: string;
    cta_link?: string;
    overlay_opacity?: number;
    text_color?: 'white' | 'black';
    text_alignment?: 'left' | 'center' | 'right';
  };
  context: 'main' | 'sidebar';
}

const HeroSection: React.FC<HeroSectionProps> = ({ data, context }) => {
  const {
    title,
    subtitle,
    description,
    background_image,
    cta_text,
    cta_link,
    overlay_opacity = 40,
    text_color = 'white',
    text_alignment = 'center'
  } = data;

  const backgroundImageUrl = background_image?.data?.attributes?.url;
  const altText = background_image?.data?.attributes?.alternativeText || title || 'Hero image';

  const textAlignmentClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  const textColorClasses = {
    white: 'text-white',
    black: 'text-black'
  };

  return (
    <section className={`relative ${context === 'sidebar' ? 'h-64' : 'h-96 lg:h-[70vh]'} overflow-hidden`}>
      {/* Background Image */}
      {backgroundImageUrl ? (
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${backgroundImageUrl})` }}
          role="img"
          aria-label={altText}
        />
      ) : (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400" />
      )}

      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black"
        style={{ opacity: overlay_opacity / 100 }}
      />

      {/* Content */}
      <div className={`relative z-10 h-full flex items-center justify-center px-4 ${textAlignmentClasses[text_alignment]}`}>
        <div className="max-w-4xl mx-auto">
          {subtitle && (
            <p className={`text-sm md:text-base uppercase tracking-wide mb-2 md:mb-4 ${textColorClasses[text_color]} opacity-90`}>
              {subtitle}
            </p>
          )}
          
          {title && (
            <h1 className={`text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 ${textColorClasses[text_color]}`}>
              {title}
            </h1>
          )}
          
          {description && (
            <p className={`text-base md:text-lg mb-6 md:mb-8 ${textColorClasses[text_color]} opacity-90 max-w-2xl ${text_alignment === 'center' ? 'mx-auto' : ''}`}>
              {description}
            </p>
          )}
          
          {cta_text && cta_link && (
            <Link
              href={cta_link}
              className={`inline-block px-6 md:px-8 py-3 md:py-4 border-2 font-medium uppercase tracking-wide transition-all duration-300 hover:scale-105 ${
                text_color === 'white' 
                  ? 'border-white text-white hover:bg-white hover:text-black' 
                  : 'border-black text-black hover:bg-black hover:text-white'
              }`}
            >
              {cta_text}
            </Link>
          )}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
