1:"$Sreact.fragment"
2:I[5080,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
3:I[9576,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"AuthProvider"]
4:I[4303,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"AccountProvider"]
5:I[8088,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"CartProvider"]
6:I[3503,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
7:I[5793,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
8:I[7555,[],""]
9:I[1901,["39","static/chunks/app/error-d022555a792f05a9.js"],"default"]
a:I[1295,[],""]
b:I[6874,["728","static/chunks/app/help/page-1c9101a470180422.js"],""]
c:I[6821,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
d:I[9634,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
e:I[9665,[],"MetadataBoundary"]
10:I[9665,[],"OutletBoundary"]
13:I[4911,[],"AsyncMetadataOutlet"]
15:I[9665,[],"ViewportBoundary"]
17:I[6614,[],""]
:HL["/_next/static/css/5a62bbe44e5ae311.css","style"]
0:{"P":null,"b":"DMw7lI2my4mO6UPvB8OZe","p":"","c":["","help"],"i":false,"f":[[["",{"children":["help",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/5a62bbe44e5ae311.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased overflow-x-hidden","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"children":["$","$L5",null,{"children":[["$","$L6",null,{}],["$","$L7",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L2",null,{"children":["$","$L8",null,{"parallelRouterKey":"children","error":"$9","errorStyles":[],"errorScripts":[],"template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-white","children":["$","div",null,{"className":"text-center max-w-md mx-auto px-4","children":[["$","h1",null,{"className":"text-6xl font-bold mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}],["$","div",null,{"className":"space-y-4","children":[["$","$Lb",null,{"href":"/","className":"luxury-button block","children":"Return Home"}],["$","div",null,{"className":"flex justify-center space-x-4 text-sm","children":[["$","$Lb",null,{"href":"/womens","className":"text-gray-600 hover:text-black","children":"Shop Women's"}],["$","$Lb",null,{"href":"/mens","className":"text-gray-600 hover:text-black","children":"Shop Men's"}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}],["$","$Lc",null,{}],["$","$Ld",null,{}]]}]}]}]}]}]}]]}],{"children":["help",["$","$1","c",{"children":[null,["$","$L8",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-white","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-4","children":["$","nav",null,{"className":"text-sm text-gray-500","children":[["$","$Lb",null,{"href":"/","className":"hover:text-black","children":"Home"}],["$","span",null,{"className":"mx-2","children":"/"}],["$","span",null,{"className":"text-black","children":"Help Centre"}]]}]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-12","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h1",null,{"className":"text-4xl font-bold mb-4","children":"Help Centre"}],["$","p",null,{"className":"text-gray-600 max-w-2xl mx-auto","children":"Find answers to your questions about shopping, delivery, returns, and more. Our customer service team is here to help you with your luxury shopping experience."}]]}],["$","div",null,{"className":"max-w-2xl mx-auto mb-12","children":["$","div",null,{"className":"relative","children":[["$","input",null,{"type":"text","placeholder":"Search for help topics...","className":"w-full px-6 py-4 border border-gray-300 focus:outline-none focus:border-black text-lg"}],["$","button",null,{"className":"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400","children":"Search"}]]}]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12","children":[["$","div","0",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Orders & Delivery"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$Lb",null,{"href":"/help/track-order","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Track your order"}]}],["$","li","1",{"children":["$","$Lb",null,{"href":"/delivery","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Delivery information"}]}],["$","li","2",{"children":["$","$Lb",null,{"href":"/help/delivery-charges","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Delivery charges"}]}],["$","li","3",{"children":["$","$Lb",null,{"href":"/help/international-delivery","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"International delivery"}]}]]}]]}],["$","div","1",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Returns & Exchanges"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$Lb",null,{"href":"/returns/request","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Request a return"}]}],["$","li","1",{"children":["$","$Lb",null,{"href":"/returns","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Return policy"}]}],["$","li","2",{"children":["$","$Lb",null,{"href":"/help/how-to-return","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"How to return"}]}],["$","li","3",{"children":["$","$Lb",null,{"href":"/help/exchanges","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Exchanges"}]}],["$","li","4",{"children":["$","$Lb",null,{"href":"/help/refunds","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Refunds"}]}]]}]]}],["$","div","2",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Account & Shopping"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$Lb",null,{"href":"/help/create-account","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Create an account"}]}],["$","li","1",{"children":["$","$Lb",null,{"href":"/help/manage-account","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Manage your account"}]}],["$","li","2",{"children":["$","$Lb",null,{"href":"/size-guide","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Size guide"}]}],["$","li","3",{"children":["$","$Lb",null,{"href":"/help/product-care","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Product care"}]}]]}]]}],["$","div","3",{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Payment & Pricing"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","0",{"children":["$","$Lb",null,{"href":"/help/payment-methods","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Payment methods"}]}],["$","li","1",{"children":["$","$Lb",null,{"href":"/help/currency-pricing","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Currency & pricing"}]}],["$","li","2",{"children":["$","$Lb",null,{"href":"/gift-cards","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Gift cards"}]}],["$","li","3",{"children":["$","$Lb",null,{"href":"/help/promotional-codes","className":"text-gray-600 hover:text-black transition-colors text-sm","children":"Promotional codes"}]}]]}]]}]]}],["$","div",null,{"className":"bg-gray-50 p-8 text-center","children":[["$","h2",null,{"className":"text-2xl font-bold mb-4","children":"Still need help?"}],["$","p",null,{"className":"text-gray-600 mb-6","children":"Our customer service team is available to assist you with any questions."}],["$","div",null,{"className":"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4","children":[["$","$Lb",null,{"href":"/help/chat","className":"luxury-button text-center","children":"Live Chat"}],["$","a",null,{"href":"mailto:<EMAIL>","className":"luxury-button-outline text-center","children":"Email Us"}],["$","$Lb",null,{"href":"/returns/request","className":"luxury-button-outline text-center","children":"Start a Return"}]]}],["$","div",null,{"className":"mt-6 text-sm text-gray-500","children":["$","p",null,{"children":"Customer Service Hours: Monday - Friday, 9AM - 6PM GMT"}]}]]}]]}]]}],["$","$Le",null,{"children":"$Lf"}],null,["$","$L10",null,{"children":["$L11","$L12",["$","$L13",null,{"promise":"$@14"}]]}]]}],{},null,false]},null,false]},[["$","div","l",{"className":"bg-white","children":[["$","div",null,{"className":"relative mb-8","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[4/3] w-full h-96","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"text-center space-y-4","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"300px","height":"48px"},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"200px","height":"24px"},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"120px","height":"40px"},"aria-hidden":"true","children":"$undefined"}]]}]}]]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-8","children":[["$","div",null,{"className":"mb-6","children":[["$","div",null,{"className":"h-6 bg-gray-200 animate-pulse rounded w-32 mb-2"}],["$","div",null,{"className":"h-8 bg-gray-200 animate-pulse rounded w-48"}]]}],["$","div",null,{"className":"flex space-x-4 overflow-hidden mb-12","children":[["$","div","0",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","1",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","2",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","3",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}]]}]]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-8","children":[["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4","children":[["$","div","0",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}],["$","div","1",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}],["$","div","2",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}]]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-4","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}]]}]]}]]}],[],[]],false],["$","$1","h",{"children":[null,["$","$1","nHA54XMUVAVV-D4Z_Byjx",{"children":[["$","$L15",null,{"children":"$L16"}],null]}],null]}],false]],"m":"$undefined","G":["$17","$undefined"],"s":false,"S":true}
18:"$Sreact.suspense"
19:I[4911,[],"AsyncMetadata"]
f:["$","$18",null,{"fallback":null,"children":["$","$L19",null,{"promise":"$@1a"}]}]
12:null
16:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
11:null
1a:{"metadata":[["$","title","0",{"children":"Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK"}],["$","meta","1",{"name":"description","content":"Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands."}],["$","meta","2",{"name":"keywords","content":"luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
14:{"metadata":"$1a:metadata","error":null,"digest":"$undefined"}
