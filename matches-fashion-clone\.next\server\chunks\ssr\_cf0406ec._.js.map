{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ui/Skeleton.tsx"], "sourcesContent": ["import React from 'react';\nimport { clsx } from 'clsx';\n\ninterface SkeletonProps {\n  className?: string;\n  width?: string | number;\n  height?: string | number;\n  variant?: 'rectangular' | 'circular' | 'text';\n  animation?: 'pulse' | 'wave' | 'none';\n  children?: React.ReactNode;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({\n  className,\n  width,\n  height,\n  variant = 'rectangular',\n  animation = 'pulse',\n  children,\n}) => {\n  const baseClasses = 'bg-gray-200';\n  \n  const variantClasses = {\n    rectangular: 'rounded',\n    circular: 'rounded-full',\n    text: 'rounded-sm',\n  };\n\n  const animationClasses = {\n    pulse: 'animate-pulse',\n    wave: 'animate-pulse', // Could be enhanced with custom wave animation\n    none: '',\n  };\n\n  const style: React.CSSProperties = {};\n  if (width) style.width = typeof width === 'number' ? `${width}px` : width;\n  if (height) style.height = typeof height === 'number' ? `${height}px` : height;\n\n  return (\n    <div\n      className={clsx(\n        baseClasses,\n        variantClasses[variant],\n        animationClasses[animation],\n        className\n      )}\n      style={style}\n      aria-hidden=\"true\"\n    >\n      {children}\n    </div>\n  );\n};\n\n// Specialized skeleton components\nexport const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({\n  lines = 1,\n  className,\n}) => (\n  <div className={clsx('space-y-2', className)}>\n    {Array.from({ length: lines }).map((_, index) => (\n      <Skeleton\n        key={index}\n        variant=\"text\"\n        height={16}\n        width={index === lines - 1 ? '75%' : '100%'}\n      />\n    ))}\n  </div>\n);\n\nexport const ImageSkeleton: React.FC<{\n  width?: string | number;\n  height?: string | number;\n  className?: string;\n  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';\n}> = ({ width, height, className, aspectRatio }) => {\n  const aspectRatioClasses = {\n    square: 'aspect-square',\n    video: 'aspect-video',\n    portrait: 'aspect-[3/4]',\n    landscape: 'aspect-[4/3]',\n  };\n\n  return (\n    <Skeleton\n      className={clsx(\n        aspectRatio && aspectRatioClasses[aspectRatio],\n        className\n      )}\n      width={width}\n      height={height}\n    />\n  );\n};\n\nexport const ProductCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (\n  <div className={clsx('space-y-3', className)}>\n    <ImageSkeleton aspectRatio=\"portrait\" className=\"w-full\" />\n    <div className=\"space-y-2\">\n      <TextSkeleton lines={1} />\n      <TextSkeleton lines={1} />\n      <Skeleton height={20} width=\"60%\" />\n    </div>\n  </div>\n);\n\nexport const ProductGridSkeleton: React.FC<{\n  count?: number;\n  columns?: number;\n  className?: string;\n}> = ({ count = 8, columns = 4, className }) => (\n  <div\n    className={clsx(\n      'grid gap-6',\n      {\n        'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4': columns === 4,\n        'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': columns === 3,\n        'grid-cols-1 sm:grid-cols-2': columns === 2,\n        'grid-cols-1': columns === 1,\n      },\n      className\n    )}\n  >\n    {Array.from({ length: count }).map((_, index) => (\n      <ProductCardSkeleton key={index} />\n    ))}\n  </div>\n);\n\nexport const HeaderSkeleton: React.FC<{ className?: string }> = ({ className }) => (\n  <div className={clsx('flex items-center justify-between p-4', className)}>\n    <Skeleton width={120} height={32} />\n    <div className=\"flex items-center space-x-4\">\n      <Skeleton width={200} height={32} />\n      <Skeleton variant=\"circular\" width={32} height={32} />\n      <Skeleton variant=\"circular\" width={32} height={32} />\n    </div>\n  </div>\n);\n\nexport const FooterSkeleton: React.FC<{ className?: string }> = ({ className }) => (\n  <div className={clsx('grid grid-cols-1 md:grid-cols-4 gap-8 p-8', className)}>\n    {Array.from({ length: 4 }).map((_, index) => (\n      <div key={index} className=\"space-y-4\">\n        <Skeleton height={20} width=\"80%\" />\n        <div className=\"space-y-2\">\n          {Array.from({ length: 5 }).map((_, linkIndex) => (\n            <Skeleton key={linkIndex} height={16} width=\"60%\" />\n          ))}\n        </div>\n      </div>\n    ))}\n  </div>\n);\n\nexport const HeroSkeleton: React.FC<{ className?: string }> = ({ className }) => (\n  <div className={clsx('relative', className)}>\n    <ImageSkeleton aspectRatio=\"landscape\" className=\"w-full h-96\" />\n    <div className=\"absolute inset-0 flex items-center justify-center\">\n      <div className=\"text-center space-y-4\">\n        <Skeleton height={48} width={300} />\n        <Skeleton height={24} width={200} />\n        <Skeleton height={40} width={120} />\n      </div>\n    </div>\n  </div>\n);\n\nexport const ProductDetailSkeleton: React.FC<{ className?: string }> = ({ className }) => (\n  <div className={clsx('grid grid-cols-1 lg:grid-cols-2 gap-8', className)}>\n    {/* Image gallery skeleton */}\n    <div className=\"space-y-4\">\n      <ImageSkeleton aspectRatio=\"square\" className=\"w-full\" />\n      <div className=\"grid grid-cols-4 gap-2\">\n        {Array.from({ length: 4 }).map((_, index) => (\n          <ImageSkeleton key={index} aspectRatio=\"square\" />\n        ))}\n      </div>\n    </div>\n\n    {/* Product info skeleton */}\n    <div className=\"space-y-6\">\n      <div className=\"space-y-2\">\n        <Skeleton height={16} width=\"40%\" />\n        <Skeleton height={32} width=\"80%\" />\n        <Skeleton height={24} width=\"30%\" />\n      </div>\n\n      <div className=\"space-y-4\">\n        <Skeleton height={20} width=\"20%\" />\n        <div className=\"grid grid-cols-4 gap-2\">\n          {Array.from({ length: 4 }).map((_, index) => (\n            <Skeleton key={index} height={40} />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"space-y-2\">\n        <Skeleton height={48} width=\"100%\" />\n        <Skeleton height={40} width=\"100%\" />\n      </div>\n\n      <div className=\"space-y-2\">\n        <Skeleton height={20} width=\"30%\" />\n        <TextSkeleton lines={3} />\n      </div>\n    </div>\n  </div>\n);\n\nexport const CarouselSkeleton: React.FC<{\n  itemCount?: number;\n  className?: string;\n}> = ({ itemCount = 4, className }) => (\n  <div className={clsx('flex space-x-4 overflow-hidden', className)}>\n    {Array.from({ length: itemCount }).map((_, index) => (\n      <div key={index} className=\"flex-shrink-0 w-64\">\n        <ProductCardSkeleton />\n      </div>\n    ))}\n  </div>\n);\n\nexport default Skeleton;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA;;;AAWO,MAAM,WAAoC,CAAC,EAChD,SAAS,EACT,KAAK,EACL,MAAM,EACN,UAAU,aAAa,EACvB,YAAY,OAAO,EACnB,QAAQ,EACT;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,aAAa;QACb,UAAU;QACV,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,MAAM,QAA6B,CAAC;IACpC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,UAAU,WAAW,GAAG,MAAM,EAAE,CAAC,GAAG;IACpE,IAAI,QAAQ,MAAM,MAAM,GAAG,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;IAExE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,aACA,cAAc,CAAC,QAAQ,EACvB,gBAAgB,CAAC,UAAU,EAC3B;QAEF,OAAO;QACP,eAAY;kBAEX;;;;;;AAGP;AAGO,MAAM,eAAiE,CAAC,EAC7E,QAAQ,CAAC,EACT,SAAS,EACV,iBACC,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,aAAa;kBAC/B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAEC,SAAQ;gBACR,QAAQ;gBACR,OAAO,UAAU,QAAQ,IAAI,QAAQ;eAHhC;;;;;;;;;;AASN,MAAM,gBAKR,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE;IAC7C,MAAM,qBAAqB;QACzB,QAAQ;QACR,OAAO;QACP,UAAU;QACV,WAAW;IACb;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,eAAe,kBAAkB,CAAC,YAAY,EAC9C;QAEF,OAAO;QACP,QAAQ;;;;;;AAGd;AAEO,MAAM,sBAAwD,CAAC,EAAE,SAAS,EAAE,iBACjF,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,aAAa;;0BAChC,8OAAC;gBAAc,aAAY;gBAAW,WAAU;;;;;;0BAChD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAa,OAAO;;;;;;kCACrB,8OAAC;wBAAa,OAAO;;;;;;kCACrB,8OAAC;wBAAS,QAAQ;wBAAI,OAAM;;;;;;;;;;;;;;;;;;AAK3B,MAAM,sBAIR,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,EAAE,SAAS,EAAE,iBACzC,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,cACA;YACE,4DAA4D,YAAY;YACxE,6CAA6C,YAAY;YACzD,8BAA8B,YAAY;YAC1C,eAAe,YAAY;QAC7B,GACA;kBAGD,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,yBAAyB;;;;;;;;;;AAKzB,MAAM,iBAAmD,CAAC,EAAE,SAAS,EAAE,iBAC5E,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,yCAAyC;;0BAC5D,8OAAC;gBAAS,OAAO;gBAAK,QAAQ;;;;;;0BAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAO;wBAAK,QAAQ;;;;;;kCAC9B,8OAAC;wBAAS,SAAQ;wBAAW,OAAO;wBAAI,QAAQ;;;;;;kCAChD,8OAAC;wBAAS,SAAQ;wBAAW,OAAO;wBAAI,QAAQ;;;;;;;;;;;;;;;;;;AAK/C,MAAM,iBAAmD,CAAC,EAAE,SAAS,EAAE,iBAC5E,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,6CAA6C;kBAC/D,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAS,QAAQ;wBAAI,OAAM;;;;;;kCAC5B,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,0BACjC,8OAAC;gCAAyB,QAAQ;gCAAI,OAAM;+BAA7B;;;;;;;;;;;eAJX;;;;;;;;;;AAYT,MAAM,eAAiD,CAAC,EAAE,SAAS,EAAE,iBAC1E,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,YAAY;;0BAC/B,8OAAC;gBAAc,aAAY;gBAAY,WAAU;;;;;;0BACjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,QAAQ;4BAAI,OAAO;;;;;;sCAC7B,8OAAC;4BAAS,QAAQ;4BAAI,OAAO;;;;;;sCAC7B,8OAAC;4BAAS,QAAQ;4BAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAM9B,MAAM,wBAA0D,CAAC,EAAE,SAAS,EAAE,iBACnF,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,yCAAyC;;0BAE5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAc,aAAY;wBAAS,WAAU;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAA0B,aAAY;+BAAnB;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;0CAC5B,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;0CAC5B,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;;;;;;;kCAG9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wCAAqB,QAAQ;uCAAf;;;;;;;;;;;;;;;;kCAKrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;0CAC5B,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;;;;;;;kCAG9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,QAAQ;gCAAI,OAAM;;;;;;0CAC5B,8OAAC;gCAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMtB,MAAM,mBAGR,CAAC,EAAE,YAAY,CAAC,EAAE,SAAS,EAAE,iBAChC,8OAAC;QAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,kCAAkC;kBACpD,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAU,GAAG,GAAG,CAAC,CAAC,GAAG,sBACzC,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;;;;;eADO;;;;;;;;;;uCAOD", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/loading.tsx"], "sourcesContent": ["import React from 'react';\nimport { HeroSkeleton, CarouselSkeleton } from '@/components/ui/Skeleton';\n\nexport default function Loading() {\n  return (\n    <div className=\"bg-white\">\n      {/* Hero Section Skeleton */}\n      <HeroSkeleton className=\"mb-8\" />\n\n      {/* Product Carousel Skeleton */}\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        <div className=\"mb-6\">\n          <div className=\"h-6 bg-gray-200 animate-pulse rounded w-32 mb-2\" />\n          <div className=\"h-8 bg-gray-200 animate-pulse rounded w-48\" />\n        </div>\n        <CarouselSkeleton itemCount={4} className=\"mb-12\" />\n      </div>\n\n      {/* Content Blocks Skeleton */}\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        {/* Large blocks */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4\">\n          <div className=\"space-y-3\">\n            <div className=\"aspect-[4/5] bg-gray-200 animate-pulse rounded\" />\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n              <div className=\"h-3 bg-gray-200 animate-pulse rounded w-1/2\" />\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"aspect-[4/5] bg-gray-200 animate-pulse rounded\" />\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n              <div className=\"h-3 bg-gray-200 animate-pulse rounded w-1/2\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Small blocks */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          {Array.from({ length: 3 }).map((_, index) => (\n            <div key={index} className=\"space-y-3\">\n              <div className=\"aspect-[3/4] bg-gray-200 animate-pulse rounded\" />\n              <div className=\"h-3 bg-gray-200 animate-pulse rounded w-2/3\" />\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom large blocks */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n          <div className=\"space-y-3\">\n            <div className=\"aspect-[4/5] bg-gray-200 animate-pulse rounded\" />\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n              <div className=\"h-3 bg-gray-200 animate-pulse rounded w-1/2\" />\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"aspect-[4/5] bg-gray-200 animate-pulse rounded\" />\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 animate-pulse rounded w-3/4\" />\n              <div className=\"h-3 bg-gray-200 animate-pulse rounded w-1/2\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,oIAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;0BAGxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAW;wBAAG,WAAU;;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;kCAQd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}]}