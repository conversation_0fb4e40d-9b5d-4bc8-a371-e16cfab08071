{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/DefaultTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface DefaultTemplateProps {\n  children: React.ReactNode;\n}\n\nconst DefaultTemplate: React.FC<DefaultTemplateProps> = ({ children }) => {\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-8\">\n      {children}\n    </div>\n  );\n};\n\nexport default DefaultTemplate;\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}]}