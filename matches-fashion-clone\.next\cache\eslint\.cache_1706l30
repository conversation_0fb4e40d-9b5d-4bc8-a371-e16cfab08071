[{"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\env.ts": "1", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\medusa.js": "2", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\strapi.js": "3", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\types.ts": "4", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\addresses\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\login\\page.tsx": "6", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\orders\\page.tsx": "7", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\profile\\page.tsx": "9", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\register\\page.tsx": "10", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\wishlists\\page.tsx": "11", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\cart\\page.tsx": "12", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\delivery\\page.tsx": "13", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx": "14", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\chat\\page.tsx": "15", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\page.tsx": "16", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx": "17", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx": "18", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\page.tsx": "19", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx": "20", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx": "21", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\page.tsx": "22", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\product\\[handle]\\page.tsx": "23", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\page.tsx": "24", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\request\\page.tsx": "25", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx": "26", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\page.tsx": "27", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx": "28", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ChatWidget.tsx": "29", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ErrorBoundary.tsx": "30", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\Footer.tsx": "31", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\Header.tsx": "32", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\LoadingIndicator.tsx": "33", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\MegaMenu.tsx": "34", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductCard.tsx": "35", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductCarousel.tsx": "36", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductGrid.tsx": "37", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\QCTestingPanel.tsx": "38", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ReturnRequestForm.tsx": "39", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\SearchModal.tsx": "40", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ui\\Loading.tsx": "41", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ui\\Skeleton.tsx": "42", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\AccountContext.tsx": "43", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\AuthContext.tsx": "44", "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\CartContext.tsx": "45"}, {"size": 7252, "mtime": *************, "results": "46", "hashOfConfig": "47"}, {"size": 481, "mtime": *************, "results": "48", "hashOfConfig": "49"}, {"size": 6522, "mtime": *************, "results": "50", "hashOfConfig": "49"}, {"size": 6700, "mtime": *************, "results": "51", "hashOfConfig": "47"}, {"size": 2675, "mtime": *************, "results": "52", "hashOfConfig": "47"}, {"size": 1816, "mtime": *************, "results": "53", "hashOfConfig": "47"}, {"size": 1303, "mtime": *************, "results": "54", "hashOfConfig": "47"}, {"size": 1275, "mtime": *************, "results": "55", "hashOfConfig": "47"}, {"size": 942, "mtime": *************, "results": "56", "hashOfConfig": "47"}, {"size": 1694, "mtime": 1749599561980, "results": "57", "hashOfConfig": "47"}, {"size": 4253, "mtime": 1749599561981, "results": "58", "hashOfConfig": "47"}, {"size": 1695, "mtime": 1749599561981, "results": "59", "hashOfConfig": "47"}, {"size": 4933, "mtime": 1749599561982, "results": "60", "hashOfConfig": "47"}, {"size": 4979, "mtime": 1749623126278, "results": "61", "hashOfConfig": "47"}, {"size": 677, "mtime": 1749599561983, "results": "62", "hashOfConfig": "47"}, {"size": 4738, "mtime": 1749599561984, "results": "63", "hashOfConfig": "47"}, {"size": 1842, "mtime": 1749623749978, "results": "64", "hashOfConfig": "47"}, {"size": 2804, "mtime": 1749623475825, "results": "65", "hashOfConfig": "47"}, {"size": 9392, "mtime": 1749599561985, "results": "66", "hashOfConfig": "47"}, {"size": 1994, "mtime": 1749599561986, "results": "67", "hashOfConfig": "47"}, {"size": 1063, "mtime": 1749599561986, "results": "68", "hashOfConfig": "47"}, {"size": 12167, "mtime": 1749624057970, "results": "69", "hashOfConfig": "47"}, {"size": 9152, "mtime": 1749623965610, "results": "70", "hashOfConfig": "47"}, {"size": 6336, "mtime": 1749599561987, "results": "71", "hashOfConfig": "47"}, {"size": 720, "mtime": 1749599561987, "results": "72", "hashOfConfig": "47"}, {"size": 1576, "mtime": 1749599561988, "results": "73", "hashOfConfig": "47"}, {"size": 10197, "mtime": 1749599561988, "results": "74", "hashOfConfig": "47"}, {"size": 2115, "mtime": 1749599561989, "results": "75", "hashOfConfig": "47"}, {"size": 1862, "mtime": 1749599561990, "results": "76", "hashOfConfig": "47"}, {"size": 6953, "mtime": 1749623097577, "results": "77", "hashOfConfig": "47"}, {"size": 6912, "mtime": 1749599561990, "results": "78", "hashOfConfig": "47"}, {"size": 15064, "mtime": 1749599561991, "results": "79", "hashOfConfig": "47"}, {"size": 762, "mtime": 1749599561991, "results": "80", "hashOfConfig": "47"}, {"size": 13259, "mtime": 1749599561992, "results": "81", "hashOfConfig": "47"}, {"size": 3138, "mtime": 1749599561993, "results": "82", "hashOfConfig": "47"}, {"size": 6942, "mtime": 1749599561993, "results": "83", "hashOfConfig": "47"}, {"size": 13233, "mtime": 1749599561993, "results": "84", "hashOfConfig": "47"}, {"size": 7799, "mtime": 1749599561994, "results": "85", "hashOfConfig": "47"}, {"size": 2671, "mtime": 1749599561994, "results": "86", "hashOfConfig": "47"}, {"size": 7163, "mtime": 1749599561994, "results": "87", "hashOfConfig": "47"}, {"size": 8147, "mtime": 1749623343996, "results": "88", "hashOfConfig": "47"}, {"size": 6346, "mtime": 1749623284222, "results": "89", "hashOfConfig": "47"}, {"size": 3892, "mtime": 1749599561995, "results": "90", "hashOfConfig": "47"}, {"size": 1695, "mtime": 1749599561995, "results": "91", "hashOfConfig": "47"}, {"size": 1919, "mtime": 1749599561995, "results": "92", "hashOfConfig": "47"}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1elbt2m", {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dw3p1e", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\env.ts", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\medusa.js", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\strapi.js", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\lib\\types.ts", ["228", "229", "230", "231", "232", "233", "234", "235"], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\addresses\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\wishlists\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\delivery\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx", ["236", "237", "238"], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\chat\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx", ["239"], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\page.tsx", ["240", "241"], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\product\\[handle]\\page.tsx", ["242", "243"], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\request\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ChatWidget.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\LoadingIndicator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\MegaMenu.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductCard.tsx", [], ["244"], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductCarousel.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ProductGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\QCTestingPanel.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ReturnRequestForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\SearchModal.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ui\\Skeleton.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\AccountContext.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\context\\CartContext.tsx", [], [], {"ruleId": "245", "severity": 2, "message": "246", "line": 106, "column": 31, "nodeType": "247", "messageId": "248", "endLine": 106, "endColumn": 34, "suggestions": "249"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 132, "column": 29, "nodeType": "247", "messageId": "248", "endLine": 132, "endColumn": 32, "suggestions": "250"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 140, "column": 29, "nodeType": "247", "messageId": "248", "endLine": 140, "endColumn": 32, "suggestions": "251"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 149, "column": 29, "nodeType": "247", "messageId": "248", "endLine": 149, "endColumn": 32, "suggestions": "252"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 161, "column": 29, "nodeType": "247", "messageId": "248", "endLine": 161, "endColumn": 32, "suggestions": "253"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 188, "column": 31, "nodeType": "247", "messageId": "248", "endLine": 188, "endColumn": 34, "suggestions": "254"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 194, "column": 29, "nodeType": "247", "messageId": "248", "endLine": 194, "endColumn": 32, "suggestions": "255"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 213, "column": 28, "nodeType": "247", "messageId": "248", "endLine": 213, "endColumn": 31, "suggestions": "256"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 27, "column": 25, "nodeType": "247", "messageId": "248", "endLine": 27, "endColumn": 28, "suggestions": "257"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 72, "column": 28, "nodeType": "247", "messageId": "248", "endLine": 72, "endColumn": 31, "suggestions": "258"}, {"ruleId": "245", "severity": 2, "message": "246", "line": 74, "column": 40, "nodeType": "247", "messageId": "248", "endLine": 74, "endColumn": 43, "suggestions": "259"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 2, "column": 24, "nodeType": null, "messageId": "262", "endLine": 2, "endColumn": 43}, {"ruleId": "260", "severity": 2, "message": "263", "line": 9, "column": 3, "nodeType": null, "messageId": "262", "endLine": 9, "endColumn": 24}, {"ruleId": "260", "severity": 2, "message": "264", "line": 78, "column": 5, "nodeType": null, "messageId": "262", "endLine": 78, "endColumn": 18}, {"ruleId": "265", "severity": 2, "message": "266", "line": 163, "column": 100, "nodeType": "267", "messageId": "268", "suggestions": "269"}, {"ruleId": "265", "severity": 2, "message": "266", "line": 169, "column": 30, "nodeType": "267", "messageId": "268", "suggestions": "270"}, {"ruleId": "271", "severity": 1, "message": "272", "line": 55, "column": 17, "nodeType": "273", "endLine": 55, "endColumn": 104, "suppressions": "274"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["275", "276"], ["277", "278"], ["279", "280"], ["281", "282"], ["283", "284"], ["285", "286"], ["287", "288"], ["289", "290"], ["291", "292"], ["293", "294"], ["295", "296"], "@typescript-eslint/no-unused-vars", "'ProductGridSkeleton' is defined but never used.", "unusedVar", "'StrapiMediaAttributes' is defined but never used.", "'hasDataErrors' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["297", "298", "299", "300"], ["301", "302", "303", "304"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["305"], {"messageId": "306", "fix": "307", "desc": "308"}, {"messageId": "309", "fix": "310", "desc": "311"}, {"messageId": "306", "fix": "312", "desc": "308"}, {"messageId": "309", "fix": "313", "desc": "311"}, {"messageId": "306", "fix": "314", "desc": "308"}, {"messageId": "309", "fix": "315", "desc": "311"}, {"messageId": "306", "fix": "316", "desc": "308"}, {"messageId": "309", "fix": "317", "desc": "311"}, {"messageId": "306", "fix": "318", "desc": "308"}, {"messageId": "309", "fix": "319", "desc": "311"}, {"messageId": "306", "fix": "320", "desc": "308"}, {"messageId": "309", "fix": "321", "desc": "311"}, {"messageId": "306", "fix": "322", "desc": "308"}, {"messageId": "309", "fix": "323", "desc": "311"}, {"messageId": "306", "fix": "324", "desc": "308"}, {"messageId": "309", "fix": "325", "desc": "311"}, {"messageId": "306", "fix": "326", "desc": "308"}, {"messageId": "309", "fix": "327", "desc": "311"}, {"messageId": "306", "fix": "328", "desc": "308"}, {"messageId": "309", "fix": "329", "desc": "311"}, {"messageId": "306", "fix": "330", "desc": "308"}, {"messageId": "309", "fix": "331", "desc": "311"}, {"messageId": "332", "data": "333", "fix": "334", "desc": "335"}, {"messageId": "332", "data": "336", "fix": "337", "desc": "338"}, {"messageId": "332", "data": "339", "fix": "340", "desc": "341"}, {"messageId": "332", "data": "342", "fix": "343", "desc": "344"}, {"messageId": "332", "data": "345", "fix": "346", "desc": "335"}, {"messageId": "332", "data": "347", "fix": "348", "desc": "338"}, {"messageId": "332", "data": "349", "fix": "350", "desc": "341"}, {"messageId": "332", "data": "351", "fix": "352", "desc": "344"}, {"kind": "353", "justification": "354"}, "suggestUnknown", {"range": "355", "text": "356"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "357", "text": "358"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "359", "text": "356"}, {"range": "360", "text": "358"}, {"range": "361", "text": "356"}, {"range": "362", "text": "358"}, {"range": "363", "text": "356"}, {"range": "364", "text": "358"}, {"range": "365", "text": "356"}, {"range": "366", "text": "358"}, {"range": "367", "text": "356"}, {"range": "368", "text": "358"}, {"range": "369", "text": "356"}, {"range": "370", "text": "358"}, {"range": "371", "text": "356"}, {"range": "372", "text": "358"}, {"range": "373", "text": "356"}, {"range": "374", "text": "358"}, {"range": "375", "text": "356"}, {"range": "376", "text": "358"}, {"range": "377", "text": "356"}, {"range": "378", "text": "358"}, "replaceWithAlt", {"alt": "379"}, {"range": "380", "text": "381"}, "Replace with `&apos;`.", {"alt": "382"}, {"range": "383", "text": "384"}, "Replace with `&lsquo;`.", {"alt": "385"}, {"range": "386", "text": "387"}, "Replace with `&#39;`.", {"alt": "388"}, {"range": "389", "text": "390"}, "Replace with `&rsquo;`.", {"alt": "379"}, {"range": "391", "text": "392"}, {"alt": "382"}, {"range": "393", "text": "394"}, {"alt": "385"}, {"range": "395", "text": "396"}, {"alt": "388"}, {"range": "397", "text": "398"}, "directive", "", [2431, 2434], "unknown", [2431, 2434], "never", [3014, 3017], [3014, 3017], [3162, 3165], [3162, 3165], [3335, 3338], [3335, 3338], [3640, 3643], [3640, 3643], [4336, 4339], [4336, 4339], [4521, 4524], [4521, 4524], [4963, 4966], [4963, 4966], [807, 810], [807, 810], [2487, 2490], [2487, 2490], [2603, 2606], [2603, 2606], "&apos;", [7799, 7812], "EDITOR&apos;S NOTE", "&lsquo;", [7799, 7812], "EDITOR&lsquo;S NOTE", "&#39;", [7799, 7812], "EDITOR&#39;S NOTE", "&rsquo;", [7799, 7812], "EDITOR&rsquo;S NOTE", [8311, 8332], "Spring &apos;25 Collection", [8311, 8332], "Spring &lsquo;25 Collection", [8311, 8332], "Spring &#39;25 Collection", [8311, 8332], "Spring &rsquo;25 Collection"]