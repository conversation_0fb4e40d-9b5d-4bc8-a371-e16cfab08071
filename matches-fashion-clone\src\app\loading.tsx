import React from 'react';
import { HeroSkeleton, ProductGridSkeleton, CarouselSkeleton } from '@/components/ui/Skeleton';

export default function Loading() {
  return (
    <div className="bg-white">
      {/* Hero Section Skeleton */}
      <HeroSkeleton className="mb-8" />

      {/* Product Carousel Skeleton */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-6">
          <div className="h-6 bg-gray-200 animate-pulse rounded w-32 mb-2" />
          <div className="h-8 bg-gray-200 animate-pulse rounded w-48" />
        </div>
        <CarouselSkeleton itemCount={4} className="mb-12" />
      </div>

      {/* Content Blocks Skeleton */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Large blocks */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          <div className="space-y-3">
            <div className="aspect-[4/5] bg-gray-200 animate-pulse rounded" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/2" />
            </div>
          </div>
          <div className="space-y-3">
            <div className="aspect-[4/5] bg-gray-200 animate-pulse rounded" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/2" />
            </div>
          </div>
        </div>

        {/* Small blocks */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <div className="aspect-[3/4] bg-gray-200 animate-pulse rounded" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-2/3" />
            </div>
          ))}
        </div>

        {/* Bottom large blocks */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="aspect-[4/5] bg-gray-200 animate-pulse rounded" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/2" />
            </div>
          </div>
          <div className="space-y-3">
            <div className="aspect-[4/5] bg-gray-200 animate-pulse rounded" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/2" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
