(()=>{var e={};e.id=373,e.ids=[373],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9040:(e,s,t)=>{Promise.resolve().then(t.bind(t,35011))},9557:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(60687),a=t(18780),i=t(52929),n=t(16189),l=t(43210),o=t(85814),u=t.n(o);function c(){let{user:e}=(0,a.A)(),{wishlists:s,addWishlist:t,removeWishlist:o,updateWishlistNote:c,addWishlistItem:d,removeWishlistItem:h}=(0,i.F)();(0,n.useRouter)();let[p,m]=(0,l.useState)(""),[x,f]=(0,l.useState)(""),[b,v]=(0,l.useState)({});if(!e)return null;let w=e=>{b[e]&&(d(e,b[e]),v(s=>({...s,[e]:""})))},g=e=>{let t=s.find(s=>s.id===e);if(!t)return;let r=function(e){let s=btoa(JSON.stringify(e));return`${window.location.origin}/wishlist-share?data=${encodeURIComponent(s)}`}({name:t.name,note:t.note,items:t.items});navigator.clipboard.writeText(r),alert("Share link copied to clipboard")};return(0,r.jsxs)("div",{className:"max-w-xl mx-auto py-10 space-y-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Wishlists"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p&&(t(p,x),m(""),f(""))},className:"space-y-2",children:[(0,r.jsx)("input",{value:p,onChange:e=>m(e.target.value),placeholder:"Wishlist name",className:"w-full border p-2",required:!0}),(0,r.jsx)("textarea",{value:x,onChange:e=>f(e.target.value),placeholder:"Notes",className:"w-full border p-2"}),(0,r.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Create Wishlist"})]}),0===s.length?(0,r.jsx)("p",{children:"No wishlists yet."}):(0,r.jsx)("div",{className:"space-y-8",children:s.map(e=>(0,r.jsxs)("div",{className:"border p-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"font-medium text-lg",children:e.name}),(0,r.jsx)("button",{onClick:()=>o(e.id),className:"text-sm underline",children:"Delete"})]}),(0,r.jsx)("textarea",{value:e.note,onChange:s=>c(e.id,s.target.value),className:"w-full border p-2",placeholder:"Add note"}),0===e.items.length?(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"No items."}):(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1",children:e.items.map(s=>(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:s.title}),(0,r.jsx)("button",{onClick:()=>h(e.id,s.id),className:"text-xs underline",children:"Remove"})]},s.id))}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,r.jsx)("input",{value:b[e.id]||"",onChange:s=>v(t=>({...t,[e.id]:s.target.value})),placeholder:"Add item",className:"flex-1 border p-2"}),(0,r.jsx)("button",{type:"button",onClick:()=>w(e.id),className:"luxury-button",children:"Add"}),(0,r.jsx)("button",{type:"button",onClick:()=>g(e.id),className:"luxury-button-outline",children:"Share"})]})]},e.id))}),(0,r.jsx)("div",{children:(0,r.jsx)(u(),{href:"/account",className:"luxury-button-outline",children:"Back to account"})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},18768:(e,s,t)=>{Promise.resolve().then(t.bind(t,9557))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35011:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\account\\\\wishlists\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\wishlists\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},87284:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let u={children:["",{children:["account",{children:["wishlists",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35011)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\wishlists\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\wishlists\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/wishlists/page",pathname:"/account/wishlists",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,20,137],()=>t(87284));module.exports=r})();