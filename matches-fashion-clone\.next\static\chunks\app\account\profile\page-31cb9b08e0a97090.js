(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[232],{2381:(e,t,r)=>{Promise.resolve().then(r.bind(r,2610))},2610:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(5155),l=r(9576),u=r(5695),a=r(2115),n=r(6874),o=r.n(n);function i(){let{user:e}=(0,l.A)(),t=(0,u.useRouter)();return((0,a.useEffect)(()=>{e||t.replace("/account/login")},[e,t]),e)?(0,s.jsxs)("div",{className:"max-w-xl mx-auto py-10 space-y-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Profile"}),(0,s.jsxs)("p",{children:["Email: ",(0,s.jsx)("strong",{children:e.email})]}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)(o(),{href:"/account/orders",className:"luxury-button-outline",children:"Orders"}),(0,s.jsx)(o(),{href:"/account/addresses",className:"luxury-button-outline",children:"Addresses"}),(0,s.jsx)(o(),{href:"/account/wishlists",className:"luxury-button-outline",children:"Wishlists"})]})]}):null}},9576:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>a});var s=r(5155),l=r(2115);let u=(0,l.createContext)(void 0),a=e=>{let{children:t}=e,[r,a]=(0,l.useState)(null);return(0,l.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&a(JSON.parse(e))},[]),(0,s.jsx)(u.Provider,{value:{user:r,login:(e,t)=>{let r=localStorage.getItem("authUser");if(r){let s=JSON.parse(r);if(s.email===e&&s.password===t)return a(s),!0}return!1},register:(e,t)=>{let r={email:e,password:t};return localStorage.setItem("authUser",JSON.stringify(r)),a(r),!0},logout:()=>{a(null),localStorage.removeItem("authUser")}},children:t})},n=()=>{let e=(0,l.useContext)(u);if(!e)throw Error("useAuth must be used within AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(2381)),_N_E=e.O()}]);