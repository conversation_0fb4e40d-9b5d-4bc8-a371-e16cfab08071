# Troubleshooting Guide

This guide helps resolve common issues you might encounter with the improved Matches Fashion Clone project.

## 🔧 Build Issues

### TypeScript Errors

**Problem**: TypeScript compilation errors during build
**Solution**:
```bash
# Check for type errors
npm run type-check

# Common fixes:
# 1. Ensure all imports use correct paths
# 2. Check that all required props are provided
# 3. Verify environment variables are properly typed
```

### ESLint Errors

**Problem**: Linting errors preventing build
**Solution**:
```bash
# Run linting
npm run lint

# Common issues:
# - Unused variables: Remove or prefix with underscore
# - Missing dependencies: Add to useEffect dependency array
# - Prefer const: Use const instead of let where possible
```

### Next.js Configuration Issues

**Problem**: Invalid next.config.ts options
**Solution**:
- Check that all configuration options are valid for Next.js 15
- Remove deprecated experimental options
- Ensure webpack configuration is properly structured

## 🌐 API Issues

### Strapi API Connection

**Problem**: Strapi API errors or connection issues
**Solutions**:

1. **Check Environment Variables**:
   ```bash
   # Verify in .env.local
   NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337
   STRAPI_API_TOKEN=your_token_here
   ```

2. **Verify Strapi Server**:
   ```bash
   # Check if Strapi is running
   curl http://localhost:1337/api/homepage
   ```

3. **Check Network Issues**:
   - Ensure Strapi server is accessible
   - Check firewall settings
   - Verify CORS configuration in Strapi

### Medusa API Connection

**Problem**: Medusa API errors or product loading issues
**Solutions**:

1. **Check Environment Variables**:
   ```bash
   # Verify in .env.local
   NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000
   NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY=your_key_here
   ```

2. **Verify Medusa Server**:
   ```bash
   # Check if Medusa is running
   curl http://localhost:9000/store/products
   ```

3. **Sales Channel Configuration**:
   - Ensure the sales channel ID in the code matches your Medusa setup
   - Check that products are assigned to the correct sales channel

## 🎨 UI/UX Issues

### Loading States Not Showing

**Problem**: Skeleton components or loading indicators not displaying
**Solutions**:
- Check that loading states are properly imported
- Verify that loading conditions are correctly implemented
- Ensure CSS classes are properly applied

### Error Boundaries Not Working

**Problem**: Error boundaries not catching errors
**Solutions**:
- Verify ErrorBoundary is properly wrapped around components
- Check that error boundaries are class components (not functional)
- Ensure error boundaries are not catching their own errors

### Styling Issues

**Problem**: Tailwind CSS classes not applying
**Solutions**:
- Ensure Tailwind CSS is properly configured
- Check that classes are not being purged incorrectly
- Verify that custom styles don't conflict with Tailwind

## 🔄 Development Issues

### Hot Reload Not Working

**Problem**: Changes not reflecting in development
**Solutions**:
```bash
# Clear Next.js cache
rm -rf .next

# Restart development server
npm run dev
```

### Port Already in Use

**Problem**: Development server can't start on default port
**Solutions**:
- Next.js will automatically use the next available port
- Or manually specify a port: `next dev -p 3002`
- Kill existing processes using the port

### Memory Issues

**Problem**: Out of memory errors during build
**Solutions**:
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

## 📦 Dependency Issues

### Package Installation Errors

**Problem**: npm install fails
**Solutions**:
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Try using yarn instead
yarn install
```

### Version Conflicts

**Problem**: Dependency version conflicts
**Solutions**:
- Check package.json for conflicting versions
- Use npm ls to identify dependency tree issues
- Consider using npm audit fix for security updates

## 🔍 Debugging Tips

### Enable Verbose Logging

Add to your .env.local:
```bash
VERBOSE_LOGGING=true
```

### Check Browser Console

- Open browser developer tools
- Check Console tab for JavaScript errors
- Check Network tab for failed API requests

### Server-Side Debugging

- Check terminal output for server-side errors
- Use console.log statements for debugging
- Check Next.js build output for warnings

### Error Tracking

For production debugging:
1. Set up Sentry or similar error tracking
2. Configure proper error reporting
3. Monitor error rates and patterns

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the documentation**: Review IMPROVEMENTS.md for detailed setup instructions
2. **Search existing issues**: Look for similar problems in the project repository
3. **Create a detailed issue report** including:
   - Error messages
   - Steps to reproduce
   - Environment details (Node.js version, OS, etc.)
   - Relevant configuration files

## 🔄 Common Recovery Steps

When all else fails, try these steps in order:

1. **Clear caches**:
   ```bash
   rm -rf .next
   npm cache clean --force
   ```

2. **Reinstall dependencies**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Reset to working state**:
   ```bash
   git stash
   git checkout main
   npm install
   npm run build
   ```

4. **Check environment**:
   - Verify Node.js version (should be 18+)
   - Check npm version
   - Ensure all required services are running

Remember: The improved error handling should provide helpful error messages to guide you toward solutions!
