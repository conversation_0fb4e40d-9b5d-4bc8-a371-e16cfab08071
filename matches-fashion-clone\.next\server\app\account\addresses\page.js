(()=>{var e={};e.id=949,e.ids=[949],e.modules={1533:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(60687),a=t(18780),n=t(52929),o=t(16189),l=t(43210),i=t(85814),u=t.n(i);function d(){let{user:e}=(0,a.A)(),{addresses:s,addAddress:t,removeAddress:i}=(0,n.F)();(0,o.useRouter)();let[d,c]=(0,l.useState)({line1:"",city:"",state:"",zip:"",country:""});return e?(0,r.jsxs)("div",{className:"max-w-xl mx-auto py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Saved Addresses"}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(d),c({line1:"",city:"",state:"",zip:"",country:""})},className:"space-y-2 mb-6",children:[(0,r.jsx)("input",{placeholder:"Address",value:d.line1,onChange:e=>c({...d,line1:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("input",{placeholder:"City",value:d.city,onChange:e=>c({...d,city:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("input",{placeholder:"State",value:d.state,onChange:e=>c({...d,state:e.target.value}),className:"w-full border p-2"}),(0,r.jsx)("input",{placeholder:"Zip",value:d.zip,onChange:e=>c({...d,zip:e.target.value}),className:"w-full border p-2"}),(0,r.jsx)("input",{placeholder:"Country",value:d.country,onChange:e=>c({...d,country:e.target.value}),className:"w-full border p-2",required:!0}),(0,r.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Add Address"})]}),0===s.length?(0,r.jsx)("p",{children:"No saved addresses."}):(0,r.jsx)("ul",{className:"space-y-4",children:s.map(e=>(0,r.jsxs)("li",{className:"border p-4 flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{children:e.line1}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.city," ",e.state," ",e.zip]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.country})]}),(0,r.jsx)("button",{onClick:()=>i(e.id),className:"text-sm underline",children:"Remove"})]},e.id))}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(u(),{href:"/account",className:"luxury-button-outline",children:"Back to account"})})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27736:(e,s,t)=>{Promise.resolve().then(t.bind(t,39955))},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32996:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var r=t(65239),a=t(48088),n=t(88170),o=t.n(n),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let u={children:["",{children:["account",{children:["addresses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39955)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\addresses\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\addresses\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/account/addresses/page",pathname:"/account/addresses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},33873:e=>{"use strict";e.exports=require("path")},37464:(e,s,t)=>{Promise.resolve().then(t.bind(t,1533))},39955:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\account\\\\addresses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\account\\addresses\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,20,137],()=>t(32996));module.exports=r})();