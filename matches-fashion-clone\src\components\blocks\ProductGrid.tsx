import React from 'react';

interface ProductGridProps {
  data: {
    title?: string;
    subtitle?: string;
    products_source?: 'medusa_category' | 'medusa_collection' | 'manual';
    product_count?: number;
    layout?: 'grid-2' | 'grid-3' | 'grid-4';
    show_filters?: boolean;
  };
  context: 'main' | 'sidebar';
  medusaCategoryId?: string;
  medusaCollectionId?: string;
}

const ProductGrid: React.FC<ProductGridProps> = ({ 
  data, 
  context, 
  medusaCategoryId, 
  medusaCollectionId 
}) => {
  const {
    title,
    subtitle,
    products_source = 'medusa_category',
    product_count = 8,
    layout = 'grid-4',
    show_filters = false
  } = data;

  // This would integrate with your existing product fetching logic
  // For now, showing placeholder
  
  const gridClasses = {
    'grid-2': 'grid-cols-1 md:grid-cols-2',
    'grid-3': 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    'grid-4': 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  };

  return (
    <section className={`${context === 'sidebar' ? 'p-4' : 'py-8'}`}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {subtitle && (
            <p className="text-sm uppercase tracking-wide text-gray-600 mb-2">
              {subtitle}
            </p>
          )}
          {title && (
            <h2 className="text-2xl md:text-3xl font-bold">
              {title}
            </h2>
          )}
        </div>
      )}

      {show_filters && context === 'main' && (
        <div className="mb-6 p-4 bg-gray-50 rounded">
          <p className="text-sm text-gray-600">
            Filters would be rendered here (integrate with your filter system)
          </p>
        </div>
      )}

      <div className={`grid ${gridClasses[layout]} gap-6`}>
        {Array.from({ length: product_count }).map((_, index) => (
          <div key={index} className="group">
            <div className="aspect-[3/4] bg-gray-200 rounded mb-3 flex items-center justify-center">
              <span className="text-gray-500 text-sm">Product {index + 1}</span>
            </div>
            <div className="space-y-1">
              <h3 className="font-medium text-sm">Product Name</h3>
              <p className="text-xs text-gray-600">Designer Name</p>
              <p className="font-medium">$XXX</p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 text-center text-sm text-gray-600">
        <p>
          Source: {products_source} 
          {medusaCategoryId && ` | Category: ${medusaCategoryId}`}
          {medusaCollectionId && ` | Collection: ${medusaCollectionId}`}
        </p>
        <p className="mt-2">
          Integrate this with your existing ProductCarousel or product fetching logic
        </p>
      </div>
    </section>
  );
};

export default ProductGrid;
