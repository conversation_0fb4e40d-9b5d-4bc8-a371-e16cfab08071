(()=>{var e={};e.id=743,e.ids=[743],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27742:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var r=t(60687),a=t(43210);function n(){let[e,s]=(0,a.useState)([{sender:"support",text:"Hi! How can we help you today?"}]),[t,n]=(0,a.useState)(""),o=(0,a.useRef)(null);return(0,r.jsxs)("div",{className:"flex flex-col h-[500px] border border-gray-300 rounded",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:[e.map((e,s)=>(0,r.jsx)("div",{className:"user"===e.sender?"text-right":"text-left",children:(0,r.jsx)("span",{className:`inline-block px-4 py-2 rounded ${"user"===e.sender?"bg-black text-white":"bg-gray-100 text-gray-800"}`,children:e.text})},s)),(0,r.jsx)("div",{ref:o})]}),(0,r.jsxs)("div",{className:"p-2 border-t flex space-x-2",children:[(0,r.jsx)("input",{value:t,onChange:e=>n(e.target.value),className:"flex-1 border border-gray-300 p-2",placeholder:"Type a message..."}),(0,r.jsx)("button",{onClick:()=>{if(!t.trim())return;let e={sender:"user",text:t.trim()};s(s=>[...s,e,{sender:"support",text:"Thank you for your message. We'll reply soon."}]),n("")},className:"luxury-button",children:"Send"})]})]})}},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41032:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\components\\\\ChatWidget.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ChatWidget.tsx","default")},43900:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),o=t.n(n),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let d={children:["",{children:["help",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87601)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\chat\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\help\\chat\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/help/chat/page",pathname:"/help/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44690:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,27742))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81642:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,41032))},87601:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(4536),n=t.n(a),o=t(41032);function l(){return(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,r.jsxs)("nav",{className:"text-sm text-gray-500",children:[(0,r.jsx)(n(),{href:"/help",className:"hover:text-black",children:"Help Centre"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{className:"text-black",children:"Chat"})]})}),(0,r.jsxs)("div",{className:"max-w-xl mx-auto px-4 py-12 space-y-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-center",children:"Live Chat"}),(0,r.jsx)(o.default,{})]})]})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,20,137],()=>t(43900));module.exports=r})();