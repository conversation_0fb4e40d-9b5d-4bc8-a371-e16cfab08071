{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/lib/medusa.js"], "sourcesContent": ["import Medusa from \"@medusajs/medusa-js\";\r\n\r\nconst MEDUSA_BACKEND_URL = process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL || \"http://localhost:9000\";\r\nconst PUBLISHABLE_API_KEY = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY;\r\n\r\nconst customHeaders = {};\r\nif (PUBLISHABLE_API_KEY) {\r\n  customHeaders['x-publishable-api-key'] = PUBLISHABLE_API_KEY;\r\n}\r\n\r\nexport const medusaClient = new Medusa({\r\n  baseUrl: MEDUSA_BACKEND_URL,\r\n  maxRetries: 3,\r\n  customHeaders: customHeaders\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,6DAA8C;AACzE,MAAM;AAEN,MAAM,gBAAgB,CAAC;AACvB,wCAAyB;IACvB,aAAa,CAAC,wBAAwB,GAAG;AAC3C;AAEO,MAAM,eAAe,IAAI,4JAAA,CAAA,UAAM,CAAC;IACrC,SAAS;IACT,YAAY;IACZ,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/product/%5Bhandle%5D/page.tsx"], "sourcesContent": ["import { medusaClient } from \"../../../../lib/medusa\";\r\nimport { notFound } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\"; // For breadcrumbs or other links\r\n\r\n// Define a basic structure for Medusa product details (can be expanded)\r\ninterface MedusaProductDetail {\r\n  id: string;\r\n  title: string;\r\n  subtitle: string | null; // For brand/designer line\r\n  description: string | null;\r\n  thumbnail: string | null;\r\n  handle: string | null;\r\n  images?: Array<{ id: string; url: string }>;\r\n  options?: Array<{\r\n    id: string;\r\n    title: string;\r\n    values: Array<{ id: string; value: string }>;\r\n  }>;\r\n  variants?: Array<{\r\n    id: string;\r\n    title: string;\r\n    prices: Array<{ amount: number; currency_code: string; price_list_id: string | null }>;\r\n    inventory_quantity: number;\r\n    options?: Array<{ option_id: string; value: string }>;\r\n  }>;\r\n  collection?: { title: string; handle: string };\r\n  // Add other fields as needed\r\n}\r\n\r\ninterface ProductPageProps {\r\n  params: Promise<{\r\n    handle: string;\r\n  }>;\r\n}\r\n\r\n// Helper to format price\r\nconst formatPrice = (amount?: number, currencyCode?: string) => {\r\n  if (amount === undefined || currencyCode === undefined) return \"Price unavailable\";\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: currencyCode,\r\n    minimumFractionDigits: 0, // Assuming prices are whole numbers if they end in 00\r\n    maximumFractionDigits: 2,\r\n  }).format(amount / 100); // Medusa prices are in smallest unit\r\n};\r\n\r\nexport default async function ProductPage({ params }: ProductPageProps) {\r\n  const { handle } = await params;\r\n  let product: MedusaProductDetail | null = null;\r\n\r\n  console.log(`PDP: Fetching product with handle: ${handle}`);\r\n\r\n  try {\r\n    // Step 1: Fetch product by handle to get its ID, ensuring it's in the correct sales channel\r\n    const { products } = await medusaClient.products.list({ \r\n      handle, \r\n      sales_channel_id: [\"sc_01JXEB1SMN7PBE8Y93SSSQWGNS\"] \r\n    });\r\n    console.log(`PDP: Initial fetch by handle '${handle}' (Sales Channel sc_01JXEB1SMN7PBE8Y93SSSQWGNS):`, JSON.stringify(products, null, 2));\r\n\r\n    if (products && products.length > 0 && products[0].id) {\r\n      const productId = products[0].id;\r\n      console.log(`PDP: Found product ID: ${productId}. Retrieving full details...`);\r\n      // Step 2: Retrieve the full product details by its ID\r\n      const detailedProductResponse = await medusaClient.products.retrieve(productId);\r\n      product = detailedProductResponse.product as MedusaProductDetail;\r\n      console.log(`PDP: Retrieved detailed product:`, JSON.stringify(product, null, 2));\r\n    } else {\r\n      console.log(`PDP: Product with handle '${handle}' not found by list endpoint or product has no ID.`);\r\n      notFound();\r\n    }\r\n  } catch (error) {\r\n    console.error(`PDP: Error during fetch for product with handle ${handle}:`, error);\r\n    notFound();\r\n  }\r\n\r\n  if (!product) {\r\n    // Should be caught above, but as a safeguard.\r\n    console.log(`PDP: Product object is unexpectedly null for handle '${handle}'.`);\r\n    notFound();\r\n  }\r\n\r\n  const brandName = product.collection?.title || product.subtitle || \"Brand\"; // Use collection title or subtitle as brand\r\n  const firstVariant = product.variants?.[0];\r\n  const price = formatPrice(firstVariant?.prices?.[0]?.amount, firstVariant?.prices?.[0]?.currency_code);\r\n\r\n  return (\r\n    <div className=\"bg-white text-black py-8\"> {/* Main text to black */}\r\n      <div className=\"container mx-auto px-4 lg:px-8\">\r\n        {/* Breadcrumbs (simplified) */}\r\n        <div className=\"text-sm text-gray-600 mb-6\"> {/* Slightly darker gray for breadcrumbs */}\r\n          <Link href=\"/\" className=\"hover:underline text-gray-700\">Home</Link>\r\n          <span className=\"mx-2\">/</span>\r\n          {/* Ideally, add category breadcrumbs here */}\r\n          <span className=\"font-medium text-black\">{product.title}</span> {/* Product title in breadcrumb black */}\r\n        </div>\r\n\r\n        <div className=\"lg:grid lg:grid-cols-2 lg:gap-12 items-start\">\r\n          {/* Image Section (Left Column on Desktop) */}\r\n          <div className=\"mb-8 lg:mb-0\">\r\n            {product.thumbnail ? (\r\n              <div className=\"relative bg-gray-50 w-[400px] h-[533px]\"> {/* Fixed W/H, approx 3:4 ratio for testing */}\r\n                <Image\r\n                  src={product.thumbnail}\r\n                  alt={product.title || \"Product image\"}\r\n                  fill={true} \r\n                  className=\"object-contain w-full h-full\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"w-[400px] h-[533px] bg-gray-50 flex items-center justify-center text-gray-500\"> {/* Fixed W/H for placeholder */}\r\n                No Image Available\r\n              </div>\r\n            )}\r\n            {/* Placeholder for thumbnails/gallery controls */}\r\n          </div>\r\n\r\n          {/* Product Info Section (Right Column on Desktop) */}\r\n          <div className=\"sticky top-24\"> {/* Sticky for scrolling alongside images if gallery is long */}\r\n            <p className=\"text-xs uppercase tracking-wider text-black mb-1 font-semibold\">EXCLUSIVE</p> {/* Re-added and styled EXCLUSIVE badge */}\r\n            <h2 className=\"text-2xl font-semibold mb-1 text-black\">{brandName}</h2>\r\n            <h1 className=\"text-xl text-gray-800 mb-3\">{product.title}</h1> {/* Slightly lighter for title if brand is full black */}\r\n            <p className=\"text-2xl font-medium text-black mb-6\">{price}</p>\r\n            \r\n            {/* Variant Selection (Simplified) */}\r\n            {product.options && product.options.map(option => (\r\n              <div key={option.id} className=\"mb-4\">\r\n                <label htmlFor={`option-${option.id}`} className=\"block text-sm font-medium text-black mb-1\">\r\n                  {option.title}:\r\n                </label>\r\n                <select \r\n                  id={`option-${option.id}`} \r\n                  name={`option-${option.id}`}\r\n                  className=\"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-400 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-none\" // No rounded corners, black focus\r\n                >\r\n                  {option.values.map(value => (\r\n                    <option key={value.id} value={value.id}>{value.value}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            ))}\r\n\r\n            <button \r\n              type=\"button\"\r\n              className=\"w-full bg-black text-white py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-white hover:text-black border border-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black mb-3 transition-colors duration-150\" // Inverted hover\r\n            >\r\n              ADD TO BAG\r\n            </button>\r\n            <button \r\n              type=\"button\"\r\n              className=\"w-full border border-black text-black py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black flex items-center justify-center transition-colors duration-150\"\r\n            >\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n              ADD TO FAVORITES\r\n            </button>\r\n\r\n            {/* Collapsible Sections Placeholder */}\r\n            <div className=\"mt-8 space-y-4\">\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-black border-b border-gray-300 pb-2\">EDITOR&apos;S NOTE</h3> {/* Black heading, lighter border */}\r\n                <p className=\"text-sm text-gray-700 mt-2\">{product.description || \"Detailed editor&apos;s note about this exclusive piece...\"}</p>\r\n              </div>\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-black border-b border-gray-300 pb-2\">PRODUCT DETAILS</h3> {/* Black heading, lighter border */}\r\n                <ul className=\"list-disc list-inside text-sm text-gray-700 mt-2 space-y-1\">\r\n                  <li>Spring &apos;25 Collection</li>\r\n                  <li>Composition: Example Material</li>\r\n                  <li>Imported</li>\r\n                  <li>Product Code: {product.id}</li>\r\n                </ul>\r\n              </div>\r\n              {/* Add Size & Fit, Shipping & Returns similarly */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Optional: Generate static paths\r\n// export async function generateStaticParams() {\r\n//   try {\r\n//     const { products } = await medusaClient.products.list({ limit: 10, sales_channel_id: [\"sc_01JXEB1SMN7PBE8Y93SSSQWGNS\"] });\r\n//     return products.map((product) => ({\r\n//       handle: product.handle!,\r\n//     }));\r\n//   } catch (error) {\r\n//     console.error(\"Failed to generate static params for products:\", error);\r\n//     return [];\r\n//   }\r\n// }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA,8QAA8B,iCAAiC;;;;;;AAiC/D,yBAAyB;AACzB,MAAM,cAAc,CAAC,QAAiB;IACpC,IAAI,WAAW,aAAa,iBAAiB,WAAW,OAAO;IAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS,MAAM,qCAAqC;AAChE;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,IAAI,UAAsC;IAE1C,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ;IAE1D,IAAI;QACF,4FAA4F;QAC5F,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,6GAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YACpD;YACA,kBAAkB;gBAAC;aAAgC;QACrD;QACA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,gDAAgD,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU,MAAM;QAEtI,IAAI,YAAY,SAAS,MAAM,GAAG,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE;YACrD,MAAM,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE;YAChC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,4BAA4B,CAAC;YAC7E,sDAAsD;YACtD,MAAM,0BAA0B,MAAM,6GAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrE,UAAU,wBAAwB,OAAO;YACzC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM;QAChF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,kDAAkD,CAAC;YACnG,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,OAAO,CAAC,CAAC,EAAE;QAC5E,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,OAAO,EAAE,CAAC;QAC9E,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,YAAY,QAAQ,UAAU,EAAE,SAAS,QAAQ,QAAQ,IAAI,SAAS,4CAA4C;IACxH,MAAM,eAAe,QAAQ,QAAQ,EAAE,CAAC,EAAE;IAC1C,MAAM,QAAQ,YAAY,cAAc,QAAQ,CAAC,EAAE,EAAE,QAAQ,cAAc,QAAQ,CAAC,EAAE,EAAE;IAExF,qBACE,8OAAC;QAAI,WAAU;;YAA2B;0BACxC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;4BAA6B;0CAC1C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAgC;;;;;;0CACzD,8OAAC;gCAAK,WAAU;0CAAO;;;;;;0CAEvB,8OAAC;gCAAK,WAAU;0CAA0B,QAAQ,KAAK;;;;;;4BAAQ;;;;;;;kCAGjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,iBAChB,8OAAC;oCAAI,WAAU;;wCAA0C;sDACvD,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,SAAS;4CACtB,KAAK,QAAQ,KAAK,IAAI;4CACtB,MAAM;4CACN,WAAU;;;;;;;;;;;yDAId,8OAAC;oCAAI,WAAU;;wCAAgF;wCAAkC;;;;;;;;;;;;0CAQrI,8OAAC;gCAAI,WAAU;;oCAAgB;kDAC7B,8OAAC;wCAAE,WAAU;kDAAiE;;;;;;oCAAa;kDAC3F,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDAA8B,QAAQ,KAAK;;;;;;oCAAM;kDAC/D,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;oCAGpD,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAA,uBACtC,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAM,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oDAAE,WAAU;;wDAC9C,OAAO,KAAK;wDAAC;;;;;;;8DAEhB,8OAAC;oDACC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oDACzB,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oDAC3B,WAAU,6IAA6I,kCAAkC;;8DAExL,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,sBACjB,8OAAC;4DAAsB,OAAO,MAAM,EAAE;sEAAG,MAAM,KAAK;2DAAvC,MAAM,EAAE;;;;;;;;;;;2CAVjB,OAAO,EAAE;;;;;kDAgBrB,8OAAC;wCACC,MAAK;wCACL,WAAU,iPAAiP,iBAAiB;;kDAC7Q;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,SAAQ;gDAAY,MAAK;0DACxF,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAgH,UAAS;;;;;;;;;;;4CAChJ;;;;;;;kDAKR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+D;;;;;;oDAAuB;kEACpG,8OAAC;wDAAE,WAAU;kEAA8B,QAAQ,WAAW,IAAI;;;;;;;;;;;;0DAEpE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+D;;;;;;oDAAoB;kEACjG,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;;oEAAG;oEAAe,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C,EAEA,kCAAkC;CAClC,iDAAiD;CACjD,UAAU;CACV,iIAAiI;CACjI,0CAA0C;CAC1C,iCAAiC;CACjC,WAAW;CACX,sBAAsB;CACtB,8EAA8E;CAC9E,iBAAiB;CACjB,MAAM;CACN,IAAI", "debugId": null}}]}