(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[946],{5900:(e,a,t)=>{Promise.resolve().then(t.bind(t,5972))},5972:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var n=t(5155),s=t(1855);function r(){let e=["XS","S","M","L","XL"],a=["Black","White","Blue","Red","Green"],t=Array.from({length:24},(t,n)=>{let s=a.slice(0,2).map((a,t)=>({color:a,images:["https://picsum.photos/seed/womens-".concat(n,"-").concat(t,"-1/600/800"),"https://picsum.photos/seed/womens-".concat(n,"-").concat(t,"-2/600/800"),"https://picsum.photos/seed/womens-".concat(n,"-").concat(t,"-3/600/800")],sizes:e.filter(()=>Math.random()>.3)}));return{id:n+1,name:"Designer Item ".concat(n+1),brand:["Gucci","Saint Laurent","Bottega Veneta","The Row","Khaite"][n%5],price:Math.floor(2e3*Math.random())+200,originalPrice:Math.floor(2e3*Math.random())+200,isOnSale:Math.random()>.7,category:["Dresses","Coats","Tops","Trousers","Knitwear"][n%5],href:"/womens/product/".concat(n+1),variants:s}});return(0,n.jsx)(s.A,{title:"Women's Clothing",categories:["Shop all","Activewear","Beachwear","Bridal","Cardigans","Coats","Denim","Dresses","Jackets","Jeans","Jumpsuits","Knitwear","Lingerie and nightwear","Loungewear","Matching sets","Skirts","Suits","Swimwear","Tops","Trousers"],designers:["ALA\xcfA","Alexander McQueen","Balenciaga","Bottega Veneta","Dolce & Gabbana","Erdem","Gabriela Hearst","Gucci","Isabel Marant","Jacquemus","Khaite","LOEWE","Max Mara","Moncler","Saint Laurent","The Row","Toteme","Valentino Garavani","Zimmermann"],initialProducts:t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[155,96,855,358],()=>a(5900)),_N_E=e.O()}]);