import React from 'react';

interface CategoryGridTemplateProps {
  children: React.ReactNode;
}

const CategoryGridTemplate: React.FC<CategoryGridTemplateProps> = ({ children }) => {
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <aside className="lg:col-span-1">
          {/* Sidebar content will be rendered here by the page renderer */}
        </aside>
        <main className="lg:col-span-3">
          {children}
        </main>
      </div>
    </div>
  );
};

export default CategoryGridTemplate;
