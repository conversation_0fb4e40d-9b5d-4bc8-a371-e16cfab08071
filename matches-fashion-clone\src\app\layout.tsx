import type { Metadata } from "next";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import QCTestingPanel from "@/components/QCTestingPanel";
import LoadingIndicator from "@/components/LoadingIndicator";
import ErrorBoundary from "@/components/ErrorBoundary";
import { AuthProvider } from "@/context/AuthContext";
import { AccountProvider } from "@/context/AccountContext";
import { CartProvider } from "@/context/CartContext";
// Environment validation removed to avoid import issues during build

export const metadata: Metadata = {
  title: "Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK",
  description: "Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands.",
  keywords: "luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion",
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body className="min-h-screen flex flex-col antialiased overflow-x-hidden">
        <ErrorBoundary>
          <AuthProvider>
            <AccountProvider>
              <CartProvider>
                <LoadingIndicator />
                <Header />
                <main className="flex-1">
                  <ErrorBoundary>
                    {children}
                  </ErrorBoundary>
                </main>
                <Footer />
                <QCTestingPanel />
              </CartProvider>
            </AccountProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
