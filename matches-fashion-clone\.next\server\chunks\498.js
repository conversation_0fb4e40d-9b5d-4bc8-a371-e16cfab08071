"use strict";exports.id=498,exports.ids=[498],exports.modules={55498:(e,s,a)=>{a.d(s,{A:()=>h});var r=a(60687),t=a(43210),l=a(80462),c=a(78272),i=a(11860),n=a(83844),d=a(32036),o=a(13372);function x({product:e,name:s,price:a,colorHex:l,href:c,variant:i}){let[x,m]=(0,t.useState)(!1),{addItem:h}=(0,n._)();return(0,r.jsxs)("div",{className:"relative group",onMouseEnter:()=>{},onMouseLeave:()=>{},children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.RC,{modules:[o.Vx,o.dK],navigation:{enabled:!0},pagination:{clickable:!0},loop:!0,className:"swiper-product",children:i.images.map((e,s)=>(0,r.jsx)(d.qr,{children:(0,r.jsx)("a",{href:c,children:(0,r.jsx)("img",{src:e,alt:`Image ${s+1}`,className:"w-full object-cover",loading:"lazy"})})},s))}),(0,r.jsx)("button",{onClick:()=>m(!x),className:"absolute bottom-2 right-2 bg-white p-1 rounded-full shadow-md block lg:hidden z-10",children:"\uD83D\uDED2"}),x&&(0,r.jsx)("div",{className:"absolute inset-x-2 bottom-2 bg-white shadow-md flex flex-wrap justify-center gap-1 p-2 z-20",children:i.sizes.map(s=>(0,r.jsx)("button",{className:"border px-2 py-1 text-xs md:text-sm hover:bg-black hover:text-white",onClick:()=>{h(e,i,s),m(!1)},children:s},s))})]}),(0,r.jsxs)("div",{className:"pt-3 text-center",children:[(0,r.jsx)("h3",{className:"text-sm uppercase",children:s}),(0,r.jsxs)("p",{className:"text-sm font-semibold",children:["₫ ",a.toLocaleString()]}),(0,r.jsx)("div",{className:"flex justify-center mt-2",children:(0,r.jsx)("span",{className:"w-4 h-4 border border-gray-300 rounded-full",style:{backgroundColor:l}})})]})]})}a(2822),a(55866),a(84120);var m=a(51933);let h=({title:e,categories:s,designers:a,colors:n=["Black","White","Blue","Red","Green"],initialProducts:d=[],query:o})=>{let[h,p]=(0,t.useState)(d),[u,b]=(0,t.useState)("newest"),[j,g]=(0,t.useState)(!1),[N,v]=(0,t.useState)([]),[f,y]=(0,t.useState)([]),[w,k]=(0,t.useState)([]),[C,S]=(0,t.useState)([]),[L,P]=(0,t.useState)([0,5e3]);(0,t.useEffect)(()=>{(async()=>{if(o&&process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT)try{let e=await fetch(process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:o})}),s=await e.json();s.data&&Array.isArray(s.data.products)&&p(s.data.products)}catch(e){console.error("Failed to fetch products",e)}})()},[o]);let z=[...h.filter(e=>{let s=0===N.length||N.includes(e.category),a=0===f.length||f.includes(e.brand),r=0===w.length||e.variants.some(e=>w.some(s=>e.sizes.includes(s))),t=0===C.length||e.variants.some(e=>C.includes(e.color)),l=e.price>=L[0]&&e.price<=L[1];return s&&a&&r&&t&&l})].sort((e,s)=>"price-low"===u?e.price-s.price:"price-high"===u?s.price-e.price:0);return(0,r.jsxs)("div",{className:"bg-white min-h-screen",children:[(0,r.jsx)("div",{className:"h-40 md:h-60 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center",children:(0,r.jsx)("h1",{className:"text-3xl font-bold uppercase",children:e})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between py-4 border-b",children:[(0,r.jsxs)("button",{className:"flex items-center space-x-2 text-sm uppercase",onClick:()=>g(!0),children:[(0,r.jsx)(l.A,{size:16}),(0,r.jsx)("span",{children:"Filter"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:u,onChange:e=>b(e.target.value),className:"appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black",children:[(0,r.jsx)("option",{value:"newest",children:"Newest"}),(0,r.jsx)("option",{value:"price-low",children:"Price: Low to High"}),(0,r.jsx)("option",{value:"price-high",children:"Price: High to Low"}),(0,r.jsx)("option",{value:"popular",children:"Most Popular"})]}),(0,r.jsx)(c.A,{size:16,className:"absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none"})]})]}),(0,r.jsx)("div",{className:"grid gap-6 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 py-6",children:z.map(e=>(0,r.jsx)(x,{product:e,name:e.name,price:e.price,href:e.href,colorHex:e.variants[0]?.color||"transparent",variant:e.variants[0]},e.id))}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsx)("button",{className:"luxury-button-outline",children:"Load More"})})]}),j&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50",onClick:()=>g(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-white p-6 overflow-y-auto",children:[(0,r.jsxs)("button",{className:"flex items-center space-x-1 mb-4 text-sm uppercase",onClick:()=>g(!1),children:[(0,r.jsx)(i.A,{size:16}),(0,r.jsx)("span",{children:"Close"})]}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Categories"}),(0,r.jsx)("ul",{className:"space-y-2",children:s.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{id:`drawer-cat-${s}`,type:"checkbox",checked:N.includes(e),onChange:()=>v(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,r.jsx)("label",{htmlFor:`drawer-cat-${s}`,className:"text-sm text-gray-600",children:e})]},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Designers"}),(0,r.jsx)("ul",{className:"space-y-2 max-h-48 overflow-y-auto pr-2",children:a.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{id:`drawer-designer-${s}`,type:"checkbox",checked:f.includes(e),onChange:()=>y(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,r.jsx)("label",{htmlFor:`drawer-designer-${s}`,className:"text-sm text-gray-600",children:e})]},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Size"}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-2",children:["XS","S","M","L","XL"].map(e=>(0,r.jsx)("button",{onClick:()=>k(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:`border py-2 text-sm transition-colors ${w.includes(e)?"border-black bg-black text-white":"border-gray-300 hover:border-black"}`,children:e},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Color"}),(0,r.jsx)("ul",{className:"space-y-2",children:n.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{id:`drawer-color-${s}`,type:"checkbox",checked:C.includes(e),onChange:()=>S(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,r.jsxs)("label",{htmlFor:`drawer-color-${s}`,className:"text-sm text-gray-600 flex items-center space-x-1",children:[(0,r.jsx)("span",{className:"w-3 h-3 inline-block border",style:{backgroundColor:e.toLowerCase()}}),(0,r.jsx)("span",{children:e})]})]},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Price"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"number",min:0,max:5e3,value:L[0],onChange:e=>{let s=Number(e.target.value);P(e=>[Math.min(s,e[1]),e[1]])},className:"w-full border px-2 py-1 text-sm"}),(0,r.jsx)("input",{type:"number",min:0,max:5e3,value:L[1],onChange:e=>{let s=Number(e.target.value);P(e=>[e[0],Math.max(s,e[0])])},className:"w-full border px-2 py-1 text-sm"})]}),(0,r.jsx)(m.Range,{values:L,step:10,min:0,max:5e3,onChange:e=>P([e[0],e[1]]),renderTrack:({props:e,children:s})=>(0,r.jsx)("div",{...e,className:"w-full h-2 rounded bg-gray-200",style:{...e.style,background:(0,m.getTrackBackground)({values:L,colors:["#d1d5db","#000","#d1d5db"],min:0,max:5e3})},children:s}),renderThumb:({props:e})=>(0,r.jsx)("div",{...e,className:"w-4 h-4 bg-white border border-gray-400 rounded-full focus:outline-none"})})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["\xa3",L[0]," - \xa3",L[1]]})]})]})]})]})]})}}};