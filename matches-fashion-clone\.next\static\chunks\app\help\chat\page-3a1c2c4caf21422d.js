(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[743],{2158:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,9482))},9482:(e,r,s)=>{"use strict";s.d(r,{default:()=>a});var t=s(5155),l=s(2115);function a(){let[e,r]=(0,l.useState)([{sender:"support",text:"Hi! How can we help you today?"}]),[s,a]=(0,l.useState)(""),n=(0,l.useRef)(null);return(0,l.useEffect)(()=>{var e;null==(e=n.current)||e.scrollIntoView({behavior:"smooth"})},[e]),(0,t.jsxs)("div",{className:"flex flex-col h-[500px] border border-gray-300 rounded",children:[(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:[e.map((e,r)=>(0,t.jsx)("div",{className:"user"===e.sender?"text-right":"text-left",children:(0,t.jsx)("span",{className:"inline-block px-4 py-2 rounded ".concat("user"===e.sender?"bg-black text-white":"bg-gray-100 text-gray-800"),children:e.text})},r)),(0,t.jsx)("div",{ref:n})]}),(0,t.jsxs)("div",{className:"p-2 border-t flex space-x-2",children:[(0,t.jsx)("input",{value:s,onChange:e=>a(e.target.value),className:"flex-1 border border-gray-300 p-2",placeholder:"Type a message..."}),(0,t.jsx)("button",{onClick:()=>{if(!s.trim())return;let e={sender:"user",text:s.trim()};r(r=>[...r,e,{sender:"support",text:"Thank you for your message. We'll reply soon."}]),a("")},className:"luxury-button",children:"Send"})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[96,358],()=>r(2158)),_N_E=e.O()}]);