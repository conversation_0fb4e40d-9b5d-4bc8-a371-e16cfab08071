import React from 'react';
import Link from 'next/link';

interface ContentBlockProps {
  data: {
    title?: string;
    content?: string;
    image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    cta_text?: string;
    cta_link?: string;
    layout?: 'text-only' | 'image-left' | 'image-right' | 'image-top';
    background_color?: string;
    text_color?: string;
  };
  context: 'main' | 'sidebar';
}

const ContentBlock: React.FC<ContentBlockProps> = ({ data, context }) => {
  const {
    title,
    content,
    image,
    cta_text,
    cta_link,
    layout = 'text-only',
    background_color,
    text_color
  } = data;

  const imageUrl = image?.data?.attributes?.url;
  const altText = image?.data?.attributes?.alternativeText || title || 'Content image';

  const containerClasses = `
    ${context === 'sidebar' ? 'p-4' : 'p-6 md:p-8'}
    ${background_color ? '' : 'bg-white'}
    ${text_color ? '' : 'text-gray-900'}
  `;

  const style: React.CSSProperties = {
    ...(background_color && { backgroundColor: background_color }),
    ...(text_color && { color: text_color })
  };

  const renderContent = () => (
    <div className="space-y-4">
      {title && (
        <h2 className={`font-bold ${context === 'sidebar' ? 'text-lg' : 'text-2xl md:text-3xl'}`}>
          {title}
        </h2>
      )}
      
      {content && (
        <div 
          className={`prose ${context === 'sidebar' ? 'prose-sm' : 'prose-lg'} max-w-none`}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
      
      {cta_text && cta_link && (
        <Link
          href={cta_link}
          className="inline-block px-6 py-3 bg-black text-white font-medium uppercase tracking-wide hover:bg-gray-800 transition-colors duration-300"
        >
          {cta_text}
        </Link>
      )}
    </div>
  );

  const renderImage = () => (
    imageUrl && (
      <div className={`${context === 'sidebar' ? 'aspect-video' : 'aspect-[4/3]'} overflow-hidden`}>
        <img
          src={imageUrl}
          alt={altText}
          className="w-full h-full object-cover"
        />
      </div>
    )
  );

  if (layout === 'text-only' || !imageUrl) {
    return (
      <div className={containerClasses} style={style}>
        {renderContent()}
      </div>
    );
  }

  if (layout === 'image-top') {
    return (
      <div className={containerClasses} style={style}>
        {renderImage()}
        <div className="mt-6">
          {renderContent()}
        </div>
      </div>
    );
  }

  if (layout === 'image-left' || layout === 'image-right') {
    return (
      <div className={containerClasses} style={style}>
        <div className={`grid grid-cols-1 ${context === 'sidebar' ? 'gap-4' : 'md:grid-cols-2 gap-6 md:gap-8'} items-center`}>
          {layout === 'image-left' && (
            <>
              {renderImage()}
              {renderContent()}
            </>
          )}
          {layout === 'image-right' && (
            <>
              {renderContent()}
              {renderImage()}
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses} style={style}>
      {renderContent()}
    </div>
  );
};

export default ContentBlock;
