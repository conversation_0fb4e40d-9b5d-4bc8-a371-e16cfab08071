# ===========================================
# MATCHES FASHION CLONE - ENVIRONMENT VARIABLES
# ===========================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control

# ===========================================
# MEDUSA BACKEND CONFIGURATION
# ===========================================
# URL of your Medusa backend server
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000

# Publishable API key for Medusa (optional but recommended for production)
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY=your_medusa_publishable_key_here

# ===========================================
# STRAPI CMS CONFIGURATION
# ===========================================
# URL of your Strapi CMS server
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337

# Strapi API token for server-side requests (optional)
# Note: This is a server-side only variable (no NEXT_PUBLIC prefix)
STRAPI_API_TOKEN=your_strapi_api_token_here

# ===========================================
# NEXT.JS CONFIGURATION
# ===========================================
# Environment (development, staging, production)
NODE_ENV=development

# Base URL of your application (used for absolute URLs)
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# ===========================================
# ANALYTICS & TRACKING (Optional)
# ===========================================
# Google Analytics tracking ID
NEXT_PUBLIC_GA_TRACKING_ID=your_ga_tracking_id_here

# Google Tag Manager ID
NEXT_PUBLIC_GTM_ID=your_gtm_id_here

# Facebook Pixel ID
NEXT_PUBLIC_FACEBOOK_PIXEL_ID=your_facebook_pixel_id_here

# ===========================================
# ERROR TRACKING (Optional)
# ===========================================
# Sentry DSN for error tracking
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here

# Sentry environment
SENTRY_ENVIRONMENT=development

# ===========================================
# EMAIL CONFIGURATION (Optional)
# ===========================================
# SMTP configuration for contact forms
SMTP_HOST=your_smtp_host_here
SMTP_PORT=587
SMTP_USER=your_smtp_username_here
SMTP_PASS=your_smtp_password_here
SMTP_FROM=<EMAIL>

# ===========================================
# SEARCH CONFIGURATION (Optional)
# ===========================================
# Algolia configuration for search functionality
NEXT_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id_here
NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY=your_algolia_search_key_here
ALGOLIA_ADMIN_API_KEY=your_algolia_admin_key_here

# ===========================================
# PAYMENT CONFIGURATION (Optional)
# ===========================================
# Stripe configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# PayPal configuration
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here

# ===========================================
# STORAGE CONFIGURATION (Optional)
# ===========================================
# AWS S3 configuration for file uploads
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket_name_here

# Cloudinary configuration for image optimization
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

# ===========================================
# REDIS CONFIGURATION (Optional)
# ===========================================
# Redis URL for caching and sessions
REDIS_URL=redis://localhost:6379

# ===========================================
# DATABASE CONFIGURATION (Optional)
# ===========================================
# Database URL (if using direct database connections)
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
# Secret key for JWT tokens and encryption
NEXTAUTH_SECRET=your_nextauth_secret_here

# URL for NextAuth.js
NEXTAUTH_URL=http://localhost:3000

# ===========================================
# FEATURE FLAGS (Optional)
# ===========================================
# Enable/disable features
NEXT_PUBLIC_ENABLE_CHAT_WIDGET=true
NEXT_PUBLIC_ENABLE_WISHLIST=true
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_RECOMMENDATIONS=true

# ===========================================
# DEVELOPMENT TOOLS
# ===========================================
# Enable bundle analyzer
ANALYZE=false

# Enable verbose logging
VERBOSE_LOGGING=false

# ===========================================
# RATE LIMITING (Optional)
# ===========================================
# Rate limiting configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# ===========================================
# CORS CONFIGURATION (Optional)
# ===========================================
# Allowed origins for CORS
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# ===========================================
# NOTES
# ===========================================
# 1. Variables with NEXT_PUBLIC_ prefix are exposed to the browser
# 2. Variables without NEXT_PUBLIC_ are server-side only
# 3. Always use strong, unique values for secrets in production
# 4. Consider using a secret management service for production
# 5. Update CORS and security settings for production deployment
