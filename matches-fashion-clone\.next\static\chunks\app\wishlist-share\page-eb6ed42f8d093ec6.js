(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[238],{3934:(e,s,a)=>{Promise.resolve().then(a.bind(a,6696))},6696:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r,dynamic:()=>n});var l=a(5155),t=a(5695),i=a(2115);let n="force-dynamic";function c(){let e=(0,t.useSearchParams)(),[s,a]=(0,i.useState)(null),[n,c]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let s=e.get("data");if(!s)return void c(!0);try{let e=JSON.parse(atob(s));a(e)}catch(e){c(!0)}},[e]),n)?(0,l.jsx)("p",{className:"p-10",children:"Invalid share link."}):s?(0,l.jsxs)("div",{className:"max-w-xl mx-auto py-10 space-y-4",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:s.name}),s.note&&(0,l.jsx)("p",{className:"italic",children:s.note}),0===s.items.length?(0,l.jsx)("p",{children:"No items."}):(0,l.jsx)("ul",{className:"list-disc list-inside space-y-1",children:s.items.map((e,s)=>(0,l.jsx)("li",{children:e.title},s))})]}):(0,l.jsx)("p",{className:"p-10",children:"Loading..."})}function r(){return(0,l.jsx)(i.Suspense,{fallback:(0,l.jsx)("p",{className:"p-10",children:"Loading..."}),children:(0,l.jsx)(c,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[96,358],()=>s(3934)),_N_E=e.O()}]);