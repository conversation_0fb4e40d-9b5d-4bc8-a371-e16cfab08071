import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { pageManager, generatePageMetadata, StrapiPage } from '../../../lib/page-manager';
import DynamicPageRenderer from '@/components/DynamicPageRenderer';

interface DynamicPageProps {
  params: Promise<{
    slug: string[];
  }>;
}

// Generate metadata for the page
export async function generateMetadata({ params }: DynamicPageProps): Promise<Metadata> {
  const { slug } = await params;
  const slugString = slug.join('/');
  
  try {
    const page = await pageManager.getPageBySlug(slugString);
    
    if (!page) {
      return {
        title: 'Page Not Found - Matches Fashion',
        description: 'The requested page could not be found.',
      };
    }

    return generatePageMetadata(page);
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Matches Fashion',
      description: 'Luxury fashion and designer clothing',
    };
  }
}

// Main page component
export default async function DynamicPage({ params }: DynamicPageProps) {
  const { slug } = await params;
  const slugString = slug.join('/');

  try {
    const page = await pageManager.getPageBySlug(slugString);
    
    if (!page) {
      notFound();
    }

    return <DynamicPageRenderer page={page} />;
  } catch (error) {
    console.error('Error rendering dynamic page:', error);
    notFound();
  }
}

// Generate static params for known pages (optional, for better performance)
export async function generateStaticParams() {
  try {
    const pages = await pageManager.getAllPages();
    
    return pages.map((page) => ({
      slug: page.attributes.slug.split('/').filter(Boolean),
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}
