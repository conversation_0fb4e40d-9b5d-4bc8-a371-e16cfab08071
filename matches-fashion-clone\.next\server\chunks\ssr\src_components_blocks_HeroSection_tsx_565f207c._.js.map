{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/blocks/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n\ninterface HeroSectionProps {\n  data: {\n    title?: string;\n    subtitle?: string;\n    description?: string;\n    background_image?: {\n      data?: {\n        attributes: {\n          url: string;\n          alternativeText?: string;\n        };\n      };\n    };\n    cta_text?: string;\n    cta_link?: string;\n    overlay_opacity?: number;\n    text_color?: 'white' | 'black';\n    text_alignment?: 'left' | 'center' | 'right';\n  };\n  context: 'main' | 'sidebar';\n}\n\nconst HeroSection: React.FC<HeroSectionProps> = ({ data, context }) => {\n  const {\n    title,\n    subtitle,\n    description,\n    background_image,\n    cta_text,\n    cta_link,\n    overlay_opacity = 40,\n    text_color = 'white',\n    text_alignment = 'center'\n  } = data;\n\n  const backgroundImageUrl = background_image?.data?.attributes?.url;\n  const altText = background_image?.data?.attributes?.alternativeText || title || 'Hero image';\n\n  const textAlignmentClasses = {\n    left: 'text-left',\n    center: 'text-center',\n    right: 'text-right'\n  };\n\n  const textColorClasses = {\n    white: 'text-white',\n    black: 'text-black'\n  };\n\n  return (\n    <section className={`relative ${context === 'sidebar' ? 'h-64' : 'h-96 lg:h-[70vh]'} overflow-hidden`}>\n      {/* Background Image */}\n      {backgroundImageUrl ? (\n        <div \n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n          style={{ backgroundImage: `url(${backgroundImageUrl})` }}\n          role=\"img\"\n          aria-label={altText}\n        />\n      ) : (\n        <div className=\"absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400\" />\n      )}\n\n      {/* Overlay */}\n      <div \n        className=\"absolute inset-0 bg-black\"\n        style={{ opacity: overlay_opacity / 100 }}\n      />\n\n      {/* Content */}\n      <div className={`relative z-10 h-full flex items-center justify-center px-4 ${textAlignmentClasses[text_alignment]}`}>\n        <div className=\"max-w-4xl mx-auto\">\n          {subtitle && (\n            <p className={`text-sm md:text-base uppercase tracking-wide mb-2 md:mb-4 ${textColorClasses[text_color]} opacity-90`}>\n              {subtitle}\n            </p>\n          )}\n          \n          {title && (\n            <h1 className={`text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 ${textColorClasses[text_color]}`}>\n              {title}\n            </h1>\n          )}\n          \n          {description && (\n            <p className={`text-base md:text-lg mb-6 md:mb-8 ${textColorClasses[text_color]} opacity-90 max-w-2xl ${text_alignment === 'center' ? 'mx-auto' : ''}`}>\n              {description}\n            </p>\n          )}\n          \n          {cta_text && cta_link && (\n            <Link\n              href={cta_link}\n              className={`inline-block px-6 md:px-8 py-3 md:py-4 border-2 font-medium uppercase tracking-wide transition-all duration-300 hover:scale-105 ${\n                text_color === 'white' \n                  ? 'border-white text-white hover:bg-white hover:text-black' \n                  : 'border-black text-black hover:bg-black hover:text-white'\n              }`}\n            >\n              {cta_text}\n            </Link>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AACA;;;AAwBA,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IAChE,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAAE,EACpB,aAAa,OAAO,EACpB,iBAAiB,QAAQ,EAC1B,GAAG;IAEJ,MAAM,qBAAqB,kBAAkB,MAAM,YAAY;IAC/D,MAAM,UAAU,kBAAkB,MAAM,YAAY,mBAAmB,SAAS;IAEhF,MAAM,uBAAuB;QAC3B,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAO;QACP,OAAO;IACT;IAEA,qBACE,8OAAC;QAAQ,WAAW,CAAC,SAAS,EAAE,YAAY,YAAY,SAAS,mBAAmB,gBAAgB,CAAC;;YAElG,mCACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBAAC;gBACvD,MAAK;gBACL,cAAY;;;;;qCAGd,8OAAC;gBAAI,WAAU;;;;;;0BAIjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,SAAS,kBAAkB;gBAAI;;;;;;0BAI1C,8OAAC;gBAAI,WAAW,CAAC,2DAA2D,EAAE,oBAAoB,CAAC,eAAe,EAAE;0BAClH,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,0BACC,8OAAC;4BAAE,WAAW,CAAC,0DAA0D,EAAE,gBAAgB,CAAC,WAAW,CAAC,WAAW,CAAC;sCACjH;;;;;;wBAIJ,uBACC,8OAAC;4BAAG,WAAW,CAAC,wDAAwD,EAAE,gBAAgB,CAAC,WAAW,EAAE;sCACrG;;;;;;wBAIJ,6BACC,8OAAC;4BAAE,WAAW,CAAC,kCAAkC,EAAE,gBAAgB,CAAC,WAAW,CAAC,sBAAsB,EAAE,mBAAmB,WAAW,YAAY,IAAI;sCACnJ;;;;;;wBAIJ,YAAY,0BACX,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAW,CAAC,gIAAgI,EAC1I,eAAe,UACX,4DACA,2DACJ;sCAED;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}]}