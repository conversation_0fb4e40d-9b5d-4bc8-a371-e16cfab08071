{"name": "matches-fashion-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@medusajs/medusa-js": "^6.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.512.0", "next": "^15.3.3", "nprogress": "^0.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-range": "^1.10.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.0", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.1"}}