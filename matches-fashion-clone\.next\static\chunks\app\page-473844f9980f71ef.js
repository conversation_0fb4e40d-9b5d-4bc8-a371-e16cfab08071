(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[476,974],{1539:(e,c,s)=>{"use strict";s.d(c,{default:()=>d});var r=s(5155),t=s(9137),a=s.n(t),l=s(2115),o=s(6874),i=s.n(o),n=s(2355),x=s(3052);let d=e=>{let{title:c,subtitle:s,count:t,ctaText:o="Shop Now",ctaHref:d,products:m}=e,[h,f]=(0,l.useState)(!1),[u,p]=(0,l.useState)(!0),j=(0,l.useRef)(null),g=()=>{if(j.current){let{scrollLeft:e,scrollWidth:c,clientWidth:s}=j.current;f(e>0),p(e<c-s-1)}};(0,l.useEffect)(()=>{g();let e=j.current;if(e)return e.addEventListener("scroll",g),()=>e.removeEventListener("scroll",g)},[]);let b=e=>{if(j.current){let c=j.current.scrollLeft+("left"===e?-300:300);j.current.scrollTo({left:c,behavior:"smooth"})}};return(0,r.jsxs)("section",{className:"jsx-7f85ea6c5640cc59 homepage-component component-product-carousel relative bg-white py-4",children:[(0,r.jsxs)("div",{className:"jsx-7f85ea6c5640cc59 product-carousel-wrapper relative",children:[(0,r.jsx)("button",{onClick:()=>b("left"),disabled:!h,"aria-label":"Previous products",className:"jsx-7f85ea6c5640cc59 "+"product-carousel-arrow arrow-prev absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ".concat(h?"cursor-pointer":"opacity-50 cursor-not-allowed"),children:(0,r.jsx)(n.A,{size:16,className:"mx-auto"})}),(0,r.jsx)("div",{ref:j,style:{scrollbarWidth:"none",msOverflowStyle:"none"},className:"jsx-7f85ea6c5640cc59 product-carousel-scroller overflow-x-auto hide-scrollbar px-12",children:(0,r.jsxs)("div",{className:"jsx-7f85ea6c5640cc59 product-carousel-items flex space-x-3 min-w-max",children:[(0,r.jsx)("div",{className:"jsx-7f85ea6c5640cc59 item-card item-wide flex-shrink-0 w-72 h-80 bg-gray-50 flex flex-col justify-center items-center text-center p-6",children:(0,r.jsxs)(i(),{href:d,className:"item-info block",children:[(0,r.jsxs)("h3",{className:"jsx-7f85ea6c5640cc59 mb-4",children:[(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59 homepage-skeleton number block text-3xl font-bold mb-1",children:t}),(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59 homepage-skeleton title block text-sm font-medium uppercase tracking-wide mb-1",children:c}),(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59 homepage-skeleton subtitle block text-xs text-gray-600 uppercase tracking-wide",children:s})]}),(0,r.jsxs)("p",{className:"jsx-7f85ea6c5640cc59 cta homepage-skeleton flex items-center justify-center space-x-1 text-xs font-medium uppercase tracking-wide hover:text-gray-600 transition-colors",children:[(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59",children:o}),(0,r.jsx)("svg",{height:"6",viewBox:"0 0 4 8",width:"3",xmlns:"http://www.w3.org/2000/svg",className:"jsx-7f85ea6c5640cc59 cta-arrow",children:(0,r.jsx)("path",{d:"m0 0 4 4-4 4z",fill:"currentColor",className:"jsx-7f85ea6c5640cc59"})})]})]})}),m.map(e=>(0,r.jsx)(i(),{href:e.href,className:"item-card product-carousel-item flex-shrink-0 w-48 h-80 group cursor-pointer","aria-label":"".concat(e.designer,". ").concat(e.name),children:(0,r.jsxs)("figure",{className:"jsx-7f85ea6c5640cc59 h-full",children:[(0,r.jsx)("div",{className:"jsx-7f85ea6c5640cc59 image-wrapper h-5/6 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden group-hover:opacity-90 transition-opacity",children:(0,r.jsx)("div",{className:"jsx-7f85ea6c5640cc59 w-full h-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59 text-gray-500 text-xs",children:"Product Image"})})}),(0,r.jsx)("figcaption",{className:"jsx-7f85ea6c5640cc59 designer h-1/6 flex items-center justify-center text-xs font-medium text-gray-700 group-hover:text-black transition-colors",children:e.designer})]})},e.id)),(0,r.jsxs)(i(),{href:d,className:"item-card product-carousel-item item-discover flex-shrink-0 w-48 h-80 bg-black text-white flex flex-col justify-center items-center text-center p-6 hover:bg-gray-800 transition-colors",children:[(0,r.jsx)("h3",{className:"jsx-7f85ea6c5640cc59 homepage-skeleton discover-text text-sm font-medium mb-3",children:"Discover the latest arrivals"}),(0,r.jsxs)("p",{className:"jsx-7f85ea6c5640cc59 cta homepage-skeleton flex items-center space-x-1 text-xs font-medium uppercase tracking-wide",children:[(0,r.jsx)("span",{className:"jsx-7f85ea6c5640cc59",children:o}),(0,r.jsx)("svg",{height:"6",viewBox:"0 0 4 8",width:"3",xmlns:"http://www.w3.org/2000/svg",className:"jsx-7f85ea6c5640cc59 cta-arrow",children:(0,r.jsx)("path",{d:"m0 0 4 4-4 4z",fill:"currentColor",className:"jsx-7f85ea6c5640cc59"})})]})]})]})}),(0,r.jsx)("button",{onClick:()=>b("right"),disabled:!u,"aria-label":"Next products",className:"jsx-7f85ea6c5640cc59 "+"product-carousel-arrow arrow-next absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ".concat(u?"cursor-pointer":"opacity-50 cursor-not-allowed"),children:(0,r.jsx)(x.A,{size:16,className:"mx-auto"})})]}),(0,r.jsx)(a(),{id:"7f85ea6c5640cc59",children:".hide-scrollbar.jsx-7f85ea6c5640cc59::-webkit-scrollbar{display:none}"})]})}},6445:(e,c,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,1539))}},e=>{var c=c=>e(e.s=c);e.O(0,[96,358],()=>c(6445)),_N_E=e.O()}]);