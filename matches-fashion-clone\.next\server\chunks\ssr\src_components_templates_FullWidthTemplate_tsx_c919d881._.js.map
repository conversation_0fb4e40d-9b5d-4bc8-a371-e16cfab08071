{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/FullWidthTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\nconst FullWidthTemplate: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <div className=\"w-full\">{children}</div>\n);\n\nexport default FullWidthTemplate;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,oBAA6D,CAAC,EAAE,QAAQ,EAAE,iBAC9E,8OAAC;QAAI,WAAU;kBAAU;;;;;;uCAGZ", "debugId": null}}]}