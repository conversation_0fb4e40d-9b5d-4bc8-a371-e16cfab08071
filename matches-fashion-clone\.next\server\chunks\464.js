exports.id=464,exports.ids=[464],exports.modules={2822:()=>{},2920:function(e,t,i){"use strict";var r,s=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),n=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var s=Object.getOwnPropertyDescriptor(t,i);(!s||("get"in s?!t.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,s)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var i in e)"default"!==i&&Object.prototype.hasOwnProperty.call(e,i)&&n(t,e,i);return a(t,e),t},o=this&&this.__spreadArray||function(e,t,i){if(i||2==arguments.length)for(var r,s=0,n=t.length;s<n;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var d=l(i(43210)),c=i(22728),u=i(29868),p=["ArrowRight","ArrowUp","k","PageUp"],h=["ArrowLeft","ArrowDown","j","PageDown"];t.default=function(e){function t(t){var i=e.call(this,t)||this;if(i.trackRef=d.createRef(),i.thumbRefs=[],i.state={draggedTrackPos:[-1,-1],draggedThumbIndex:-1,thumbZIndexes:Array(i.props.values.length).fill(0).map(function(e,t){return t}),isChanged:!1,markOffsets:[]},i.getOffsets=function(){var e=i.props,t=e.direction,r=e.values,s=e.min,n=e.max,a=i.trackRef.current;if(!a)return console.warn("No track element found."),[];var l=a.getBoundingClientRect(),o=(0,c.getPaddingAndBorder)(a);return i.getThumbs().map(function(e,i){var a={x:0,y:0},d=e.getBoundingClientRect(),p=(0,c.getMargin)(e);switch(t){case u.Direction.Right:return a.x=-((p.left+o.left)*1),a.y=-(((d.height-l.height)/2+o.top)*1),a.x+=l.width*(0,c.relativeValue)(r[i],s,n)-d.width/2,a;case u.Direction.Left:return a.x=-((p.right+o.right)*1),a.y=-(((d.height-l.height)/2+o.top)*1),a.x+=l.width-l.width*(0,c.relativeValue)(r[i],s,n)-d.width/2,a;case u.Direction.Up:return a.x=-(((d.width-l.width)/2+p.left+o.left)*1),a.y=-o.left,a.y+=l.height-l.height*(0,c.relativeValue)(r[i],s,n)-d.height/2,a;case u.Direction.Down:return a.x=-(((d.width-l.width)/2+p.left+o.left)*1),a.y=-o.left,a.y+=l.height*(0,c.relativeValue)(r[i],s,n)-d.height/2,a;default:return(0,c.assertUnreachable)(t)}})},i.getThumbs=function(){return i.trackRef&&i.trackRef.current?Array.from(i.trackRef.current.children).filter(function(e){return e.hasAttribute("aria-valuenow")}):(console.warn("No thumbs found in the track container. Did you forget to pass & spread the `props` param in renderTrack?"),[])},i.getTargetIndex=function(e){return i.getThumbs().findIndex(function(t){return t===e.target||t.contains(e.target)})},i.addTouchEvents=function(e){document.addEventListener("touchmove",i.schdOnTouchMove,{passive:!1}),document.addEventListener("touchend",i.schdOnEnd,{passive:!1}),document.addEventListener("touchcancel",i.schdOnEnd,{passive:!1})},i.addMouseEvents=function(e){document.addEventListener("mousemove",i.schdOnMouseMove),document.addEventListener("mouseup",i.schdOnEnd)},i.onMouseDownTrack=function(e){var t;if(!(0!==e.button||(0,c.isIOS)()))if(e.persist(),e.preventDefault(),i.addMouseEvents(e.nativeEvent),i.props.values.length>1&&i.props.draggableTrack){if(i.thumbRefs.some(function(t){var i;return null==(i=t.current)?void 0:i.contains(e.target)}))return;i.setState({draggedTrackPos:[e.clientX,e.clientY]},function(){return i.onMove(e.clientX,e.clientY)})}else{var r=(0,c.getClosestThumbIndex)(i.thumbRefs.map(function(e){return e.current}),e.clientX,e.clientY,i.props.direction);null==(t=i.thumbRefs[r].current)||t.focus(),i.setState({draggedThumbIndex:r},function(){return i.onMove(e.clientX,e.clientY)})}},i.onResize=function(){(0,c.translateThumbs)(i.getThumbs(),i.getOffsets(),i.props.rtl),i.calculateMarkOffsets()},i.onTouchStartTrack=function(e){var t;if(e.persist(),i.addTouchEvents(e.nativeEvent),i.props.values.length>1&&i.props.draggableTrack){if(i.thumbRefs.some(function(t){var i;return null==(i=t.current)?void 0:i.contains(e.target)}))return;i.setState({draggedTrackPos:[e.touches[0].clientX,e.touches[0].clientY]},function(){return i.onMove(e.touches[0].clientX,e.touches[0].clientY)})}else{var r=(0,c.getClosestThumbIndex)(i.thumbRefs.map(function(e){return e.current}),e.touches[0].clientX,e.touches[0].clientY,i.props.direction);null==(t=i.thumbRefs[r].current)||t.focus(),i.setState({draggedThumbIndex:r},function(){return i.onMove(e.touches[0].clientX,e.touches[0].clientY)})}},i.onMouseOrTouchStart=function(e){if(!i.props.disabled){var t=(0,c.isTouchEvent)(e);if(t||0===e.button){var r=i.getTargetIndex(e);-1!==r&&(t?i.addTouchEvents(e):i.addMouseEvents(e),i.setState({draggedThumbIndex:r,thumbZIndexes:i.state.thumbZIndexes.map(function(e,t){return t===r?Math.max.apply(Math,i.state.thumbZIndexes):e<=i.state.thumbZIndexes[r]?e:e-1})}))}}},i.onMouseMove=function(e){e.preventDefault(),i.onMove(e.clientX,e.clientY)},i.onTouchMove=function(e){e.preventDefault(),i.onMove(e.touches[0].clientX,e.touches[0].clientY)},i.onKeyDown=function(e){var t=i.props,r=t.values,s=t.onChange,n=t.step,a=t.rtl,l=t.direction,o=i.state.isChanged,d=i.getTargetIndex(e.nativeEvent),f=a||l===u.Direction.Left||l===u.Direction.Down?-1:1;-1!==d&&(p.includes(e.key)?(e.preventDefault(),i.setState({draggedThumbIndex:d,isChanged:!0}),s((0,c.replaceAt)(r,d,i.normalizeValue(r[d]+f*("PageUp"===e.key?10*n:n),d)))):h.includes(e.key)?(e.preventDefault(),i.setState({draggedThumbIndex:d,isChanged:!0}),s((0,c.replaceAt)(r,d,i.normalizeValue(r[d]-f*("PageDown"===e.key?10*n:n),d)))):"Tab"===e.key?i.setState({draggedThumbIndex:-1},function(){o&&i.fireOnFinalChange()}):o&&i.fireOnFinalChange())},i.onKeyUp=function(e){var t=i.state.isChanged;i.setState({draggedThumbIndex:-1},function(){t&&i.fireOnFinalChange()})},i.onMove=function(e,t){var r=i.state,s=r.draggedThumbIndex,n=r.draggedTrackPos,a=i.props,l=a.direction,o=a.min,d=a.max,p=a.onChange,h=a.values,f=a.step,m=a.rtl;if(-1===s&&-1===n[0]&&-1===n[1])return null;var v=i.trackRef.current;if(!v)return null;var g=v.getBoundingClientRect(),b=(0,c.isVertical)(l)?g.height:g.width;if(-1!==n[0]&&-1!==n[1]){var w=e-n[0],y=t-n[1],S=0;switch(l){case u.Direction.Right:case u.Direction.Left:S=w/b*(d-o);break;case u.Direction.Down:case u.Direction.Up:S=y/b*(d-o);break;default:(0,c.assertUnreachable)(l)}if(m&&(S*=-1),Math.abs(S)>=f/2){for(var T=0;T<i.thumbRefs.length;T++){if(h[T]===d&&1===Math.sign(S)||h[T]===o&&-1===Math.sign(S))return;var x=h[T]+S;x>d?S=d-h[T]:x<o&&(S=o-h[T])}for(var E=h.slice(0),T=0;T<i.thumbRefs.length;T++)E=(0,c.replaceAt)(E,T,i.normalizeValue(h[T]+S,T));i.setState({draggedTrackPos:[e,t]}),p(E)}}else{var M=0;switch(l){case u.Direction.Right:M=(e-g.left)/b*(d-o)+o;break;case u.Direction.Left:M=(b-(e-g.left))/b*(d-o)+o;break;case u.Direction.Down:M=(t-g.top)/b*(d-o)+o;break;case u.Direction.Up:M=(b-(t-g.top))/b*(d-o)+o;break;default:(0,c.assertUnreachable)(l)}m&&(M=d+o-M),Math.abs(h[s]-M)>=f/2&&p((0,c.replaceAt)(h,s,i.normalizeValue(M,s)))}},i.normalizeValue=function(e,t){var r=i.props,s=r.min,n=r.max,a=r.step,l=r.allowOverlap,o=r.values;return(0,c.normalizeValue)(e,t,s,n,a,l,o)},i.onEnd=function(e){if(e.preventDefault(),document.removeEventListener("mousemove",i.schdOnMouseMove),document.removeEventListener("touchmove",i.schdOnTouchMove),document.removeEventListener("mouseup",i.schdOnEnd),document.removeEventListener("touchend",i.schdOnEnd),document.removeEventListener("touchcancel",i.schdOnEnd),-1===i.state.draggedThumbIndex&&-1===i.state.draggedTrackPos[0]&&-1===i.state.draggedTrackPos[1])return null;i.setState({draggedThumbIndex:-1,draggedTrackPos:[-1,-1]},function(){i.fireOnFinalChange()})},i.fireOnFinalChange=function(){i.setState({isChanged:!1});var e=i.props,t=e.onFinalChange,r=e.values;t&&t(r)},i.updateMarkRefs=function(e){if(!e.renderMark){i.numOfMarks=void 0,i.markRefs=void 0;return}i.numOfMarks=(e.max-e.min)/i.props.step,i.markRefs=[];for(var t=0;t<i.numOfMarks+1;t++)i.markRefs[t]=d.createRef()},i.calculateMarkOffsets=function(){if(i.props.renderMark&&i.trackRef&&i.numOfMarks&&i.markRefs&&null!==i.trackRef.current){for(var e=window.getComputedStyle(i.trackRef.current),t=parseInt(e.width,10),r=parseInt(e.height,10),s=parseInt(e.paddingLeft,10),n=parseInt(e.paddingTop,10),a=[],l=0;l<i.numOfMarks+1;l++){var o=9999,d=9999;if(i.markRefs[l].current){var c=i.markRefs[l].current.getBoundingClientRect();o=c.height,d=c.width}i.props.direction===u.Direction.Left||i.props.direction===u.Direction.Right?a.push([Math.round(t/i.numOfMarks*l+s-d/2),-Math.round((o-r)/2)]):a.push([Math.round(r/i.numOfMarks*l+n-o/2),-Math.round((d-t)/2)])}i.setState({markOffsets:a})}},0===t.step)throw Error('"step" property should be a positive number');return i.schdOnMouseMove=(0,c.schd)(i.onMouseMove),i.schdOnTouchMove=(0,c.schd)(i.onTouchMove),i.schdOnEnd=(0,c.schd)(i.onEnd),i.thumbRefs=t.values.map(function(){return d.createRef()}),i.updateMarkRefs(t),i}return s(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,i=t.values,r=t.min,s=t.step;this.resizeObserver=window.ResizeObserver?new window.ResizeObserver(this.onResize):{observe:function(){return window.addEventListener("resize",e.onResize)},unobserve:function(){return window.removeEventListener("resize",e.onResize)}},document.addEventListener("touchstart",this.onMouseOrTouchStart,{passive:!1}),document.addEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),this.props.allowOverlap||(0,c.checkInitialOverlap)(this.props.values),this.props.values.forEach(function(t){return(0,c.checkBoundaries)(t,e.props.min,e.props.max)}),this.resizeObserver.observe(this.trackRef.current),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),this.props.rtl),this.calculateMarkOffsets(),i.forEach(function(e){(0,c.isStepDivisible)(r,e,s)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")})},t.prototype.componentDidUpdate=function(e,t){var i=this.props,r=i.max,s=i.min,n=i.step,a=i.values,l=i.rtl;(e.max!==r||e.min!==s||e.step!==n)&&this.updateMarkRefs(this.props),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),l),(e.max!==r||e.min!==s||e.step!==n||t.markOffsets.length!==this.state.markOffsets.length)&&(this.calculateMarkOffsets(),a.forEach(function(e){(0,c.isStepDivisible)(s,e,n)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")}))},t.prototype.componentWillUnmount=function(){document.removeEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),document.removeEventListener("mousemove",this.schdOnMouseMove),document.removeEventListener("touchmove",this.schdOnTouchMove),document.removeEventListener("touchstart",this.onMouseOrTouchStart),document.removeEventListener("mouseup",this.schdOnEnd),document.removeEventListener("touchend",this.schdOnEnd),this.resizeObserver.unobserve(this.trackRef.current)},t.prototype.render=function(){var e=this,t=this.props,i=t.label,r=t.labelledBy,s=t.renderTrack,n=t.renderThumb,a=t.renderMark,l=void 0===a?function(){return null}:a,d=t.values,p=t.min,h=t.max,f=t.allowOverlap,m=t.disabled,v=this.state,g=v.draggedThumbIndex,b=v.thumbZIndexes,w=v.markOffsets;return s({props:{style:{transform:"scale(1)",cursor:g>-1?"grabbing":this.props.draggableTrack?(0,c.isVertical)(this.props.direction)?"ns-resize":"ew-resize":1!==d.length||m?"inherit":"pointer"},onMouseDown:m?c.voidFn:this.onMouseDownTrack,onTouchStart:m?c.voidFn:this.onTouchStartTrack,ref:this.trackRef},isDragged:this.state.draggedThumbIndex>-1,disabled:m,children:o(o([],w.map(function(t,i,r){return l({props:{style:e.props.direction===u.Direction.Left||e.props.direction===u.Direction.Right?{position:"absolute",left:"".concat(t[0],"px"),marginTop:"".concat(t[1],"px")}:{position:"absolute",top:"".concat(t[0],"px"),marginLeft:"".concat(t[1],"px")},key:"mark".concat(i),ref:e.markRefs[i]},index:i})}),!0),d.map(function(t,s){var a=e.state.draggedThumbIndex===s;return n({index:s,value:t,isDragged:a,props:{style:{position:"absolute",zIndex:b[s],cursor:m?"inherit":a?"grabbing":"grab",userSelect:"none",touchAction:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none"},key:s,tabIndex:m?void 0:0,"aria-valuemax":f?h:d[s+1]||h,"aria-valuemin":f?p:d[s-1]||p,"aria-valuenow":t,draggable:!1,ref:e.thumbRefs[s],"aria-label":i,"aria-labelledby":r,role:"slider",onKeyDown:m?c.voidFn:e.onKeyDown,onKeyUp:m?c.voidFn:e.onKeyUp}})}),!0)})},t.defaultProps={label:"Accessibility label",labelledBy:null,step:1,direction:u.Direction.Right,rtl:!1,disabled:!1,allowOverlap:!1,draggableTrack:!1,min:0,max:100},t}(d.Component)},13372:(e,t,i)=>{"use strict";i.d(t,{Vx:()=>n,dK:()=>l}),i(49863);var r=i(33544);function s(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(n=>{if(!i[n]&&!0===i.auto){let a=(0,r.e)(e.el,`.${s[n]}`)[0];a||((a=(0,r.c)("div",s[n])).className=s[n],e.el.append(a)),i[n]=a,t[n]=a}}),i}function n(e){let{swiper:t,extendParams:i,on:n,emit:a}=e;function l(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function o(e,i){let s=t.params.navigation;(e=(0,r.m)(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass))})}function d(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){o(i,!1),o(e,!1);return}o(i,t.isBeginning&&!t.params.rewind),o(e,t.isEnd&&!t.params.rewind)}function c(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),a("navigationPrev"))}function u(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),a("navigationNext"))}function p(){let e=t.params.navigation;if(t.params.navigation=s(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=l(e.nextEl),n=l(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:n}),i=(0,r.m)(i),n=(0,r.m)(n);let a=(i,r)=>{i&&i.addEventListener("click","next"===r?u:c),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>a(e,"next")),n.forEach(e=>a(e,"prev"))}function h(){let{nextEl:e,prevEl:i}=t.navigation;e=(0,r.m)(e),i=(0,r.m)(i);let s=(e,i)=>{e.removeEventListener("click","next"===i?u:c),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),i.forEach(e=>s(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},n("init",()=>{!1===t.params.navigation.enabled?f():(p(),d())}),n("toEdge fromEdge lock unlock",()=>{d()}),n("destroy",()=>{h()}),n("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=(0,r.m)(e),i=(0,r.m)(i),t.enabled)return void d();[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),n("click",(e,i)=>{let{nextEl:s,prevEl:n}=t.navigation;s=(0,r.m)(s),n=(0,r.m)(n);let l=i.target,o=n.includes(l)||s.includes(l);if(t.isElement&&!o){let e=i.path||i.composedPath&&i.composedPath();e&&(o=e.find(e=>s.includes(e)||n.includes(e)))}if(t.params.navigation.hideOnClick&&!o){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===l||t.pagination.el.contains(l)))return;s.length?e=s[0].classList.contains(t.params.navigation.hiddenClass):n.length&&(e=n[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?a("navigationShow"):a("navigationHide"),[...s,...n].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let f=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),h()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),p(),d()},disable:f,update:d,init:p,destroy:h})}function a(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function l(e){let t,{swiper:i,extendParams:n,on:l,emit:o}=e,d="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${d}-bullet`,bulletActiveClass:`${d}-bullet-active`,modifierClass:`${d}-`,currentClass:`${d}-current`,totalClass:`${d}-total`,hiddenClass:`${d}-hidden`,progressbarFillClass:`${d}-progressbar-fill`,progressbarOppositeClass:`${d}-progressbar-opposite`,clickableClass:`${d}-clickable`,lockClass:`${d}-lock`,horizontalClass:`${d}-horizontal`,verticalClass:`${d}-vertical`,paginationDisabledClass:`${d}-disabled`}}),i.pagination={el:null,bullets:[]};let c=0;function u(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function p(e,t){let{bulletActiveClass:r}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${r}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${r}-${t}-${t}`))}function h(e){let t=e.target.closest(a(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=(0,r.i)(t)*i.params.slidesPerGroup;if(i.params.loop){var n,l,o;if(i.realIndex===s)return;let e=(n=i.realIndex,l=s,(n%=o=i.slides.length,(l%=o)===n+1)?"next":l===n-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function f(){let e,s,n=i.rtl,l=i.params.pagination;if(u())return;let d=i.pagination.el;d=(0,r.m)(d);let h=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,f=i.params.loop?Math.ceil(h/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(s=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,s=i.previousSnapIndex):(s=i.previousIndex||0,e=i.activeIndex||0),"bullets"===l.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let a,o,u,h=i.pagination.bullets;if(l.dynamicBullets&&(t=(0,r.h)(h[0],i.isHorizontal()?"width":"height",!0),d.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(l.dynamicMainBullets+4)}px`}),l.dynamicMainBullets>1&&void 0!==s&&((c+=e-(s||0))>l.dynamicMainBullets-1?c=l.dynamicMainBullets-1:c<0&&(c=0)),u=((o=(a=Math.max(e-c,0))+(Math.min(h.length,l.dynamicMainBullets)-1))+a)/2),h.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${l.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),d.length>1)h.forEach(t=>{let s=(0,r.i)(t);s===e?t.classList.add(...l.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),l.dynamicBullets&&(s>=a&&s<=o&&t.classList.add(...`${l.bulletActiveClass}-main`.split(" ")),s===a&&p(t,"prev"),s===o&&p(t,"next"))});else{let t=h[e];if(t&&t.classList.add(...l.bulletActiveClass.split(" ")),i.isElement&&h.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),l.dynamicBullets){let e=h[a],t=h[o];for(let e=a;e<=o;e+=1)h[e]&&h[e].classList.add(...`${l.bulletActiveClass}-main`.split(" "));p(e,"prev"),p(t,"next")}}if(l.dynamicBullets){let e=Math.min(h.length,l.dynamicMainBullets+4),r=(t*e-t)/2-u*t,s=n?"right":"left";h.forEach(e=>{e.style[i.isHorizontal()?s:"top"]=`${r}px`})}}d.forEach((t,s)=>{if("fraction"===l.type&&(t.querySelectorAll(a(l.currentClass)).forEach(t=>{t.textContent=l.formatFractionCurrent(e+1)}),t.querySelectorAll(a(l.totalClass)).forEach(e=>{e.textContent=l.formatFractionTotal(f)})),"progressbar"===l.type){let r;r=l.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let s=(e+1)/f,n=1,o=1;"horizontal"===r?n=s:o=s,t.querySelectorAll(a(l.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${n}) scaleY(${o})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===l.type&&l.renderCustom?((0,r.s)(t,l.renderCustom(i,e+1,f)),0===s&&o("paginationRender",t)):(0===s&&o("paginationRender",t),o("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](l.lockClass)})}function m(){let e=i.params.pagination;if(u())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=(0,r.m)(s);let n="";if("bullets"===e.type){let r=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&r>t&&(r=t);for(let t=0;t<r;t+=1)e.renderBullet?n+=e.renderBullet.call(i,t,e.bulletClass):n+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(n=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(n=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&(0,r.s)(t,n||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(a(e.bulletClass)))}),"custom"!==e.type&&o("paginationRender",s[0])}function v(){let e;i.params.pagination=s(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>(0,r.b)(e,".swiper")[0]===i.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=(0,r.m)(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),c=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",h),i.enabled||e.classList.add(t.lockClass)})))}function g(){let e=i.params.pagination;if(u())return;let t=i.pagination.el;t&&(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",h))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}l("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),l("init",()=>{!1===i.params.pagination.enabled?b():(v(),m(),f())}),l("activeIndexChange",()=>{void 0===i.snapIndex&&f()}),l("snapIndexChange",()=>{f()}),l("snapGridLengthChange",()=>{m(),f()}),l("destroy",()=>{g()}),l("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),l("lock unlock",()=>{f()}),l("click",(e,t)=>{let s=t.target,n=(0,r.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&n&&n.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;!0===n[0].classList.contains(i.params.pagination.hiddenClass)?o("paginationShow"):o("paginationHide"),n.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let b=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),g()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),v(),m(),f()},disable:b,render:m,update:f,init:v,destroy:g})}},22728:function(e,t,i){"use strict";var r=this&&this.__spreadArray||function(e,t,i){if(i||2==arguments.length)for(var r,s=0,n=t.length;s<n;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.isIOS=t.useThumbOverlap=t.assertUnreachable=t.voidFn=t.getTrackBackground=t.replaceAt=t.schd=t.translate=t.getClosestThumbIndex=t.translateThumbs=t.getPaddingAndBorder=t.getMargin=t.checkInitialOverlap=t.checkValuesAgainstBoundaries=t.checkBoundaries=t.isVertical=t.relativeValue=t.normalizeValue=t.isStepDivisible=t.isTouchEvent=t.getStepDecimals=void 0;var s=i(43210),n=i(29868);function a(e){return e===n.Direction.Up||e===n.Direction.Down}function l(e,t,i){e.style.transform="translate(".concat(t,"px, ").concat(i,"px)")}t.getStepDecimals=function(e){var t=e.toString().split(".")[1];return t?t.length:0},t.isTouchEvent=function(e){return e.touches&&e.touches.length||e.changedTouches&&e.changedTouches.length},t.isStepDivisible=function(e,t,i){var r=Number(((t-e)/i).toFixed(8));return parseInt(r.toString(),10)===r},t.normalizeValue=function(e,i,r,s,n,a,l){if(e=Math.round(1e11*e)/1e11,!a){var o=l[i-1],d=l[i+1];if(o&&o>e)return o;if(d&&d<e)return d}if(e>s)return s;if(e<r)return r;var c=Math.floor(1e11*e-1e11*r)%Math.floor(1e11*n),u=Math.floor(1e11*e-Math.abs(c)),p=0===c?e:u/1e11,h=Math.abs(c/1e11)<n/2?p:p+n,f=(0,t.getStepDecimals)(n);return parseFloat(h.toFixed(f))},t.relativeValue=function(e,t,i){return(e-t)/(i-t)},t.isVertical=a,t.checkBoundaries=function(e,t,i){if(t>=i)throw RangeError("min (".concat(t,") is equal/bigger than max (").concat(i,")"));if(e<t)throw RangeError("value (".concat(e,") is smaller than min (").concat(t,")"));if(e>i)throw RangeError("value (".concat(e,") is bigger than max (").concat(i,")"))},t.checkValuesAgainstBoundaries=function(e,t,i){return e<t?t:e>i?i:e},t.checkInitialOverlap=function(e){if(!(e.length<2)&&!e.slice(1).every(function(t,i){return e[i]<=t}))throw RangeError("values={[".concat(e,"]} needs to be sorted when allowOverlap={false}"))},t.getMargin=function(e){var t=window.getComputedStyle(e);return{top:parseInt(t["margin-top"],10),bottom:parseInt(t["margin-bottom"],10),left:parseInt(t["margin-left"],10),right:parseInt(t["margin-right"],10)}},t.getPaddingAndBorder=function(e){var t=window.getComputedStyle(e);return{top:parseInt(t["padding-top"],10)+parseInt(t["border-top-width"],10),bottom:parseInt(t["padding-bottom"],10)+parseInt(t["border-bottom-width"],10),left:parseInt(t["padding-left"],10)+parseInt(t["border-left-width"],10),right:parseInt(t["padding-right"],10)+parseInt(t["border-right-width"],10)}},t.translateThumbs=function(e,t,i){var r=i?-1:1;e.forEach(function(e,i){return l(e,r*t[i].x,t[i].y)})},t.getClosestThumbIndex=function(e,t,i,r){for(var s=0,n=c(e[0],t,i,r),a=1;a<e.length;a++){var l=c(e[a],t,i,r);l<n&&(n=l,s=a)}return s},t.translate=l,t.schd=function(e){var t=[],i=null;return function(){for(var r=[],s=0;s<arguments.length;s++)r[s]=arguments[s];t=r,i||(i=requestAnimationFrame(function(){i=null,e.apply(void 0,t)}))}},t.replaceAt=function(e,t,i){var r=e.slice(0);return r[t]=i,r},t.getTrackBackground=function(e){var t=e.values,i=e.colors,r=e.min,s=e.max,a=e.direction,l=void 0===a?n.Direction.Right:a,o=e.rtl,d=void 0!==o&&o;d&&l===n.Direction.Right?l=n.Direction.Left:d&&n.Direction.Left&&(l=n.Direction.Right);var c=t.slice(0).sort(function(e,t){return e-t}).map(function(e){return(e-r)/(s-r)*100}).reduce(function(e,t,r){return"".concat(e,", ").concat(i[r]," ").concat(t,"%, ").concat(i[r+1]," ").concat(t,"%")},"");return"linear-gradient(".concat(l,", ").concat(i[0]," 0%").concat(c,", ").concat(i[i.length-1]," 100%)")},t.voidFn=function(){},t.assertUnreachable=function(e){throw Error("Didn't expect to get here")};var o=function(e,t,i,s,n){return void 0===n&&(n=function(e){return e}),Math.ceil(r([e],Array.from(e.children),!0).reduce(function(e,r){var a=Math.ceil(r.getBoundingClientRect().width);if(r.innerText&&r.innerText.includes(i)&&0===r.childElementCount){var l=r.cloneNode(!0);l.innerHTML=n(t.toFixed(s)),l.style.visibility="hidden",document.body.appendChild(l),a=Math.ceil(l.getBoundingClientRect().width),document.body.removeChild(l)}return a>e?a:e},e.getBoundingClientRect().width))},d=function(e,t,i,s,n,a,l){void 0===l&&(l=function(e){return e});var d=[],c=function(e){var u=o(i[e],s[e],n,a,l),p=t[e].x;t.forEach(function(t,h){var f=t.x,m=o(i[h],s[h],n,a,l);e!==h&&(p>=f&&p<=f+m||p+u>=f&&p+u<=f+m)&&!d.includes(h)&&(d.push(e),d.push(h),d=r(r([],d,!0),[e,h],!1),c(h))})};return c(e),Array.from(new Set(d.sort()))};function c(e,t,i,r){var s=e.getBoundingClientRect(),n=s.left,l=s.top,o=s.width,d=s.height;return a(r)?Math.abs(i-(l+d/2)):Math.abs(t-(n+o/2))}t.useThumbOverlap=function(e,i,n,a,l,o){void 0===a&&(a=.1),void 0===l&&(l=" - "),void 0===o&&(o=function(e){return e});var c=(0,t.getStepDecimals)(a),u=(0,s.useState)({}),p=u[0],h=u[1],f=(0,s.useState)(o(i[n].toFixed(c))),m=f[0],v=f[1];return(0,s.useEffect)(function(){if(e){var t=e.getThumbs();if(!(t.length<1)){var s={},a=e.getOffsets(),u=d(n,a,t,i,l,c,o),p=o(i[n].toFixed(c));if(u.length){var f=u.reduce(function(e,t,i,s){return e.length?r(r([],e,!0),[a[s[i]].x],!1):[a[s[i]].x]},[]);if(Math.min.apply(Math,f)===a[n].x){var m=[];u.forEach(function(e){m.push(i[e].toFixed(c))}),p=Array.from(new Set(m.sort(function(e,t){return parseFloat(e)-parseFloat(t)}))).map(o).join(l);var g=Math.min.apply(Math,f),b=Math.max.apply(Math,f),w=t[u[f.indexOf(b)]].getBoundingClientRect().width;s.left="".concat(Math.abs(g-(b+w))/2,"px"),s.transform="translate(-50%, 0)"}else s.visibility="hidden"}v(p),h(s)}}},[e,i]),[m,p]},t.isIOS=function(){var e;return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes((null==(e=navigator.userAgentData)?void 0:e.platform)||navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}},29868:(e,t)=>{"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.Direction=void 0,function(e){e.Right="to right",e.Left="to left",e.Down="to bottom",e.Up="to top"}(i||(t.Direction=i={}))},32036:(e,t,i)=>{"use strict";let r,s,n;i.d(t,{RC:()=>W,qr:()=>X});var a=i(43210),l=i(49863),o=i(33544);function d(){return r||(r=function(){let e=(0,l.a)(),t=(0,l.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),r}function c(e){return void 0===e&&(e={}),s||(s=function(e){let{userAgent:t}=void 0===e?{}:e,i=d(),r=(0,l.a)(),s=r.navigator.platform,n=t||r.navigator.userAgent,a={ios:!1,android:!1},o=r.screen.width,c=r.screen.height,u=n.match(/(Android);?[\s\/]+([\d.]+)?/),p=n.match(/(iPad).*OS\s([\d_]+)/),h=n.match(/(iPod)(.*OS\s([\d_]+))?/),f=!p&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===s;return!p&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${c}`)>=0&&((p=n.match(/(Version)\/([\d.]+)/))||(p=[0,1,"13_0_0"]),m=!1),u&&"Win32"!==s&&(a.os="android",a.android=!0),(p||f||h)&&(a.os="ios",a.ios=!0),a}(e)),s}function u(){return n||(n=function(){let e=(0,l.a)(),t=c(),i=!1;function r(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(r()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,r]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&r<2}}let s=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),n=r(),a=n||s&&t.ios;return{isSafari:i||n,needPerspectiveFix:i,need3dFix:a,isWebView:s}}()),n}let p=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},h=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},f=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},m=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},v=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let r="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),s=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[s-t];i.push(...Array.from({length:t}).map((e,t)=>s+r+t)),e.slides.forEach((t,r)=>{i.includes(t.column)&&m(e,r)});return}let n=s+r-1;if(e.params.rewind||e.params.loop)for(let r=s-t;r<=n+t;r+=1){let t=(r%i+i)%i;(t<s||t>n)&&m(e,t)}else for(let r=Math.max(s-t,0);r<=Math.min(n+t,i-1);r+=1)r!==s&&(r>n||r<s)&&m(e,r)};function g(e){let{swiper:t,runCallbacks:i,direction:r,step:s}=e,{activeIndex:n,previousIndex:a}=t,l=r;l||(l=n>a?"next":n<a?"prev":"reset"),t.emit(`transition${s}`),i&&"reset"===l?t.emit(`slideResetTransition${s}`):i&&n!==a&&(t.emit(`slideChangeTransition${s}`),"next"===l?t.emit(`slideNextTransition${s}`):t.emit(`slidePrevTransition${s}`))}function b(e,t,i){let r=(0,l.a)(),{params:s}=e,n=s.edgeSwipeDetection,a=s.edgeSwipeThreshold;return!n||!(i<=a)&&!(i>=r.innerWidth-a)||"prevent"===n&&(t.preventDefault(),!0)}function w(e){let t=(0,l.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let r=this.touchEventsData;if("pointerdown"===i.type){if(null!==r.pointerId&&r.pointerId!==i.pointerId)return;r.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(r.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type)return void b(this,i,i.targetTouches[0].pageX);let{params:s,touches:n,enabled:a}=this;if(!a||!s.simulateTouch&&"mouse"===i.pointerType||this.animating&&s.preventInteractionOnTransition)return;!this.animating&&s.cssMode&&s.loop&&this.loopFix();let d=i.target;if("wrapper"===s.touchEventsTarget&&!(0,o.w)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||r.isTouched&&r.isMoved)return;let c=!!s.noSwipingClass&&""!==s.noSwipingClass,u=i.composedPath?i.composedPath():i.path;c&&i.target&&i.target.shadowRoot&&u&&(d=u[0]);let p=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(s.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,l.g)()||i===(0,l.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let r=i.closest(e);return r||i.getRootNode?r||t(i.getRootNode().host):null}(t)}(p,d):d.closest(p))){this.allowClick=!0;return}if(s.swipeHandler&&!d.closest(s.swipeHandler))return;n.currentX=i.pageX,n.currentY=i.pageY;let f=n.currentX,m=n.currentY;if(!b(this,i,f))return;Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),n.startX=f,n.startY=m,r.touchStartTime=(0,o.f)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,s.threshold>0&&(r.allowThresholdMove=!1);let v=!0;d.matches(r.focusableElements)&&(v=!1,"SELECT"===d.nodeName&&(r.isTouched=!1)),t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(r.focusableElements))&&t.activeElement.blur();let g=v&&this.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||g)&&!d.isContentEditable&&i.preventDefault(),s.freeMode&&s.freeMode.enabled&&this.freeMode&&this.animating&&!s.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function y(e){let t,i,r=(0,l.g)(),s=this.touchEventsData,{params:n,touches:a,rtlTranslate:d,enabled:c}=this;if(!c||!n.simulateTouch&&"mouse"===e.pointerType)return;let u=e;if(u.originalEvent&&(u=u.originalEvent),"pointermove"===u.type&&(null!==s.touchId||u.pointerId!==s.pointerId))return;if("touchmove"===u.type){if(!(t=[...u.changedTouches].find(e=>e.identifier===s.touchId))||t.identifier!==s.touchId)return}else t=u;if(!s.isTouched){s.startMoving&&s.isScrolling&&this.emit("touchMoveOpposite",u);return}let p=t.pageX,h=t.pageY;if(u.preventedByNestedSwiper){a.startX=p,a.startY=h;return}if(!this.allowTouchMove){u.target.matches(s.focusableElements)||(this.allowClick=!1),s.isTouched&&(Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h}),s.touchStartTime=(0,o.f)());return}if(n.touchReleaseOnEdges&&!n.loop){if(this.isVertical()){if(h<a.startY&&this.translate<=this.maxTranslate()||h>a.startY&&this.translate>=this.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else if(d&&(p>a.startX&&-this.translate<=this.maxTranslate()||p<a.startX&&-this.translate>=this.minTranslate()))return;else if(!d&&(p<a.startX&&this.translate<=this.maxTranslate()||p>a.startX&&this.translate>=this.minTranslate()))return}if(r.activeElement&&r.activeElement.matches(s.focusableElements)&&r.activeElement!==u.target&&"mouse"!==u.pointerType&&r.activeElement.blur(),r.activeElement&&u.target===r.activeElement&&u.target.matches(s.focusableElements)){s.isMoved=!0,this.allowClick=!1;return}s.allowTouchCallbacks&&this.emit("touchMove",u),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=p,a.currentY=h;let f=a.currentX-a.startX,m=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(f**2+m**2)<this.params.threshold)return;if(void 0===s.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?s.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,s.isScrolling=this.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(s.isScrolling&&this.emit("touchMoveOpposite",u),void 0===s.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(s.startMoving=!0),s.isScrolling||"touchmove"===u.type&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;this.allowClick=!1,!n.cssMode&&u.cancelable&&u.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&u.stopPropagation();let v=this.isHorizontal()?f:m,g=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;n.oneWayMovement&&(v=Math.abs(v)*(d?1:-1),g=Math.abs(g)*(d?1:-1)),a.diff=v,v*=n.touchRatio,d&&(v=-v,g=-g);let b=this.touchesDirection;this.swipeDirection=v>0?"prev":"next",this.touchesDirection=g>0?"prev":"next";let w=this.params.loop&&!n.cssMode,y="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!s.isMoved){if(w&&y&&this.loopFix({direction:this.swipeDirection}),s.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}s.allowMomentumBounce=!1,n.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",u)}if(new Date().getTime(),!1!==n._loopSwapReset&&s.isMoved&&s.allowThresholdMove&&b!==this.touchesDirection&&w&&y&&Math.abs(v)>=1){Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}this.emit("sliderMove",u),s.isMoved=!0,s.currentTranslate=v+s.startTranslate;let S=!0,T=n.resistanceRatio;if(n.touchReleaseOnEdges&&(T=0),v>0?(w&&y&&!i&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==n.slidesPerView&&this.slides.length-n.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>this.minTranslate()&&(S=!1,n.resistance&&(s.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+s.startTranslate+v)**T))):v<0&&(w&&y&&!i&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==n.slidesPerView&&this.slides.length-n.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===n.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<this.maxTranslate()&&(S=!1,n.resistance&&(s.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-s.startTranslate-v)**T))),S&&(u.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),this.allowSlidePrev||this.allowSlideNext||(s.currentTranslate=s.startTranslate),n.threshold>0)if(Math.abs(v)>n.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,s.currentTranslate=s.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{s.currentTranslate=s.startTranslate;return}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&this.freeMode||n.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(s.currentTranslate),this.setTranslate(s.currentTranslate))}function S(e){let t,i,r=this,s=r.touchEventsData,n=e;if(n.originalEvent&&(n=n.originalEvent),"touchend"===n.type||"touchcancel"===n.type){if(!(t=[...n.changedTouches].find(e=>e.identifier===s.touchId))||t.identifier!==s.touchId)return}else{if(null!==s.touchId||n.pointerId!==s.pointerId)return;t=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&!(["pointercancel","contextmenu"].includes(n.type)&&(r.browser.isSafari||r.browser.isWebView)))return;s.pointerId=null,s.touchId=null;let{params:a,touches:l,rtlTranslate:d,slidesGrid:c,enabled:u}=r;if(!u||!a.simulateTouch&&"mouse"===n.pointerType)return;if(s.allowTouchCallbacks&&r.emit("touchEnd",n),s.allowTouchCallbacks=!1,!s.isTouched){s.isMoved&&a.grabCursor&&r.setGrabCursor(!1),s.isMoved=!1,s.startMoving=!1;return}a.grabCursor&&s.isMoved&&s.isTouched&&(!0===r.allowSlideNext||!0===r.allowSlidePrev)&&r.setGrabCursor(!1);let p=(0,o.f)(),h=p-s.touchStartTime;if(r.allowClick){let e=n.path||n.composedPath&&n.composedPath();r.updateClickedSlide(e&&e[0]||n.target,e),r.emit("tap click",n),h<300&&p-s.lastClickTime<300&&r.emit("doubleTap doubleClick",n)}if(s.lastClickTime=(0,o.f)(),(0,o.n)(()=>{r.destroyed||(r.allowClick=!0)}),!s.isTouched||!s.isMoved||!r.swipeDirection||0===l.diff&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset){s.isTouched=!1,s.isMoved=!1,s.startMoving=!1;return}if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,i=a.followFinger?d?r.translate:-r.translate:-s.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled)return void r.freeMode.onTouchEnd({currentPos:i});let f=i>=-r.maxTranslate()&&!r.params.loop,m=0,v=r.slidesSizesGrid[0];for(let e=0;e<c.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){let t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==c[e+t]?(f||i>=c[e]&&i<c[e+t])&&(m=e,v=c[e+t]-c[e]):(f||i>=c[e])&&(m=e,v=c[c.length-1]-c[c.length-2])}let g=null,b=null;a.rewind&&(r.isBeginning?b=a.virtual&&a.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1:r.isEnd&&(g=0));let w=(i-c[m])/v,y=m<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(h>a.longSwipesMs){if(!a.longSwipes)return void r.slideTo(r.activeIndex);"next"===r.swipeDirection&&(w>=a.longSwipesRatio?r.slideTo(a.rewind&&r.isEnd?g:m+y):r.slideTo(m)),"prev"===r.swipeDirection&&(w>1-a.longSwipesRatio?r.slideTo(m+y):null!==b&&w<0&&Math.abs(w)>a.longSwipesRatio?r.slideTo(b):r.slideTo(m))}else{if(!a.shortSwipes)return void r.slideTo(r.activeIndex);r.navigation&&(n.target===r.navigation.nextEl||n.target===r.navigation.prevEl)?n.target===r.navigation.nextEl?r.slideTo(m+y):r.slideTo(m):("next"===r.swipeDirection&&r.slideTo(null!==g?g:m+y),"prev"===r.swipeDirection&&r.slideTo(null!==b?b:m))}}function T(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:r,allowSlidePrev:s,snapGrid:n}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let l=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||l?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=s,e.allowSlideNext=r,e.params.watchOverflow&&n!==e.snapGrid&&e.checkOverflow()}function x(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function E(){let e,{wrapperEl:t,rtlTranslate:i,enabled:r}=this;if(!r)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-t.scrollLeft:this.translate=-t.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(i?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function M(e){f(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function C(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let k=(e,t)=>{let i=(0,l.g)(),{params:r,el:s,wrapperEl:n,device:a}=e,o=!!r.nested,d="on"===t?"addEventListener":"removeEventListener";s&&"string"!=typeof s&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),s[d]("touchstart",e.onTouchStart,{passive:!1}),s[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&s[d]("click",e.onClick,!0),r.cssMode&&n[d]("scroll",e.onScroll),r.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",T,!0):e[t]("observerUpdate",T,!0),s[d]("load",e.onLoad,{capture:!0}))},P=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var O={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let L={eventsEmitter:{on(e,t,i){let r=this;if(!r.eventsListeners||r.destroyed||"function"!=typeof t)return r;let s=i?"unshift":"push";return e.split(" ").forEach(e=>{r.eventsListeners[e]||(r.eventsListeners[e]=[]),r.eventsListeners[e][s](t)}),r},once(e,t,i){let r=this;if(!r.eventsListeners||r.destroyed||"function"!=typeof t)return r;function s(){r.off(e,s),s.__emitterProxy&&delete s.__emitterProxy;for(var i=arguments.length,n=Array(i),a=0;a<i;a++)n[a]=arguments[a];t.apply(r,n)}return s.__emitterProxy=t,r.on(e,s,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((r,s)=>{(r===t||r.__emitterProxy&&r.__emitterProxy===t)&&i.eventsListeners[e].splice(s,1)})}),i},emit(){let e,t,i,r=this;if(!r.eventsListeners||r.destroyed||!r.eventsListeners)return r;for(var s=arguments.length,n=Array(s),a=0;a<s;a++)n[a]=arguments[a];return"string"==typeof n[0]||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),i=r):(e=n[0].events,t=n[0].data,i=n[0].context||r),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{r.eventsAnyListeners&&r.eventsAnyListeners.length&&r.eventsAnyListeners.forEach(r=>{r.apply(i,[e,...t])}),r.eventsListeners&&r.eventsListeners[e]&&r.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),r}},update:{updateSize:function(){let e,t,i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,o.q)(i,"padding-left")||0,10)-parseInt((0,o.q)(i,"padding-right")||0,10),t=t-parseInt((0,o.q)(i,"padding-top")||0,10)-parseInt((0,o.q)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e,t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let r=t.params,{wrapperEl:s,slidesEl:n,size:a,rtlTranslate:l,wrongRTL:d}=t,c=t.virtual&&r.virtual.enabled,u=c?t.virtual.slides.length:t.slides.length,p=(0,o.e)(n,`.${t.params.slideClass}, swiper-slide`),h=c?t.virtual.slides.length:p.length,f=[],m=[],v=[],g=r.slidesOffsetBefore;"function"==typeof g&&(g=r.slidesOffsetBefore.call(t));let b=r.slidesOffsetAfter;"function"==typeof b&&(b=r.slidesOffsetAfter.call(t));let w=t.snapGrid.length,y=t.slidesGrid.length,S=r.spaceBetween,T=-g,x=0,E=0;if(void 0===a)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*a:"string"==typeof S&&(S=parseFloat(S)),t.virtualSize=-S,p.forEach(e=>{l?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),r.centeredSlides&&r.cssMode&&((0,o.a)(s,"--swiper-centered-offset-before",""),(0,o.a)(s,"--swiper-centered-offset-after",""));let M=r.grid&&r.grid.rows>1&&t.grid;M?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let C="auto"===r.slidesPerView&&r.breakpoints&&Object.keys(r.breakpoints).filter(e=>void 0!==r.breakpoints[e].slidesPerView).length>0;for(let s=0;s<h;s+=1){let n;if(e=0,p[s]&&(n=p[s]),M&&t.grid.updateSlide(s,n,p),!p[s]||"none"!==(0,o.q)(n,"display")){if("auto"===r.slidesPerView){C&&(p[s].style[t.getDirectionLabel("width")]="");let a=getComputedStyle(n),l=n.style.transform,d=n.style.webkitTransform;if(l&&(n.style.transform="none"),d&&(n.style.webkitTransform="none"),r.roundLengths)e=t.isHorizontal()?(0,o.h)(n,"width",!0):(0,o.h)(n,"height",!0);else{let t=i(a,"width"),r=i(a,"padding-left"),s=i(a,"padding-right"),l=i(a,"margin-left"),o=i(a,"margin-right"),d=a.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+l+o;else{let{clientWidth:i,offsetWidth:a}=n;e=t+r+s+l+o+(a-i)}}l&&(n.style.transform=l),d&&(n.style.webkitTransform=d),r.roundLengths&&(e=Math.floor(e))}else e=(a-(r.slidesPerView-1)*S)/r.slidesPerView,r.roundLengths&&(e=Math.floor(e)),p[s]&&(p[s].style[t.getDirectionLabel("width")]=`${e}px`);p[s]&&(p[s].swiperSlideSize=e),v.push(e),r.centeredSlides?(T=T+e/2+x/2+S,0===x&&0!==s&&(T=T-a/2-S),0===s&&(T=T-a/2-S),.001>Math.abs(T)&&(T=0),r.roundLengths&&(T=Math.floor(T)),E%r.slidesPerGroup==0&&f.push(T),m.push(T)):(r.roundLengths&&(T=Math.floor(T)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup==0&&f.push(T),m.push(T),T=T+e+S),t.virtualSize+=e+S,x=e,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+b,l&&d&&("slide"===r.effect||"coverflow"===r.effect)&&(s.style.width=`${t.virtualSize+S}px`),r.setWrapperSize&&(s.style[t.getDirectionLabel("width")]=`${t.virtualSize+S}px`),M&&t.grid.updateWrapperSize(e,f),!r.centeredSlides){let e=[];for(let i=0;i<f.length;i+=1){let s=f[i];r.roundLengths&&(s=Math.floor(s)),f[i]<=t.virtualSize-a&&e.push(s)}f=e,Math.floor(t.virtualSize-a)-Math.floor(f[f.length-1])>1&&f.push(t.virtualSize-a)}if(c&&r.loop){let e=v[0]+S;if(r.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/r.slidesPerGroup),s=e*r.slidesPerGroup;for(let e=0;e<i;e+=1)f.push(f[f.length-1]+s)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===r.slidesPerGroup&&f.push(f[f.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===f.length&&(f=[0]),0!==S){let e=t.isHorizontal()&&l?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!r.cssMode||!!r.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${S}px`})}if(r.centeredSlides&&r.centeredSlidesBounds){let e=0;v.forEach(t=>{e+=t+(S||0)});let t=(e-=S)>a?e-a:0;f=f.map(e=>e<=0?-g:e>t?t+b:e)}if(r.centerInsufficientSlides){let e=0;v.forEach(t=>{e+=t+(S||0)}),e-=S;let t=(r.slidesOffsetBefore||0)+(r.slidesOffsetAfter||0);if(e+t<a){let i=(a-e-t)/2;f.forEach((e,t)=>{f[t]=e-i}),m.forEach((e,t)=>{m[t]=e+i})}}if(Object.assign(t,{slides:p,snapGrid:f,slidesGrid:m,slidesSizesGrid:v}),r.centeredSlides&&r.cssMode&&!r.centeredSlidesBounds){(0,o.a)(s,"--swiper-centered-offset-before",`${-f[0]}px`),(0,o.a)(s,"--swiper-centered-offset-after",`${t.size/2-v[v.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(h!==u&&t.emit("slidesLengthChange"),f.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==y&&t.emit("slidesGridLengthChange"),r.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!c&&!r.cssMode&&("slide"===r.effect||"fade"===r.effect)){let e=`${r.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);h<=r.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t,i=this,r=[],s=i.virtual&&i.params.virtual.enabled,n=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let a=e=>s?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1)if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{r.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!s)break;r.push(a(e))}else r.push(a(i.activeIndex));for(t=0;t<r.length;t+=1)if(void 0!==r[t]){let e=r[t].offsetHeight;n=e>n?e:n}(n||0===n)&&(i.wrapperEl.style.height=`${n}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:r,snapGrid:s}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let n=-e;r&&(n=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let l=i[e],o=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let d=(n+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+a),c=(n-s[0]+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+a),u=-(n-o),h=u+this.slidesSizesGrid[e],f=u>=0&&u<=this.size-this.slidesSizesGrid[e],m=u>=0&&u<this.size-1||h>1&&h<=this.size||u<=0&&h>=this.size;m&&(this.visibleSlides.push(l),this.visibleSlidesIndexes.push(e)),p(l,m,t.slideVisibleClass),p(l,f,t.slideFullyVisibleClass),l.progress=r?-d:d,l.originalProgress=r?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:r,isBeginning:s,isEnd:n,progressLoop:a}=this,l=s,o=n;if(0===i)r=0,s=!0,n=!0;else{r=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());s=t||r<=0,n=a||r>=1,t&&(r=0),a&&(r=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),r=this.slidesGrid[t],s=this.slidesGrid[i],n=this.slidesGrid[this.slidesGrid.length-1],l=Math.abs(e);(a=l>=r?(l-r)/n:(l+n-s)/n)>1&&(a-=1)}Object.assign(this,{progress:r,progressLoop:a,isBeginning:s,isEnd:n}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),s&&!l&&this.emit("reachBeginning toEdge"),n&&!o&&this.emit("reachEnd toEdge"),(l&&!s||o&&!n)&&this.emit("fromEdge"),this.emit("progress",r)},updateSlidesClasses:function(){let e,t,i,{slides:r,params:s,slidesEl:n,activeIndex:a}=this,l=this.virtual&&s.virtual.enabled,d=this.grid&&s.grid&&s.grid.rows>1,c=e=>(0,o.e)(n,`.${s.slideClass}${e}, swiper-slide${e}`)[0];if(l)if(s.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=c(`[data-swiper-slide-index="${t}"]`)}else e=c(`[data-swiper-slide-index="${a}"]`);else d?(e=r.find(e=>e.column===a),i=r.find(e=>e.column===a+1),t=r.find(e=>e.column===a-1)):e=r[a];e&&!d&&(i=(0,o.r)(e,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!i&&(i=r[0]),t=(0,o.t)(e,`.${s.slideClass}, swiper-slide`)[0],s.loop),r.forEach(r=>{h(r,r===e,s.slideActiveClass),h(r,r===i,s.slideNextClass),h(r,r===t,s.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i,r=this,s=r.rtlTranslate?r.translate:-r.translate,{snapGrid:n,params:a,activeIndex:l,realIndex:o,snapIndex:d}=r,c=e,u=e=>{let t=e-r.virtual.slidesBefore;return t<0&&(t=r.virtual.slides.length+t),t>=r.virtual.slides.length&&(t-=r.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t,{slidesGrid:i,params:r}=e,s=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?s>=i[e]&&s<i[e+1]-(i[e+1]-i[e])/2?t=e:s>=i[e]&&s<i[e+1]&&(t=e+1):s>=i[e]&&(t=e);return r.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(r)),n.indexOf(s)>=0)t=n.indexOf(s);else{let e=Math.min(a.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/a.slidesPerGroup)}if(t>=n.length&&(t=n.length-1),c===l&&!r.params.loop){t!==d&&(r.snapIndex=t,r.emit("snapIndexChange"));return}if(c===l&&r.params.loop&&r.virtual&&r.params.virtual.enabled){r.realIndex=u(c);return}let p=r.grid&&a.grid&&a.grid.rows>1;if(r.virtual&&a.virtual.enabled&&a.loop)i=u(c);else if(p){let e=r.slides.find(e=>e.column===c),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(r.slides.indexOf(e),0)),i=Math.floor(t/a.grid.rows)}else if(r.slides[c]){let e=r.slides[c].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):c}else i=c;Object.assign(r,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:l,activeIndex:c}),r.initialized&&v(r),r.emit("activeIndexChange"),r.emit("snapIndexChange"),(r.initialized||r.params.runCallbacksOnInit)&&(o!==i&&r.emit("realIndexChange"),r.emit("slideChange"))},updateClickedSlide:function(e,t){let i,r=this.params,s=e.closest(`.${r.slideClass}, swiper-slide`);!s&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!s&&e.matches&&e.matches(`.${r.slideClass}, swiper-slide`)&&(s=e)});let n=!1;if(s){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===s){n=!0,i=e;break}}if(s&&n)this.clickedSlide=s,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(s.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}r.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:r,wrapperEl:s}=this;if(t.virtualTranslate)return i?-r:r;if(t.cssMode)return r;let n=(0,o.k)(s,e);return n+=this.cssOverflowAdjustment(),i&&(n=-n),n||0},setTranslate:function(e,t){let i,{rtlTranslate:r,params:s,wrapperEl:n,progress:a}=this,l=0,o=0;this.isHorizontal()?l=r?-e:e:o=e,s.roundLengths&&(l=Math.floor(l),o=Math.floor(o)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?l:o,s.cssMode?n[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-l:-o:s.virtualTranslate||(this.isHorizontal()?l-=this.cssOverflowAdjustment():o-=this.cssOverflowAdjustment(),n.style.transform=`translate3d(${l}px, ${o}px, 0px)`);let d=this.maxTranslate()-this.minTranslate();(0===d?0:(e-this.minTranslate())/d)!==a&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,r,s){let n;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===r&&(r=!0);let a=this,{params:l,wrapperEl:d}=a;if(a.animating&&l.preventInteractionOnTransition)return!1;let c=a.minTranslate(),u=a.maxTranslate();if(n=r&&e>c?c:r&&e<u?u:e,a.updateProgress(n),l.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-n;else{if(!a.support.smoothScroll)return(0,o.u)({swiper:a,targetPosition:-n,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-n,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,s),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,s),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),g({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),g({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,r,s){let n;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,l=e;l<0&&(l=0);let{params:d,snapGrid:c,slidesGrid:p,previousIndex:h,activeIndex:f,rtlTranslate:m,wrapperEl:v,enabled:g}=a;if(!g&&!r&&!s||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let b=Math.min(a.params.slidesPerGroupSkip,l),w=b+Math.floor((l-b)/a.params.slidesPerGroup);w>=c.length&&(w=c.length-1);let y=-c[w];if(d.normalizeSlideIndex)for(let e=0;e<p.length;e+=1){let t=-Math.floor(100*y),i=Math.floor(100*p[e]),r=Math.floor(100*p[e+1]);void 0!==p[e+1]?t>=i&&t<r-(r-i)/2?l=e:t>=i&&t<r&&(l=e+1):t>=i&&(l=e)}if(a.initialized&&l!==f&&(!a.allowSlideNext&&(m?y>a.translate&&y>a.minTranslate():y<a.translate&&y<a.minTranslate())||!a.allowSlidePrev&&y>a.translate&&y>a.maxTranslate()&&(f||0)!==l))return!1;l!==(h||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(y),n=l>f?"next":l<f?"prev":"reset";let S=a.virtual&&a.params.virtual.enabled;if(!(S&&s)&&(m&&-y===a.translate||!m&&y===a.translate))return a.updateActiveIndex(l),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(y),"reset"!==n&&(a.transitionStart(i,n),a.transitionEnd(i,n)),!1;if(d.cssMode){let e=a.isHorizontal(),i=m?y:-y;if(0===t)S&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),S&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{v[e?"scrollLeft":"scrollTop"]=i})):v[e?"scrollLeft":"scrollTop"]=i,S&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return(0,o.u)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;v.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}let T=u().isSafari;return S&&!s&&T&&a.isElement&&a.virtual.update(!1,!1,l),a.setTransition(t),a.setTranslate(y),a.updateActiveIndex(l),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,r),a.transitionStart(i,n),0===t?a.transitionEnd(i,n):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,n))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,r){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let s=this;if(s.destroyed)return;void 0===t&&(t=s.params.speed);let n=s.grid&&s.params.grid&&s.params.grid.rows>1,a=e;if(s.params.loop)if(s.virtual&&s.params.virtual.enabled)a+=s.virtual.slidesBefore;else{let e;if(n){let t=a*s.params.grid.rows;e=s.slides.find(e=>+e.getAttribute("data-swiper-slide-index")===t).column}else e=s.getSlideIndexByData(a);let t=n?Math.ceil(s.slides.length/s.params.grid.rows):s.slides.length,{centeredSlides:i}=s.params,l=s.params.slidesPerView;"auto"===l?l=s.slidesPerViewDynamic():(l=Math.ceil(parseFloat(s.params.slidesPerView,10)),i&&l%2==0&&(l+=1));let o=t-e<l;if(i&&(o=o||e<Math.ceil(l/2)),r&&i&&"auto"!==s.params.slidesPerView&&!n&&(o=!1),o){let r=i?e<s.activeIndex?"prev":"next":e-s.activeIndex-1<s.params.slidesPerView?"next":"prev";s.loopFix({direction:r,slideTo:!0,activeSlideIndex:"next"===r?e+1:e-t+1,slideRealIndex:"next"===r?s.realIndex:void 0})}if(n){let e=a*s.params.grid.rows;a=s.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e).column}else a=s.getSlideIndexByData(a)}return requestAnimationFrame(()=>{s.slideTo(a,t,i,r)}),s},slideNext:function(e,t,i){void 0===t&&(t=!0);let r=this,{enabled:s,params:n,animating:a}=r;if(!s||r.destroyed)return r;void 0===e&&(e=r.params.speed);let l=n.slidesPerGroup;"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(l=Math.max(r.slidesPerViewDynamic("current",!0),1));let o=r.activeIndex<n.slidesPerGroupSkip?1:l,d=r.virtual&&n.virtual.enabled;if(n.loop){if(a&&!d&&n.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+o,e,t,i)}),!0}return n.rewind&&r.isEnd?r.slideTo(0,e,t,i):r.slideTo(r.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let r=this,{params:s,snapGrid:n,slidesGrid:a,rtlTranslate:l,enabled:o,animating:d}=r;if(!o||r.destroyed)return r;void 0===e&&(e=r.params.speed);let c=r.virtual&&s.virtual.enabled;if(s.loop){if(d&&!c&&s.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let p=u(l?r.translate:-r.translate),h=n.map(e=>u(e)),f=s.freeMode&&s.freeMode.enabled,m=n[h.indexOf(p)-1];if(void 0===m&&(s.cssMode||f)){let e;n.forEach((t,i)=>{p>=t&&(e=i)}),void 0!==e&&(m=f?n[e]:n[e>0?e-1:e])}let v=0;if(void 0!==m&&((v=a.indexOf(m))<0&&(v=r.activeIndex-1),"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(v=Math.max(v=v-r.slidesPerViewDynamic("previous",!0)+1,0))),s.rewind&&r.isBeginning){let s=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(s,e,t,i)}return s.loop&&0===r.activeIndex&&s.cssMode?(requestAnimationFrame(()=>{r.slideTo(v,e,t,i)}),!0):r.slideTo(v,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,r){if(void 0===t&&(t=!0),void 0===r&&(r=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let s=this.activeIndex,n=Math.min(this.params.slidesPerGroupSkip,s),a=n+Math.floor((s-n)/this.params.slidesPerGroup),l=this.rtlTranslate?this.translate:-this.translate;if(l>=this.snapGrid[a]){let e=this.snapGrid[a];l-e>(this.snapGrid[a+1]-e)*r&&(s+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];l-e<=(this.snapGrid[a]-e)*r&&(s-=this.params.slidesPerGroup)}return s=Math.min(s=Math.max(s,0),this.slidesGrid.length-1),this.slideTo(s,e,t,i)},slideToClickedSlide:function(){let e,t=this;if(t.destroyed)return;let{params:i,slidesEl:r}=t,s="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,n=t.clickedIndex,a=t.isElement?"swiper-slide":`.${i.slideClass}`;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?n<t.loopedSlides-s/2||n>t.slides.length-t.loopedSlides+s/2?(t.loopFix(),n=t.getSlideIndex((0,o.e)(r,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(n)})):t.slideTo(n):n>t.slides.length-s?(t.loopFix(),n=t.getSlideIndex((0,o.e)(r,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(n)})):t.slideTo(n)}else t.slideTo(n)}},loop:{loopCreate:function(e,t){let i=this,{params:r,slidesEl:s}=i;if(!r.loop||i.virtual&&i.params.virtual.enabled)return;let n=i.grid&&r.grid&&r.grid.rows>1,a=r.slidesPerGroup*(n?r.grid.rows:1),l=i.slides.length%a!=0,d=n&&i.slides.length%r.grid.rows!=0,c=e=>{for(let t=0;t<e;t+=1){let e=i.isElement?(0,o.c)("swiper-slide",[r.slideBlankClass]):(0,o.c)("div",[r.slideClass,r.slideBlankClass]);i.slidesEl.append(e)}};l?r.loopAddBlankSlides?(c(a-i.slides.length%a),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):d&&(r.loopAddBlankSlides?(c(r.grid.rows-i.slides.length%r.grid.rows),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,o.e)(s,`.${r.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),i.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:r,setTranslate:s,activeSlideIndex:n,initial:a,byController:l,byMousewheel:d}=void 0===e?{}:e,c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");let{slides:u,allowSlidePrev:p,allowSlideNext:h,slidesEl:f,params:m}=c,{centeredSlides:v,initialSlide:g}=m;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&m.virtual.enabled){i&&(m.centeredSlides||0!==c.snapIndex?m.centeredSlides&&c.snapIndex<m.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0):c.slideTo(c.virtual.slides.length,0,!1,!0)),c.allowSlidePrev=p,c.allowSlideNext=h,c.emit("loopFix");return}let b=m.slidesPerView;"auto"===b?b=c.slidesPerViewDynamic():(b=Math.ceil(parseFloat(m.slidesPerView,10)),v&&b%2==0&&(b+=1));let w=m.slidesPerGroupAuto?b:m.slidesPerGroup,y=w;y%w!=0&&(y+=w-y%w),c.loopedSlides=y+=m.loopAdditionalSlides;let S=c.grid&&m.grid&&m.grid.rows>1;u.length<b+y||"cards"===c.params.effect&&u.length<b+2*y?(0,o.v)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&"row"===m.grid.fill&&(0,o.v)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let T=[],x=[],E=S?Math.ceil(u.length/m.grid.rows):u.length,M=a&&E-g<b&&!v,C=M?g:c.activeIndex;void 0===n?n=c.getSlideIndex(u.find(e=>e.classList.contains(m.slideActiveClass))):C=n;let k="next"===r||!r,P="prev"===r||!r,O=0,L=0,I=(S?u[n].column:n)+(v&&void 0===s?-b/2+.5:0);if(I<y){O=Math.max(y-I,w);for(let e=0;e<y-I;e+=1){let t=e-Math.floor(e/E)*E;if(S){let e=E-t-1;for(let t=u.length-1;t>=0;t-=1)u[t].column===e&&T.push(t)}else T.push(E-t-1)}}else if(I+b>E-y){L=Math.max(I-(E-2*y),w),M&&(L=Math.max(L,b-E+g+1));for(let e=0;e<L;e+=1){let t=e-Math.floor(e/E)*E;S?u.forEach((e,i)=>{e.column===t&&x.push(i)}):x.push(t)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),"cards"===c.params.effect&&u.length<b+2*y&&(x.includes(n)&&x.splice(x.indexOf(n),1),T.includes(n)&&T.splice(T.indexOf(n),1)),P&&T.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.prepend(u[e]),u[e].swiperLoopMoveDOM=!1}),k&&x.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.append(u[e]),u[e].swiperLoopMoveDOM=!1}),c.recalcSlides(),"auto"===m.slidesPerView?c.updateSlides():S&&(T.length>0&&P||x.length>0&&k)&&c.slides.forEach((e,t)=>{c.grid.updateSlide(t,e,c.slides)}),m.watchSlidesProgress&&c.updateSlidesOffset(),i){if(T.length>0&&P){if(void 0===t){let e=c.slidesGrid[C],t=c.slidesGrid[C+O]-e;d?c.setTranslate(c.translate-t):(c.slideTo(C+Math.ceil(O),0,!1,!0),s&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else if(s){let e=S?T.length/m.grid.rows:T.length;c.slideTo(c.activeIndex+e,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(x.length>0&&k)if(void 0===t){let e=c.slidesGrid[C],t=c.slidesGrid[C-L]-e;d?c.setTranslate(c.translate-t):(c.slideTo(C-L,0,!1,!0),s&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else{let e=S?x.length/m.grid.rows:x.length;c.slideTo(c.activeIndex-e,0,!1,!0)}}if(c.allowSlidePrev=p,c.allowSlideNext=h,c.controller&&c.controller.control&&!l){let e={slideRealIndex:t,direction:r,setTranslate:s,activeSlideIndex:n,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&i})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...e,slideTo:c.controller.control.params.slidesPerView===m.slidesPerView&&i})}c.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=w.bind(this),this.onTouchMove=y.bind(this),this.onTouchEnd=S.bind(this),this.onDocumentTouchStart=C.bind(this),e.cssMode&&(this.onScroll=E.bind(this)),this.onClick=x.bind(this),this.onLoad=M.bind(this),k(this,"on")},detachEvents:function(){k(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:r,el:s}=e,n=r.breakpoints;if(!n||n&&0===Object.keys(n).length)return;let a=(0,l.g)(),d="window"!==r.breakpointsBase&&r.breakpointsBase?"container":r.breakpointsBase,c=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:a.querySelector(r.breakpointsBase),u=e.getBreakpoint(n,d,c);if(!u||e.currentBreakpoint===u)return;let p=(u in n?n[u]:void 0)||e.originalParams,h=P(e,r),f=P(e,p),m=e.params.grabCursor,v=p.grabCursor,g=r.enabled;h&&!f?(s.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!h&&f&&(s.classList.add(`${r.containerModifierClass}grid`),(p.grid.fill&&"column"===p.grid.fill||!p.grid.fill&&"column"===r.grid.fill)&&s.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!v?e.unsetGrabCursor():!m&&v&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===p[t])return;let i=r[t]&&r[t].enabled,s=p[t]&&p[t].enabled;i&&!s&&e[t].disable(),!i&&s&&e[t].enable()});let b=p.direction&&p.direction!==r.direction,w=r.loop&&(p.slidesPerView!==r.slidesPerView||b),y=r.loop;b&&i&&e.changeDirection(),(0,o.x)(e.params,p);let S=e.params.enabled,T=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),g&&!S?e.disable():!g&&S&&e.enable(),e.currentBreakpoint=u,e.emit("_beforeBreakpoint",p),i&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!y&&T?(e.loopCreate(t),e.updateSlides()):y&&!T&&e.loopDestroy()),e.emit("breakpoint",p)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let r=!1,s=(0,l.a)(),n="window"===t?s.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:n*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:n,value:l}=a[e];"window"===t?s.matchMedia(`(min-width: ${l}px)`).matches&&(r=n):l<=i.clientWidth&&(r=n)}return r||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:r,device:s}=this,n=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(r=>{e[r]&&i.push(t+r)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:s.android},{ios:s.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...n),r.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},I={};class A{constructor(){let e,t;for(var i=arguments.length,r=Array(i),s=0;s<i;s++)r[s]=arguments[s];1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?t=r[0]:[e,t]=r,t||(t={}),t=(0,o.x)({},t),e&&!t.el&&(t.el=e);let n=(0,l.g)();if(t.el&&"string"==typeof t.el&&n.querySelectorAll(t.el).length>1){let e=[];return n.querySelectorAll(t.el).forEach(i=>{let r=(0,o.x)({},t,{el:i});e.push(new A(r))}),e}let a=this;a.__swiper__=!0,a.support=d(),a.device=c({userAgent:t.userAgent}),a.browser=u(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);let p={};a.modules.forEach(e=>{e({params:t,swiper:a,extendParams:function(e,t){return function(i){void 0===i&&(i={});let r=Object.keys(i)[0],s=i[r];return"object"!=typeof s||null===s?void(0,o.x)(t,i):(!0===e[r]&&(e[r]={enabled:!0}),"navigation"===r&&e[r]&&e[r].enabled&&!e[r].prevEl&&!e[r].nextEl&&(e[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&e[r]&&e[r].enabled&&!e[r].el&&(e[r].auto=!0),r in e&&"enabled"in s)?void("object"==typeof e[r]&&!("enabled"in e[r])&&(e[r].enabled=!0),!e[r]&&(e[r]={enabled:!1}),(0,o.x)(t,i)):void(0,o.x)(t,i)}}(t,p),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});let h=(0,o.x)({},O,p);return a.params=(0,o.x)({},h,I,t),a.originalParams=(0,o.x)({},a.params),a.passedParams=(0,o.x)({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,r=(0,o.e)(t,`.${i.slideClass}, swiper-slide`),s=(0,o.i)(r[0]);return(0,o.i)(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e))}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,o.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),r=(this.maxTranslate()-i)*e+i;this.translateTo(r,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let r=e.getSlideClasses(i);t.push({slideEl:i,classNames:r}),e.emit("_slideClass",i,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:r,slidesGrid:s,slidesSizesGrid:n,size:a,activeIndex:l}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=r[l]?Math.ceil(r[l].swiperSlideSize):0;for(let i=l+1;i<r.length;i+=1)r[i]&&!e&&(t+=Math.ceil(r[i].swiperSlideSize),o+=1,t>a&&(e=!0));for(let i=l-1;i>=0;i-=1)r[i]&&!e&&(t+=r[i].swiperSlideSize,o+=1,t>a&&(e=!0))}else if("current"===e)for(let e=l+1;e<r.length;e+=1)(t?s[e]+n[e]-s[l]<a:s[e]-s[l]<a)&&(o+=1);else for(let e=l-1;e>=0;e-=1)s[l]-s[e]<a&&(o+=1);return o}update(){let e,t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:r}=t;function s(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&f(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),r.freeMode&&r.freeMode.enabled&&!r.cssMode)s(),r.autoHeight&&t.updateAutoHeight();else{if(("auto"===r.slidesPerView||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){let i=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||s()}r.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let r=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,s=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(r()):(0,o.e)(i,r())[0];return!s&&t.params.createElements&&(s=(0,o.c)("div",t.params.wrapperClass),i.append(s),(0,o.e)(i,`.${t.params.slideClass}`).forEach(e=>{s.append(e)})),Object.assign(t,{el:i,wrapperEl:s,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:s,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction")),wrongRTL:"-webkit-box"===(0,o.q)(s,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?f(t,e):e.addEventListener("load",e=>{f(t,e.target)})}),v(t),t.initialized=!0,v(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:r,el:s,wrapperEl:n,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),r.loop&&i.loopDestroy(),t&&(i.removeClasses(),s&&"string"!=typeof s&&s.removeAttribute("style"),n&&n.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,o.y)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,o.x)(I,e)}static get extendedDefaults(){return I}static get defaults(){return O}static installModule(e){A.prototype.__modules__||(A.prototype.__modules__=[]);let t=A.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>A.installModule(e)):A.installModule(e),A}}Object.keys(L).forEach(e=>{Object.keys(L[e]).forEach(t=>{A.prototype[t]=L[e][t]})}),A.use([function(e){let{swiper:t,on:i,emit:r}=e,s=(0,l.a)(),n=null,a=null,o=()=>{t&&!t.destroyed&&t.initialized&&(r("beforeResize"),r("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&(n=new ResizeObserver(e=>{a=s.requestAnimationFrame(()=>{let{width:i,height:r}=t,s=i,n=r;e.forEach(e=>{let{contentBoxSize:i,contentRect:r,target:a}=e;a&&a!==t.el||(s=r?r.width:(i[0]||i).inlineSize,n=r?r.height:(i[0]||i).blockSize)}),(s!==i||n!==r)&&o()})})).observe(t.el)},c=()=>{a&&s.cancelAnimationFrame(a),n&&n.unobserve&&t.el&&(n.unobserve(t.el),n=null)},u=()=>{t&&!t.destroyed&&t.initialized&&r("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==s.ResizeObserver)return void d();s.addEventListener("resize",o),s.addEventListener("orientationchange",u)}),i("destroy",()=>{c(),s.removeEventListener("resize",o),s.removeEventListener("orientationchange",u)})},function(e){let{swiper:t,extendParams:i,on:r,emit:s}=e,n=[],a=(0,l.a)(),d=function(e,i){void 0===i&&(i={});let r=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void s("observerUpdate",e[0]);let i=function(){s("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});r.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),n.push(r)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,o.b)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),r("destroy",()=>{n.forEach(e=>{e.disconnect()}),n.splice(0,n.length)})}]);let _=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function D(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function z(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:D(t[i])&&D(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:z(e[i],t[i]):e[i]=t[i]})}function R(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function B(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function $(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function V(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let G=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e}).apply(this,arguments)}function N(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function j(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let H=(0,a.createContext)(null),q=(0,a.createContext)(null),W=(0,a.forwardRef)(function(e,t){var i;let{className:r,tag:s="div",wrapperTag:n="div",children:l,onSwiper:d,...c}=void 0===e?{}:e,u=!1,[p,h]=(0,a.useState)("swiper"),[f,m]=(0,a.useState)(null),[v,g]=(0,a.useState)(!1),b=(0,a.useRef)(!1),w=(0,a.useRef)(null),y=(0,a.useRef)(null),S=(0,a.useRef)(null),T=(0,a.useRef)(null),x=(0,a.useRef)(null),E=(0,a.useRef)(null),M=(0,a.useRef)(null),C=(0,a.useRef)(null),{params:k,passedParams:P,rest:L,events:I}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},r={},s={};z(i,O),i._emitClasses=!0,i.init=!1;let n={},a=_.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(l=>{void 0!==e[l]&&(a.indexOf(l)>=0?D(e[l])?(i[l]={},s[l]={},z(i[l],e[l]),z(s[l],e[l])):(i[l]=e[l],s[l]=e[l]):0===l.search(/on[A-Z]/)&&"function"==typeof e[l]?t?r[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:i.on[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:n[l]=e[l])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:s,rest:n,events:r}}(c),{slides:H,slots:W}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(N(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let r=function e(t){let i=[];return a.Children.toArray(t).forEach(t=>{N(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);r.length>0?r.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(l),X=()=>{g(!v)};Object.assign(k.on,{_containerClasses(e,t){h(t)}});let Y=()=>{Object.assign(k.on,I),u=!0;let e={...k};if(delete e.wrapperClass,y.current=new A(e),y.current.virtual&&y.current.params.virtual.enabled){y.current.virtual.slides=H;let e={cache:!1,slides:H,renderExternal:m,renderExternalUpdate:!1};z(y.current.params.virtual,e),z(y.current.originalParams.virtual,e)}};w.current||Y(),y.current&&y.current.on("_beforeBreakpoint",X);let U=()=>{!u&&I&&y.current&&Object.keys(I).forEach(e=>{y.current.on(e,I[e])})},K=()=>{I&&y.current&&Object.keys(I).forEach(e=>{y.current.off(e,I[e])})};return(0,a.useEffect)(()=>()=>{y.current&&y.current.off("_beforeBreakpoint",X)}),(0,a.useEffect)(()=>{!b.current&&y.current&&(y.current.emitSlidesClasses(),b.current=!0)}),j(()=>{if(t&&(t.current=w.current),w.current)return y.current.destroyed&&Y(),!function(e,t){let{el:i,nextEl:r,prevEl:s,paginationEl:n,scrollbarEl:a,swiper:l}=e;R(t)&&r&&s&&(l.params.navigation.nextEl=r,l.originalParams.navigation.nextEl=r,l.params.navigation.prevEl=s,l.originalParams.navigation.prevEl=s),B(t)&&n&&(l.params.pagination.el=n,l.originalParams.pagination.el=n),$(t)&&a&&(l.params.scrollbar.el=a,l.originalParams.scrollbar.el=a),l.init(i)}({el:w.current,nextEl:x.current,prevEl:E.current,paginationEl:M.current,scrollbarEl:C.current,swiper:y.current},k),d&&!y.current.destroyed&&d(y.current),()=>{y.current&&!y.current.destroyed&&y.current.destroy(!0,!1)}},[]),j(()=>{U();let e=function(e,t,i,r,s){let n=[];if(!t)return n;let a=e=>{0>n.indexOf(e)&&n.push(e)};if(i&&r){let e=r.map(s),t=i.map(s);e.join("")!==t.join("")&&a("children"),r.length!==i.length&&a("children")}return _.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t)if(D(e[i])&&D(t[i])){let r=Object.keys(e[i]),s=Object.keys(t[i]);r.length!==s.length?a(i):(r.forEach(r=>{e[i][r]!==t[i][r]&&a(i)}),s.forEach(r=>{e[i][r]!==t[i][r]&&a(i)}))}else e[i]!==t[i]&&a(i)}),n}(P,S.current,H,T.current,e=>e.key);return S.current=P,T.current=H,e.length&&y.current&&!y.current.destroyed&&function(e){let t,i,r,s,n,a,l,d,{swiper:c,slides:u,passedParams:p,changedParams:h,nextEl:f,prevEl:m,scrollbarEl:v,paginationEl:g}=e,b=h.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:w,pagination:y,navigation:S,scrollbar:T,virtual:x,thumbs:E}=c;h.includes("thumbs")&&p.thumbs&&p.thumbs.swiper&&!p.thumbs.swiper.destroyed&&w.thumbs&&(!w.thumbs.swiper||w.thumbs.swiper.destroyed)&&(t=!0),h.includes("controller")&&p.controller&&p.controller.control&&w.controller&&!w.controller.control&&(i=!0),h.includes("pagination")&&p.pagination&&(p.pagination.el||g)&&(w.pagination||!1===w.pagination)&&y&&!y.el&&(r=!0),h.includes("scrollbar")&&p.scrollbar&&(p.scrollbar.el||v)&&(w.scrollbar||!1===w.scrollbar)&&T&&!T.el&&(s=!0),h.includes("navigation")&&p.navigation&&(p.navigation.prevEl||m)&&(p.navigation.nextEl||f)&&(w.navigation||!1===w.navigation)&&S&&!S.prevEl&&!S.nextEl&&(n=!0);let M=e=>{c[e]&&(c[e].destroy(),"navigation"===e?(c.isElement&&(c[e].prevEl.remove(),c[e].nextEl.remove()),w[e].prevEl=void 0,w[e].nextEl=void 0,c[e].prevEl=void 0,c[e].nextEl=void 0):(c.isElement&&c[e].el.remove(),w[e].el=void 0,c[e].el=void 0))};h.includes("loop")&&c.isElement&&(w.loop&&!p.loop?a=!0:!w.loop&&p.loop?l=!0:d=!0),b.forEach(e=>{if(D(w[e])&&D(p[e]))Object.assign(w[e],p[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in p[e]&&!p[e].enabled&&M(e);else{let t=p[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&M(e):w[e]=p[e]}}),b.includes("controller")&&!i&&c.controller&&c.controller.control&&w.controller&&w.controller.control&&(c.controller.control=w.controller.control),h.includes("children")&&u&&x&&w.virtual.enabled?(x.slides=u,x.update(!0)):h.includes("virtual")&&x&&w.virtual.enabled&&(u&&(x.slides=u),x.update(!0)),h.includes("children")&&u&&w.loop&&(d=!0),t&&E.init()&&E.update(!0),i&&(c.controller.control=w.controller.control),r&&(c.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-pagination"),g.part.add("pagination"),c.el.appendChild(g)),g&&(w.pagination.el=g),y.init(),y.render(),y.update()),s&&(c.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-scrollbar"),v.part.add("scrollbar"),c.el.appendChild(v)),v&&(w.scrollbar.el=v),T.init(),T.updateSize(),T.setTranslate()),n&&(c.isElement&&(f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-next"),(0,o.s)(f,c.hostEl.constructor.nextButtonSvg),f.part.add("button-next"),c.el.appendChild(f)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),(0,o.s)(m,c.hostEl.constructor.prevButtonSvg),m.part.add("button-prev"),c.el.appendChild(m))),f&&(w.navigation.nextEl=f),m&&(w.navigation.prevEl=m),S.init(),S.update()),h.includes("allowSlideNext")&&(c.allowSlideNext=p.allowSlideNext),h.includes("allowSlidePrev")&&(c.allowSlidePrev=p.allowSlidePrev),h.includes("direction")&&c.changeDirection(p.direction,!1),(a||d)&&c.loopDestroy(),(l||d)&&c.loopCreate(),c.update()}({swiper:y.current,slides:H,passedParams:P,changedParams:e,nextEl:x.current,prevEl:E.current,scrollbarEl:C.current,paginationEl:M.current}),()=>{K()}}),j(()=>{G(y.current)},[f]),a.createElement(s,F({ref:w,className:V(`${p}${r?` ${r}`:""}`)},L),a.createElement(q.Provider,{value:y.current},W["container-start"],a.createElement(n,{className:(void 0===(i=k.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},W["wrapper-start"],k.virtual?function(e,t,i){if(!i)return null;let r=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},s=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:n,to:l}=i,o=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,c=[];for(let e=o;e<d;e+=1)e>=n&&e<=l&&c.push(t[r(e)]);return c.map((t,i)=>a.cloneElement(t,{swiper:e,style:s,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(y.current,H,f):H.map((e,t)=>a.cloneElement(e,{swiper:y.current,swiperSlideIndex:t})),W["wrapper-end"]),R(k)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:E,className:"swiper-button-prev"}),a.createElement("div",{ref:x,className:"swiper-button-next"})),$(k)&&a.createElement("div",{ref:C,className:"swiper-scrollbar"}),B(k)&&a.createElement("div",{ref:M,className:"swiper-pagination"}),W["container-end"]))});W.displayName="Swiper";let X=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:r,className:s="",swiper:n,zoom:l,lazy:o,virtualIndex:d,swiperSlideIndex:c,...u}=void 0===e?{}:e,p=(0,a.useRef)(null),[h,f]=(0,a.useState)("swiper-slide"),[m,v]=(0,a.useState)(!1);function g(e,t,i){t===p.current&&f(i)}j(()=>{if(void 0!==c&&(p.current.swiperSlideIndex=c),t&&(t.current=p.current),p.current&&n){if(n.destroyed){"swiper-slide"!==h&&f("swiper-slide");return}return n.on("_slideClass",g),()=>{n&&n.off("_slideClass",g)}}}),j(()=>{n&&p.current&&!n.destroyed&&f(n.getSlideClasses(p.current))},[n]);let b={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof r?r(b):r;return a.createElement(i,F({ref:p,className:V(`${h}${s?` ${s}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{v(!0)}},u),l&&a.createElement(H.Provider,{value:b},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof l?l:void 0},w(),o&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!l&&a.createElement(H.Provider,{value:b},w(),o&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"})))});X.displayName="SwiperSlide"},33544:(e,t,i)=>{"use strict";i.d(t,{a:()=>d,b:()=>w,c:()=>f,e:()=>u,f:()=>a,h:()=>y,i:()=>b,k:()=>l,m:()=>S,n:()=>n,q:()=>g,r:()=>v,s:()=>T,t:()=>m,u:()=>c,v:()=>h,w:()=>p,x:()=>function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let r=1;r<arguments.length;r+=1){let s=r<0||arguments.length<=r?void 0:arguments[r];if(null!=s&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(s instanceof HTMLElement):!s||1!==s.nodeType&&11!==s.nodeType)){let r=Object.keys(Object(s)).filter(e=>0>i.indexOf(e));for(let i=0,n=r.length;i<n;i+=1){let n=r[i],a=Object.getOwnPropertyDescriptor(s,n);void 0!==a&&a.enumerable&&(o(t[n])&&o(s[n])?s[n].__swiper__?t[n]=s[n]:e(t[n],s[n]):!o(t[n])&&o(s[n])?(t[n]={},s[n].__swiper__?t[n]=s[n]:e(t[n],s[n])):t[n]=s[n])}}}return t},y:()=>s});var r=i(49863);function s(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function n(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function a(){return Date.now()}function l(e,t){let i,s,n;void 0===t&&(t="x");let a=(0,r.a)(),l=function(e){let t,i=(0,r.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((s=l.transform||l.webkitTransform).split(",").length>6&&(s=s.split(", ").map(e=>e.replace(",",".")).join(", ")),n=new a.WebKitCSSMatrix("none"===s?"":s)):i=(n=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(s=a.WebKitCSSMatrix?n.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(s=a.WebKitCSSMatrix?n.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),s||0}function o(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e,t,i){e.style.setProperty(t,i)}function c(e){let t,{swiper:i,targetPosition:s,side:n}=e,a=(0,r.a)(),l=-i.translate,o=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let c=s>l?"next":"prev",u=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,p=()=>{t=new Date().getTime(),null===o&&(o=t);let e=l+(.5-Math.cos(Math.max(Math.min((t-o)/d,1),0)*Math.PI)/2)*(s-l);if(u(e,s)&&(e=s),i.wrapperEl.scrollTo({[n]:e}),u(e,s)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[n]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(p)};p()}function u(e,t){void 0===t&&(t="");let i=(0,r.a)(),s=[...e.children];return(i.HTMLSlotElement&&e instanceof HTMLSlotElement&&s.push(...e.assignedElements()),t)?s.filter(e=>e.matches(t)):s}function p(e,t){let i=(0,r.a)(),s=t.contains(e);return!s&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&((s=[...t.assignedElements()].includes(e))||(s=function(e,t){let i=[t];for(;i.length>0;){let t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),s}function h(e){try{console.warn(e);return}catch(e){}}function f(e,t){var i;void 0===t&&(t=[]);let r=document.createElement(e);return r.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),r}function m(e,t){let i=[];for(;e.previousElementSibling;){let r=e.previousElementSibling;t?r.matches(t)&&i.push(r):i.push(r),e=r}return i}function v(e,t){let i=[];for(;e.nextElementSibling;){let r=e.nextElementSibling;t?r.matches(t)&&i.push(r):i.push(r),e=r}return i}function g(e,t){return(0,r.a)().getComputedStyle(e,null).getPropertyValue(t)}function b(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function w(e,t){let i=[],r=e.parentElement;for(;r;)t?r.matches(t)&&i.push(r):i.push(r),r=r.parentElement;return i}function y(e,t,i){let s=(0,r.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function S(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function T(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}},49863:(e,t,i)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function s(e,t){void 0===e&&(e={}),void 0===t&&(t={});let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:r(t[i])&&r(e[i])&&Object.keys(t[i]).length>0&&s(e[i],t[i])})}i.d(t,{a:()=>o,g:()=>a});let n={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return s(e,n),e}let l={document:n,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){let e="undefined"!=typeof window?window:{};return s(e,l),e}},51933:function(e,t,i){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.checkValuesAgainstBoundaries=t.relativeValue=t.useThumbOverlap=t.Direction=t.getTrackBackground=t.Range=void 0,t.Range=r(i(2920)).default;var s=i(22728);Object.defineProperty(t,"getTrackBackground",{enumerable:!0,get:function(){return s.getTrackBackground}}),Object.defineProperty(t,"useThumbOverlap",{enumerable:!0,get:function(){return s.useThumbOverlap}}),Object.defineProperty(t,"relativeValue",{enumerable:!0,get:function(){return s.relativeValue}}),Object.defineProperty(t,"checkValuesAgainstBoundaries",{enumerable:!0,get:function(){return s.checkValuesAgainstBoundaries}});var n=i(29868);Object.defineProperty(t,"Direction",{enumerable:!0,get:function(){return n.Direction}})},55866:()=>{},78272:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},80462:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},84120:()=>{}};