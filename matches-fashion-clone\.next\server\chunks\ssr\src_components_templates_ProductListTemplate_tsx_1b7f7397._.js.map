{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/ProductListTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\nconst ProductListTemplate: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <div className=\"max-w-7xl mx-auto px-4 py-8\">{children}</div>\n);\n\nexport default ProductListTemplate;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,sBAA+D,CAAC,EAAE,QAAQ,EAAE,iBAChF,8OAAC;QAAI,WAAU;kBAA+B;;;;;;uCAGjC", "debugId": null}}]}