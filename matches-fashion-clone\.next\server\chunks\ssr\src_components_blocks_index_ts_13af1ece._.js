module.exports = {

"[project]/src/components/blocks/index.ts [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/components/blocks/index.ts'

Expected '>', got 'className'`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/src/components/blocks/index.ts [app-rsc] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/blocks/index.ts [app-rsc] (ecmascript)"));
}}),

};