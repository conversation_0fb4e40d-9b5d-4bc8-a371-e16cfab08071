{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page", "description": "Dynamic pages for the frontend"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "meta_title": {"type": "string", "maxLength": 60}, "meta_description": {"type": "text", "maxLength": 160}, "page_type": {"type": "enumeration", "enum": ["homepage", "category_index", "product_listing", "content_page", "landing_page", "collection_page"], "default": "content_page", "required": true}, "template": {"type": "enumeration", "enum": ["default", "homepage", "category-grid", "product-list", "full-width", "sidebar-left", "sidebar-right"], "default": "default"}, "status": {"type": "enumeration", "enum": ["draft", "published", "archived"], "default": "draft"}, "featured_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "content_blocks": {"type": "dynamiczone", "components": ["blocks.hero-section", "blocks.product-grid", "blocks.content-block", "blocks.image-gallery", "blocks.call-to-action", "blocks.featured-products", "blocks.category-showcase", "blocks.testimonials", "blocks.newsletter-signup"]}, "sidebar_content": {"type": "dynamiczone", "components": ["blocks.category-menu", "blocks.filter-widget", "blocks.featured-products", "blocks.content-block"]}, "breadcrumbs": {"type": "json"}, "parent_page": {"type": "relation", "relation": "manyToOne", "target": "api::page.page"}, "child_pages": {"type": "relation", "relation": "oneToMany", "target": "api::page.page", "mappedBy": "parent_page"}, "medusa_category_id": {"type": "string", "description": "Link to Medusa category for product pages"}, "medusa_collection_id": {"type": "string", "description": "Link to Medusa collection for collection pages"}, "custom_fields": {"type": "json", "description": "Additional custom data for the page"}, "sort_order": {"type": "integer", "default": 0}, "is_menu_item": {"type": "boolean", "default": false}, "menu_label": {"type": "string", "maxLength": 50}}}