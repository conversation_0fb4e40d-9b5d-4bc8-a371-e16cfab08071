(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{548:(e,s,r)=>{Promise.resolve().then(r.bind(r,1901))},1901:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(5155),n=r(2115),o=r(1243),i=r(3904),a=r(7550),c=r(7340);function l(e){let{error:s,reset:r}=e;n.useEffect(()=>{console.error("Route error:",s),l(s)},[s]);let l=e=>{console.log("Logging route error to external service:",{message:e.message,stack:e.stack,digest:e.digest,timestamp:new Date().toISOString(),userAgent:"undefined"!=typeof navigator?navigator.userAgent:"unknown",url:window.location.href})};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(o.A,{className:"mx-auto h-16 w-16 text-red-500"}),(0,t.jsx)("h1",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Oops! Something went wrong"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"We encountered an unexpected error while loading this page."}),!1]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,t.jsxs)("button",{onClick:r,className:"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,t.jsxs)("button",{onClick:()=>{window.location.reload()},className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Reload Page"]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,t.jsxs)("button",{onClick:()=>{window.history.back()},className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,t.jsxs)("button",{onClick:()=>{window.location.href="/"},className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,t.jsx)("p",{children:"If this problem persists, please contact our support team."}),(0,t.jsxs)("p",{className:"mt-1",children:["Error ID: ",Date.now().toString(36).toUpperCase()]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[96,358],()=>s(548)),_N_E=e.O()}]);