{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ProductCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useCart } from '@/context/CartContext';\r\nimport { Swiper, SwiperSlide } from 'swiper/react';\r\nimport { Navigation, Pagination } from 'swiper/modules';\r\nimport 'swiper/css';\r\nimport 'swiper/css/navigation';\r\nimport 'swiper/css/pagination';\r\n\r\nexport interface Variant {\r\n  color: string;\r\n  sizes: string[];\r\n  images: string[];\r\n}\r\n\r\nimport type { Product } from '@/components/ProductGrid';\r\n\r\ninterface ProductCardProps {\r\n  product: Product;\r\n  name: string;\r\n  price: number;\r\n  colorHex: string;\r\n  href: string;\r\n  variant: Variant;\r\n}\r\n\r\nexport default function ProductCard({ product, name, price, colorHex, href, variant }: ProductCardProps) {\r\n  const [showSizes, setShowSizes] = useState(false);\r\n  const { addItem } = useCart();\r\n\r\n  return (\r\n    <div\r\n      className=\"relative group\"\r\n      onMouseEnter={() => {\r\n        if (typeof window !== 'undefined' && window.innerWidth >= 1024) setShowSizes(true);\r\n      }}\r\n      onMouseLeave={() => {\r\n        if (typeof window !== 'undefined' && window.innerWidth >= 1024) setShowSizes(false);\r\n      }}\r\n    >\r\n      {/* Image Slider */}\r\n      <div className=\"relative\">\r\n        <Swiper\r\n          modules={[Navigation, Pagination]}\r\n          navigation={{ enabled: true }}\r\n          pagination={{ clickable: true }}\r\n          loop\r\n          className=\"swiper-product\"\r\n        >\r\n          {variant.images.map((img, i) => (\r\n            <SwiperSlide key={i}>\r\n              <a href={href}>\r\n                {/* eslint-disable-next-line @next/next/no-img-element */}\r\n                <img src={img} alt={`Image ${i + 1}`} className=\"w-full object-cover\" loading=\"lazy\" />\r\n              </a>\r\n            </SwiperSlide>\r\n          ))}\r\n        </Swiper>\r\n\r\n        {/* Mobile quick add icon */}\r\n        <button\r\n          onClick={() => setShowSizes(!showSizes)}\r\n          className=\"absolute bottom-2 right-2 bg-white p-1 rounded-full shadow-md block lg:hidden z-10\"\r\n        >\r\n          🛒\r\n        </button>\r\n\r\n        {/* Size selection for quick add */}\r\n        {showSizes && (\r\n          <div className=\"absolute inset-x-2 bottom-2 bg-white shadow-md flex flex-wrap justify-center gap-1 p-2 z-20\">\r\n            {variant.sizes.map((size) => (\r\n              <button\r\n                key={size}\r\n                className=\"border px-2 py-1 text-xs md:text-sm hover:bg-black hover:text-white\"\r\n                onClick={() => {\r\n                  addItem(product, variant, size);\r\n                  setShowSizes(false);\r\n                }}\r\n              >\r\n                {size}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Name, price, color swatch */}\r\n      <div className=\"pt-3 text-center\">\r\n        <h3 className=\"text-sm uppercase\">{name}</h3>\r\n        <p className=\"text-sm font-semibold\">₫ {price.toLocaleString()}</p>\r\n        <div className=\"flex justify-center mt-2\">\r\n          <span className=\"w-4 h-4 border border-gray-300 rounded-full\" style={{ backgroundColor: colorHex }} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;;;;AA2Be,SAAS,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAoB;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE1B,qBACE,8OAAC;QACC,WAAU;QACV,cAAc;YACZ,IAAI,gBAAkB,eAAe,OAAO,UAAU,IAAI,MAAM;;YAAkB;QACpF;QACA,cAAc;YACZ,IAAI,gBAAkB,eAAe,OAAO,UAAU,IAAI,MAAM;;YAAmB;QACrF;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,SAAM;wBACL,SAAS;4BAAC,yLAAA,CAAA,aAAU;4BAAE,yLAAA,CAAA,aAAU;yBAAC;wBACjC,YAAY;4BAAE,SAAS;wBAAK;wBAC5B,YAAY;4BAAE,WAAW;wBAAK;wBAC9B,IAAI;wBACJ,WAAU;kCAET,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,kBACxB,8OAAC,0IAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAE,MAAM;8CAEP,cAAA,8OAAC;wCAAI,KAAK;wCAAK,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG;wCAAE,WAAU;wCAAsB,SAAQ;;;;;;;;;;;+BAHhE;;;;;;;;;;kCAUtB,8OAAC;wBACC,SAAS,IAAM,aAAa,CAAC;wBAC7B,WAAU;kCACX;;;;;;oBAKA,2BACC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAEC,WAAU;gCACV,SAAS;oCACP,QAAQ,SAAS,SAAS;oCAC1B,aAAa;gCACf;0CAEC;+BAPI;;;;;;;;;;;;;;;;0BAef,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAG,MAAM,cAAc;;;;;;;kCAC5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;4BAA8C,OAAO;gCAAE,iBAAiB;4BAAS;;;;;;;;;;;;;;;;;;;;;;;AAK3G", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ProductGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { Filter, ChevronDown, X } from \"lucide-react\";\r\nimport ProductCard, { Variant } from \"@/components/ProductCard\";\r\nimport { Range, getTrackBackground } from \"react-range\";\r\n\r\nexport interface Product {\r\n  id: number | string;\r\n  name: string;\r\n  brand: string;\r\n  price: number;\r\n  originalPrice?: number;\r\n  isOnSale?: boolean;\r\n  category: string;\r\n  href: string;\r\n  variants: Variant[];\r\n}\r\n\r\ninterface ProductGridProps {\r\n  title: string;\r\n  categories: string[];\r\n  designers: string[];\r\n  colors?: string[];\r\n  initialProducts?: Product[];\r\n  query?: string;\r\n}\r\n\r\nconst ProductGrid = ({\r\n  title,\r\n  categories,\r\n  designers,\r\n  colors = [\"Black\", \"White\", \"Blue\", \"Red\", \"Green\"],\r\n  initialProducts = [],\r\n  query,\r\n}: ProductGridProps) => {\r\n  const [products, setProducts] = useState<Product[]>(initialProducts);\r\n  const [sortBy, setSortBy] = useState(\"newest\");\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);\r\n  const [selectedDesigners, setSelectedDesigners] = useState<string[]>([]);\r\n  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);\r\n  const [selectedColors, setSelectedColors] = useState<string[]>([]);\r\n  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5000]);\r\n\r\n  useEffect(() => {\r\n    const fetchProducts = async () => {\r\n      if (!query || !process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT) return;\r\n      try {\r\n        const res = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT!, {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ query }),\r\n        });\r\n        const json = await res.json();\r\n        if (json.data && Array.isArray(json.data.products)) {\r\n          setProducts(json.data.products);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch products\", err);\r\n      }\r\n    };\r\n    fetchProducts();\r\n  }, [query]);\r\n\r\n  const filteredProducts = products.filter((p) => {\r\n    const matchCategory =\r\n      selectedCategories.length === 0 || selectedCategories.includes(p.category);\r\n    const matchDesigner =\r\n      selectedDesigners.length === 0 || selectedDesigners.includes(p.brand);\r\n    const matchSize =\r\n      selectedSizes.length === 0 ||\r\n      p.variants.some((v) => selectedSizes.some((s) => v.sizes.includes(s)));\r\n    const matchColor =\r\n      selectedColors.length === 0 ||\r\n      p.variants.some((v) => selectedColors.includes(v.color));\r\n    const matchPrice = p.price >= priceRange[0] && p.price <= priceRange[1];\r\n    return (\r\n      matchCategory && matchDesigner && matchSize && matchColor && matchPrice\r\n    );\r\n  });\r\n\r\n  const sortedProducts = [...filteredProducts].sort((a, b) => {\r\n    if (sortBy === \"price-low\") return a.price - b.price;\r\n    if (sortBy === \"price-high\") return b.price - a.price;\r\n    return 0;\r\n  });\r\n\r\n  return (\r\n    <div className=\"bg-white min-h-screen\">\r\n      {/* Hero */}\r\n      <div className=\"h-40 md:h-60 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\r\n        <h1 className=\"text-3xl font-bold uppercase\">{title}</h1>\r\n      </div>\r\n\r\n      {/* Filter Bar */}\r\n      <div className=\"max-w-6xl mx-auto px-4\">\r\n        <div className=\"flex items-center justify-between py-4 border-b\">\r\n          <button\r\n            className=\"flex items-center space-x-2 text-sm uppercase\"\r\n            onClick={() => setShowFilters(true)}\r\n          >\r\n            <Filter size={16} />\r\n            <span>Filter</span>\r\n          </button>\r\n          <div className=\"relative\">\r\n            <select\r\n              value={sortBy}\r\n              onChange={(e) => setSortBy(e.target.value)}\r\n              className=\"appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black\"\r\n            >\r\n              <option value=\"newest\">Newest</option>\r\n              <option value=\"price-low\">Price: Low to High</option>\r\n              <option value=\"price-high\">Price: High to Low</option>\r\n              <option value=\"popular\">Most Popular</option>\r\n            </select>\r\n            <ChevronDown size={16} className=\"absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Product Grid */}\r\n        <div className=\"grid gap-6 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 py-6\">\r\n          {sortedProducts.map((product) => (\r\n            <ProductCard\r\n              key={product.id}\r\n              product={product}\r\n              name={product.name}\r\n              price={product.price}\r\n              href={product.href}\r\n              colorHex={product.variants[0]?.color || 'transparent'}\r\n              variant={product.variants[0]}\r\n            />\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"text-center mt-8\">\r\n          <button className=\"luxury-button-outline\">Load More</button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filter Drawer */}\r\n      {showFilters && (\r\n        <div className=\"fixed inset-0 z-50\">\r\n          <div className=\"absolute inset-0 bg-black/50\" onClick={() => setShowFilters(false)} />\r\n          <div className=\"absolute right-0 top-0 bottom-0 w-80 bg-white p-6 overflow-y-auto\">\r\n            <button\r\n              className=\"flex items-center space-x-1 mb-4 text-sm uppercase\"\r\n              onClick={() => setShowFilters(false)}\r\n            >\r\n              <X size={16} />\r\n              <span>Close</span>\r\n            </button>\r\n\r\n            <div className=\"space-y-8\">\r\n              {/* Categories */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Categories</h3>\r\n                <ul className=\"space-y-2\">\r\n                  {categories.map((category, index) => (\r\n                    <li key={index} className=\"flex items-center space-x-2\">\r\n                      <input\r\n                        id={`drawer-cat-${index}`}\r\n                        type=\"checkbox\"\r\n                        checked={selectedCategories.includes(category)}\r\n                        onChange={() =>\r\n                          setSelectedCategories((prev) =>\r\n                            prev.includes(category)\r\n                              ? prev.filter((c) => c !== category)\r\n                              : [...prev, category]\r\n                          )\r\n                        }\r\n                        className=\"accent-black\"\r\n                      />\r\n                      <label htmlFor={`drawer-cat-${index}`} className=\"text-sm text-gray-600\">\r\n                        {category}\r\n                      </label>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Designers */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Designers</h3>\r\n                <ul className=\"space-y-2 max-h-48 overflow-y-auto pr-2\">\r\n                  {designers.map((designer, index) => (\r\n                    <li key={index} className=\"flex items-center space-x-2\">\r\n                      <input\r\n                        id={`drawer-designer-${index}`}\r\n                        type=\"checkbox\"\r\n                        checked={selectedDesigners.includes(designer)}\r\n                        onChange={() =>\r\n                          setSelectedDesigners((prev) =>\r\n                            prev.includes(designer)\r\n                              ? prev.filter((d) => d !== designer)\r\n                              : [...prev, designer]\r\n                          )\r\n                        }\r\n                        className=\"accent-black\"\r\n                      />\r\n                      <label htmlFor={`drawer-designer-${index}`} className=\"text-sm text-gray-600\">\r\n                        {designer}\r\n                      </label>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Size Filter */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Size</h3>\r\n                <div className=\"grid grid-cols-3 gap-2\">\r\n                  {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size) => (\r\n                    <button\r\n                      key={size}\r\n                      onClick={() =>\r\n                        setSelectedSizes((prev) =>\r\n                          prev.includes(size)\r\n                            ? prev.filter((s) => s !== size)\r\n                            : [...prev, size]\r\n                        )\r\n                      }\r\n                      className={`border py-2 text-sm transition-colors ${\r\n                        selectedSizes.includes(size)\r\n                          ? \"border-black bg-black text-white\"\r\n                          : \"border-gray-300 hover:border-black\"\r\n                      }`}\r\n                    >\r\n                      {size}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Color Filter */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Color</h3>\r\n                <ul className=\"space-y-2\">\r\n                  {colors.map((color, index) => (\r\n                    <li key={index} className=\"flex items-center space-x-2\">\r\n                      <input\r\n                        id={`drawer-color-${index}`}\r\n                        type=\"checkbox\"\r\n                        checked={selectedColors.includes(color)}\r\n                        onChange={() =>\r\n                          setSelectedColors((prev) =>\r\n                            prev.includes(color)\r\n                              ? prev.filter((c) => c !== color)\r\n                              : [...prev, color]\r\n                          )\r\n                        }\r\n                        className=\"accent-black\"\r\n                      />\r\n                      <label htmlFor={`drawer-color-${index}`} className=\"text-sm text-gray-600 flex items-center space-x-1\">\r\n                        <span className=\"w-3 h-3 inline-block border\" style={{ backgroundColor: color.toLowerCase() }} />\r\n                        <span>{color}</span>\r\n                      </label>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Price Filter */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Price</h3>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex space-x-2\">\r\n                    <input\r\n                      type=\"number\"\r\n                      min={0}\r\n                      max={5000}\r\n                      value={priceRange[0]}\r\n                      onChange={(e) => {\r\n                        const val = Number(e.target.value);\r\n                        setPriceRange((prev) => [Math.min(val, prev[1]), prev[1]]);\r\n                      }}\r\n                      className=\"w-full border px-2 py-1 text-sm\"\r\n                    />\r\n                    <input\r\n                      type=\"number\"\r\n                      min={0}\r\n                      max={5000}\r\n                      value={priceRange[1]}\r\n                      onChange={(e) => {\r\n                        const val = Number(e.target.value);\r\n                        setPriceRange((prev) => [prev[0], Math.max(val, prev[0])]);\r\n                      }}\r\n                      className=\"w-full border px-2 py-1 text-sm\"\r\n                    />\r\n                  </div>\r\n                  <Range\r\n                    values={priceRange}\r\n                    step={10}\r\n                    min={0}\r\n                    max={5000}\r\n                    onChange={(values) => setPriceRange([values[0], values[1]])}\r\n                    renderTrack={({ props, children }) => (\r\n                      <div\r\n                        {...props}\r\n                        className=\"w-full h-2 rounded bg-gray-200\"\r\n                        style={{\r\n                          ...props.style,\r\n                          background: getTrackBackground({\r\n                            values: priceRange,\r\n                            colors: [\"#d1d5db\", \"#000\", \"#d1d5db\"],\r\n                            min: 0,\r\n                            max: 5000,\r\n                          }),\r\n                        }}\r\n                      >\r\n                        {children}\r\n                      </div>\r\n                    )}\r\n                    renderThumb={({ props }) => (\r\n                      <div\r\n                        {...props}\r\n                        className=\"w-4 h-4 bg-white border border-gray-400 rounded-full focus:outline-none\"\r\n                      />\r\n                    )}\r\n                  />\r\n                </div>\r\n                <p className=\"text-xs text-gray-500 mt-1\">£{priceRange[0]} - £{priceRange[1]}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductGrid;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AA4BA,MAAM,cAAc,CAAC,EACnB,KAAK,EACL,UAAU,EACV,SAAS,EACT,SAAS;IAAC;IAAS;IAAS;IAAQ;IAAO;CAAQ,EACnD,kBAAkB,EAAE,EACpB,KAAK,EACY;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAK;IAExE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,4BAA4B,EAAE;YACzD,IAAI;gBACF,MAAM,MAAM,MAAM,MAAM,QAAQ,GAAG,CAAC,4BAA4B,EAAG;oBACjE,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAM;gBAC/B;gBACA,MAAM,OAAO,MAAM,IAAI,IAAI;gBAC3B,IAAI,KAAK,IAAI,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,GAAG;oBAClD,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAChC;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;QACA;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,gBACJ,mBAAmB,MAAM,KAAK,KAAK,mBAAmB,QAAQ,CAAC,EAAE,QAAQ;QAC3E,MAAM,gBACJ,kBAAkB,MAAM,KAAK,KAAK,kBAAkB,QAAQ,CAAC,EAAE,KAAK;QACtE,MAAM,YACJ,cAAc,MAAM,KAAK,KACzB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAM,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,CAAC,QAAQ,CAAC;QACpE,MAAM,aACJ,eAAe,MAAM,KAAK,KAC1B,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAM,eAAe,QAAQ,CAAC,EAAE,KAAK;QACxD,MAAM,aAAa,EAAE,KAAK,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,UAAU,CAAC,EAAE;QACvE,OACE,iBAAiB,iBAAiB,aAAa,cAAc;IAEjE;IAEA,MAAM,iBAAiB;WAAI;KAAiB,CAAC,IAAI,CAAC,CAAC,GAAG;QACpD,IAAI,WAAW,aAAa,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QACpD,IAAI,WAAW,cAAc,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QACrD,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAgC;;;;;;;;;;;0BAIhD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;kDACd,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;kDAE1B,8OAAC,oNAAA,CAAA,cAAW;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAKrC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC,iIAAA,CAAA,UAAW;gCAEV,SAAS;gCACT,MAAM,QAAQ,IAAI;gCAClB,OAAO,QAAQ,KAAK;gCACpB,MAAM,QAAQ,IAAI;gCAClB,UAAU,QAAQ,QAAQ,CAAC,EAAE,EAAE,SAAS;gCACxC,SAAS,QAAQ,QAAQ,CAAC,EAAE;+BANvB,QAAQ,EAAE;;;;;;;;;;kCAWrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAK7C,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA+B,SAAS,IAAM,eAAe;;;;;;kCAC5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;kDACT,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;0DACX,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEACC,IAAI,CAAC,WAAW,EAAE,OAAO;gEACzB,MAAK;gEACL,SAAS,mBAAmB,QAAQ,CAAC;gEACrC,UAAU,IACR,sBAAsB,CAAC,OACrB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,YACzB;+EAAI;4EAAM;yEAAS;gEAG3B,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAS,CAAC,WAAW,EAAE,OAAO;gEAAE,WAAU;0EAC9C;;;;;;;uDAfI;;;;;;;;;;;;;;;;kDAuBf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;0DACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEACC,IAAI,CAAC,gBAAgB,EAAE,OAAO;gEAC9B,MAAK;gEACL,SAAS,kBAAkB,QAAQ,CAAC;gEACpC,UAAU,IACR,qBAAqB,CAAC,OACpB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,YACzB;+EAAI;4EAAM;yEAAS;gEAG3B,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAS,CAAC,gBAAgB,EAAE,OAAO;gEAAE,WAAU;0EACnD;;;;;;;uDAfI;;;;;;;;;;;;;;;;kDAuBf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAM;oDAAK;oDAAK;oDAAK;iDAAK,CAAC,GAAG,CAAC,CAAC,qBAChC,8OAAC;wDAEC,SAAS,IACP,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,QACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,QACzB;uEAAI;oEAAM;iEAAK;wDAGvB,WAAW,CAAC,sCAAsC,EAChD,cAAc,QAAQ,CAAC,QACnB,qCACA,sCACJ;kEAED;uDAdI;;;;;;;;;;;;;;;;kDAqBb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;0DACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEACC,IAAI,CAAC,aAAa,EAAE,OAAO;gEAC3B,MAAK;gEACL,SAAS,eAAe,QAAQ,CAAC;gEACjC,UAAU,IACR,kBAAkB,CAAC,OACjB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,SACzB;+EAAI;4EAAM;yEAAM;gEAGxB,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAS,CAAC,aAAa,EAAE,OAAO;gEAAE,WAAU;;kFACjD,8OAAC;wEAAK,WAAU;wEAA8B,OAAO;4EAAE,iBAAiB,MAAM,WAAW;wEAAG;;;;;;kFAC5F,8OAAC;kFAAM;;;;;;;;;;;;;uDAhBF;;;;;;;;;;;;;;;;kDAwBf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,KAAK;gEACL,KAAK;gEACL,OAAO,UAAU,CAAC,EAAE;gEACpB,UAAU,CAAC;oEACT,MAAM,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oEACjC,cAAc,CAAC,OAAS;4EAAC,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE;4EAAG,IAAI,CAAC,EAAE;yEAAC;gEAC3D;gEACA,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,KAAK;gEACL,KAAK;gEACL,OAAO,UAAU,CAAC,EAAE;gEACpB,UAAU,CAAC;oEACT,MAAM,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oEACjC,cAAc,CAAC,OAAS;4EAAC,IAAI,CAAC,EAAE;4EAAE,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE;yEAAE;gEAC3D;gEACA,WAAU;;;;;;;;;;;;kEAGd,8OAAC,8IAAA,CAAA,QAAK;wDACJ,QAAQ;wDACR,MAAM;wDACN,KAAK;wDACL,KAAK;wDACL,UAAU,CAAC,SAAW,cAAc;gEAAC,MAAM,CAAC,EAAE;gEAAE,MAAM,CAAC,EAAE;6DAAC;wDAC1D,aAAa,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,iBAC/B,8OAAC;gEACE,GAAG,KAAK;gEACT,WAAU;gEACV,OAAO;oEACL,GAAG,MAAM,KAAK;oEACd,YAAY,CAAA,GAAA,8IAAA,CAAA,qBAAkB,AAAD,EAAE;wEAC7B,QAAQ;wEACR,QAAQ;4EAAC;4EAAW;4EAAQ;yEAAU;wEACtC,KAAK;wEACL,KAAK;oEACP;gEACF;0EAEC;;;;;;wDAGL,aAAa,CAAC,EAAE,KAAK,EAAE,iBACrB,8OAAC;gEACE,GAAG,KAAK;gEACT,WAAU;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAE,WAAU;;oDAA6B;oDAAE,UAAU,CAAC,EAAE;oDAAC;oDAAK,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5F;uCAEe", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/womens/shop/clothing/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ProductGrid, { Product } from '@/components/ProductGrid';\r\n\r\nexport default function WomensClothingPage() {\r\n  const sizeOptions = ['XS', 'S', 'M', 'L', 'XL'];\r\n  const colorOptions = ['Black', 'White', 'Blue', 'Red', 'Green'];\r\n  const products: Product[] = Array.from({ length: 24 }, (_, i) => {\r\n    const variants = colorOptions.slice(0, 2).map((color, idx) => ({\r\n      color,\r\n      images: [\r\n        `https://picsum.photos/seed/womens-${i}-${idx}-1/600/800`,\r\n        `https://picsum.photos/seed/womens-${i}-${idx}-2/600/800`,\r\n        `https://picsum.photos/seed/womens-${i}-${idx}-3/600/800`,\r\n      ],\r\n      sizes: sizeOptions.filter(() => Math.random() > 0.3),\r\n    }));\r\n    return {\r\n      id: i + 1,\r\n      name: `Designer Item ${i + 1}`,\r\n      brand: ['<PERSON><PERSON>', '<PERSON> Laurent', 'Botte<PERSON> Veneta', 'The Row', '<PERSON>haite'][i % 5],\r\n      price: Math.floor(Math.random() * 2000) + 200,\r\n      originalPrice: Math.floor(Math.random() * 2000) + 200,\r\n      isOnSale: Math.random() > 0.7,\r\n      category: ['Dresses', 'Coats', 'Tops', 'Trousers', 'Knitwear'][i % 5],\r\n      href: `/womens/product/${i + 1}`,\r\n      variants,\r\n    };\r\n  });\r\n\r\n  const categories = [\r\n    'Shop all',\r\n    'Activewear',\r\n    'Beachwear',\r\n    'Bridal',\r\n    'Cardigans',\r\n    'Coats',\r\n    'Denim',\r\n    'Dresses',\r\n    'Jackets',\r\n    'Jeans',\r\n    'Jumpsuits',\r\n    'Knitwear',\r\n    'Lingerie and nightwear',\r\n    'Loungewear',\r\n    'Matching sets',\r\n    'Skirts',\r\n    'Suits',\r\n    'Swimwear',\r\n    'Tops',\r\n    'Trousers',\r\n  ];\r\n\r\n  const designers = [\r\n    'ALAÏA',\r\n    'Alexander McQueen',\r\n    'Balenciaga',\r\n    'Bottega Veneta',\r\n    'Dolce & Gabbana',\r\n    'Erdem',\r\n    'Gabriela Hearst',\r\n    'Gucci',\r\n    'Isabel Marant',\r\n    'Jacquemus',\r\n    'Khaite',\r\n    'LOEWE',\r\n    'Max Mara',\r\n    'Moncler',\r\n    'Saint Laurent',\r\n    'The Row',\r\n    'Toteme',\r\n    'Valentino Garavani',\r\n    'Zimmermann',\r\n  ];\r\n\r\n  return (\r\n    <ProductGrid\r\n      title=\"Women's Clothing\"\r\n      categories={categories}\r\n      designers={designers}\r\n      initialProducts={products}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,cAAc;QAAC;QAAM;QAAK;QAAK;QAAK;KAAK;IAC/C,MAAM,eAAe;QAAC;QAAS;QAAS;QAAQ;QAAO;KAAQ;IAC/D,MAAM,WAAsB,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG;QACzD,MAAM,WAAW,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,MAAQ,CAAC;gBAC7D;gBACA,QAAQ;oBACN,CAAC,kCAAkC,EAAE,EAAE,CAAC,EAAE,IAAI,UAAU,CAAC;oBACzD,CAAC,kCAAkC,EAAE,EAAE,CAAC,EAAE,IAAI,UAAU,CAAC;oBACzD,CAAC,kCAAkC,EAAE,EAAE,CAAC,EAAE,IAAI,UAAU,CAAC;iBAC1D;gBACD,OAAO,YAAY,MAAM,CAAC,IAAM,KAAK,MAAM,KAAK;YAClD,CAAC;QACD,OAAO;YACL,IAAI,IAAI;YACR,MAAM,CAAC,cAAc,EAAE,IAAI,GAAG;YAC9B,OAAO;gBAAC;gBAAS;gBAAiB;gBAAkB;gBAAW;aAAS,CAAC,IAAI,EAAE;YAC/E,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAC1C,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAClD,UAAU,KAAK,MAAM,KAAK;YAC1B,UAAU;gBAAC;gBAAW;gBAAS;gBAAQ;gBAAY;aAAW,CAAC,IAAI,EAAE;YACrE,MAAM,CAAC,gBAAgB,EAAE,IAAI,GAAG;YAChC;QACF;IACF;IAEA,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC,iIAAA,CAAA,UAAW;QACV,OAAM;QACN,YAAY;QACZ,WAAW;QACX,iBAAiB;;;;;;AAGvB", "debugId": null}}]}