// Use environment variables directly to avoid import issues
const STRAPI_API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
const isDevelopment = () => process.env.NODE_ENV === 'development';

// Environment variables are already defined above

// Enhanced error class for Strapi API errors
export class StrapiApiError extends Error {
  constructor(message, status, statusText, url, responseBody, code) {
    super(message);
    this.name = 'StrapiApiError';
    this.status = status;
    this.statusText = statusText;
    this.url = url;
    this.responseBody = responseBody;
    this.code = code;
  }
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  retryableStatuses: [408, 429, 500, 502, 503, 504],
};

// Helper function to determine if an error is retryable
const isRetryableError = (status) => {
  return RETRY_CONFIG.retryableStatuses.includes(status);
};

// Helper function to wait for a specified delay
const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Enhanced fetch function with retry logic and better error handling
export async function fetchStrapiAPI(path, params = {}, options = {}) {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (STRAPI_API_TOKEN) {
    headers['Authorization'] = `Bearer ${STRAPI_API_TOKEN}`;
  }

  const mergedOptions = {
    ...options,
    headers,
  };

  const requestPath = path.startsWith('/') ? path : `/${path}`;
  const queryString = new URLSearchParams(params).toString();
  const requestUrl = `${STRAPI_API_URL}/api${requestPath}${queryString ? `?${queryString}` : ''}`;

  let lastError = null;

  // Retry logic
  for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {
    try {
      if (isDevelopment() && attempt > 0) {
        console.log(`🔄 Retrying Strapi API request (attempt ${attempt + 1}/${RETRY_CONFIG.maxRetries + 1}): ${requestUrl}`);
      }

      const response = await fetch(requestUrl, mergedOptions);

      if (!response.ok) {
        const errorBody = await response.text();
        const error = new StrapiApiError(
          `Strapi API request failed: ${response.status} ${response.statusText}`,
          response.status,
          response.statusText,
          requestUrl,
          errorBody
        );

        // Log error details
        console.error("Strapi API Error:", {
          status: response.status,
          statusText: response.statusText,
          url: requestUrl,
          body: errorBody,
          attempt: attempt + 1,
        });

        // Check if we should retry
        if (attempt < RETRY_CONFIG.maxRetries && isRetryableError(response.status)) {
          lastError = error;
          await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt)); // Exponential backoff
          continue;
        }

        // Return structured error response
        return {
          data: undefined,
          error: {
            message: getUserFriendlyErrorMessage(response.status),
            code: `STRAPI_${response.status}`,
            status: response.status,
            details: {
              originalMessage: error.message,
              url: requestUrl,
              body: errorBody,
            },
          },
          success: false,
        };
      }

      // Success - parse and return data
      const data = await response.json();

      if (isDevelopment()) {
        console.log(`✅ Strapi API success: ${requestUrl}`);
      }

      return {
        data,
        success: true,
      };

    } catch (error) {
      const networkError = error;
      lastError = networkError;

      console.error("Network error fetching from Strapi API:", {
        error: networkError.message,
        url: requestUrl,
        attempt: attempt + 1,
      });

      // Retry on network errors
      if (attempt < RETRY_CONFIG.maxRetries) {
        await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt));
        continue;
      }

      // Return structured error response for network errors
      return {
        data: undefined,
        error: {
          message: "Unable to connect to the content management system. Please check your internet connection and try again.",
          code: 'STRAPI_NETWORK_ERROR',
          status: 0,
          details: {
            originalMessage: networkError.message,
            url: requestUrl,
          },
        },
        success: false,
      };
    }
  }

  // This should never be reached, but just in case
  return {
    data: undefined,
    error: {
      message: "An unexpected error occurred while fetching data.",
      code: 'STRAPI_UNKNOWN_ERROR',
      status: 0,
      details: {
        originalMessage: lastError?.message || 'Unknown error',
        url: requestUrl,
      },
    },
    success: false,
  };
}

// Helper function to provide user-friendly error messages
const getUserFriendlyErrorMessage = (status) => {
  switch (status) {
    case 400:
      return "The request was invalid. Please try again.";
    case 401:
      return "Authentication failed. Please refresh the page and try again.";
    case 403:
      return "Access denied. You don't have permission to access this content.";
    case 404:
      return "The requested content was not found.";
    case 408:
      return "The request timed out. Please try again.";
    case 429:
      return "Too many requests. Please wait a moment and try again.";
    case 500:
      return "A server error occurred. Please try again later.";
    case 502:
      return "The content management system is temporarily unavailable. Please try again later.";
    case 503:
      return "The service is temporarily unavailable. Please try again later.";
    case 504:
      return "The request timed out. Please try again.";
    default:
      return "An unexpected error occurred. Please try again later.";
  }
};

// Legacy function for backward compatibility
export async function fetchStrapiAPILegacy(path, params = {}, options = {}) {
  const result = await fetchStrapiAPI(path, params, options);

  if (!result.success) {
    throw new Error(result.error?.message || 'Strapi API request failed');
  }

  return result.data;
}
