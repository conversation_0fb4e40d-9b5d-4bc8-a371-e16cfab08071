"use strict";(()=>{var e={};e.id=211,e.ids=[211],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28127:(e,s,r)=>{r.r(s),r.d(s,{default:()=>t});var a=r(37413),i=r(4536),l=r.n(i);function t(){return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("nav",{className:"text-sm text-gray-500",children:[(0,a.jsx)(l(),{href:"/",className:"hover:text-black",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-black",children:"Delivery"})]})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-8",children:"Delivery Information"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"UK Delivery"}),(0,a.jsx)("div",{className:"bg-gray-50 p-6 space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Standard Delivery"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"3-5 working days"}),(0,a.jsx)("p",{className:"text-sm",children:"FREE on orders over \xa3200"}),(0,a.jsx)("p",{className:"text-sm",children:"\xa35 on orders under \xa3200"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Express Delivery"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Next working day"}),(0,a.jsx)("p",{className:"text-sm",children:"\xa315 (order by 2pm)"})]})]})})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"International Delivery"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We deliver to over 190 countries worldwide. Delivery times and costs vary by destination."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Europe"}),(0,a.jsx)("p",{className:"text-gray-600",children:"3-7 working days"}),(0,a.jsx)("p",{children:"From \xa315"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"USA & Canada"}),(0,a.jsx)("p",{className:"text-gray-600",children:"5-10 working days"}),(0,a.jsx)("p",{children:"From \xa325"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Rest of World"}),(0,a.jsx)("p",{className:"text-gray-600",children:"7-14 working days"}),(0,a.jsx)("p",{children:"From \xa335"})]})]})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Order Processing"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Orders are processed Monday to Friday, excluding bank holidays. Orders placed after 2pm on Friday will be processed the following Monday."}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-600",children:[(0,a.jsx)("li",{children:"You will receive an order confirmation email immediately after placing your order"}),(0,a.jsx)("li",{children:"A dispatch confirmation with tracking information will be sent when your order ships"}),(0,a.jsx)("li",{children:"Track your order using the link provided in your dispatch email"})]})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Special Delivery Services"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Saturday Delivery"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Available for UK orders"}),(0,a.jsx)("p",{className:"text-sm",children:"\xa325 additional charge"})]}),(0,a.jsxs)("div",{className:"border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Nominated Day Delivery"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Choose your delivery date"}),(0,a.jsx)("p",{className:"text-sm",children:"\xa320 additional charge"})]})]})]}),(0,a.jsxs)("section",{className:"bg-gray-50 p-6 text-center",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Need help with your delivery?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Contact our customer service team for assistance"}),(0,a.jsx)(l(),{href:"/help",className:"luxury-button-outline",children:"Contact Us"})]})]})]})]})}},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},42022:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>t.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o});var a=r(65239),i=r(48088),l=r(88170),t=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["delivery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28127)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\delivery\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\delivery\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/delivery/page",pathname:"/delivery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,20,137],()=>r(42022));module.exports=a})();