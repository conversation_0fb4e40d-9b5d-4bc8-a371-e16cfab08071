(()=>{var e={};e.id=861,e.ids=[861],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:o,objectFit:s}=e,l=i?40*i:t,a=n?40*n:r,u=l&&a?"viewBox='0 0 "+l+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:o}=e,s=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+s+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>u});var i=r(65239),n=r(48088),o=r(88170),s=r.n(o),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let u={children:["",{children:["product",{children:["[handle]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70872)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\product\\[handle]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\product\\[handle]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/product/[handle]/page",pathname:"/product/[handle]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(21122);let i=r(1322),n=r(27894),o=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:b,quality:v,width:y,height:_,fill:x=!1,style:w,overrideSrc:j,onLoad:E,onLoadingComplete:P,placeholder:O="empty",blurDataURL:R,fetchPriority:C,decoding:S="async",layout:M,objectFit:D,objectPosition:N,lazyBoundary:k,lazyRoot:z,...I}=e,{imgConf:T,showAltText:A,blurComplete:F,defaultLoader:B}=t,q=T||n.imageConfigDefault;if("allSizes"in q)u=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),i=null==(r=q.qualities)?void 0:r.sort((e,t)=>e-t);u={...q,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===B)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=I.loader||B;delete I.loader,delete I.srcSet;let L="__next_img_default"in G;if(L){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...i}=t;return e(i)}}if(M){"fill"===M&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let U="",X=l(y),W=l(_);if((a=f)&&"object"==typeof a&&(s(a)||void 0!==a.src)){let e=s(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,R=R||e.blurDataURL,U=e.src,!x)if(X||W){if(X&&!W){let t=X/e.width;W=Math.round(e.height*t)}else if(!X&&W){let t=W/e.height;X=Math.round(e.width*t)}}else X=e.width,W=e.height}let $=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:U)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,$=!1),u.unoptimized&&(m=!0),L&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=l(v),V=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:D,objectPosition:N}:{},A?{}:{color:"transparent"},w),J=F||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:X,heightInt:W,blurWidth:d,blurHeight:c,blurDataURL:R||"",objectFit:V.objectFit})+'")':'url("'+O+'")',Y=o.includes(V.objectFit)?"fill"===V.objectFit?"100% 100%":"cover":V.objectFit,K=J?{backgroundSize:Y,backgroundPosition:V.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:o,sizes:s,loader:l}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:a,kind:u}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),d=a.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:a.map((e,i)=>l({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:i+1)+u).join(", "),src:l({config:t,src:r,quality:o,width:a[d]})}}({config:u,src:f,unoptimized:m,width:X,quality:H,sizes:p,loader:G});return{props:{...I,loading:$?"lazy":g,fetchPriority:C,width:X,height:W,decoding:S,className:b,style:{...V,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:j||Q.src},meta:{unoptimized:m,priority:h,placeholder:O,fill:x}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.AmpContext},17903:(e,t,r)=>{"use strict";e.exports=r(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},21820:e=>{"use strict";e.exports=require("os")},24524:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23))},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return c}});let i=r(59630),n=r(84441),o=r(60687),s=n._(r(43210)),l=i._(r(47755)),a=r(14959),u=r(89513),d=r(34604);function c(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(50148);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,i={};return n=>{let o=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?o=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=n.props[t],r=i[t]||new Set;("name"!==t||!s)&&r.has(e)?o=!1:(r.add(e),i[t]=r)}}}return o}}()).reverse().map((e,t)=>{let i=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:i})})}let h=function(e){let{children:t}=e,r=(0,s.useContext)(a.AmpStateContext),i=(0,s.useContext)(u.HeadManagerContext);return(0,o.jsx)(l.default,{reduceComponentsToState:m,headManager:i,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:o}=e,s=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+s+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},33873:e=>{"use strict";e.exports=require("path")},34604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||r&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},37676:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23))},41480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:o,objectFit:s}=e,l=i?40*i:t,a=n?40*n:r,u=l&&a?"viewBox='0 0 "+l+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},44953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(50148);let i=r(41480),n=r(12756),o=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:b,quality:v,width:y,height:_,fill:x=!1,style:w,overrideSrc:j,onLoad:E,onLoadingComplete:P,placeholder:O="empty",blurDataURL:R,fetchPriority:C,decoding:S="async",layout:M,objectFit:D,objectPosition:N,lazyBoundary:k,lazyRoot:z,...I}=e,{imgConf:T,showAltText:A,blurComplete:F,defaultLoader:B}=t,q=T||n.imageConfigDefault;if("allSizes"in q)u=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),i=null==(r=q.qualities)?void 0:r.sort((e,t)=>e-t);u={...q,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===B)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=I.loader||B;delete I.loader,delete I.srcSet;let L="__next_img_default"in G;if(L){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...i}=t;return e(i)}}if(M){"fill"===M&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let U="",X=l(y),W=l(_);if((a=f)&&"object"==typeof a&&(s(a)||void 0!==a.src)){let e=s(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,R=R||e.blurDataURL,U=e.src,!x)if(X||W){if(X&&!W){let t=X/e.width;W=Math.round(e.height*t)}else if(!X&&W){let t=W/e.height;X=Math.round(e.width*t)}}else X=e.width,W=e.height}let $=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:U)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,$=!1),u.unoptimized&&(m=!0),L&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=l(v),V=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:D,objectPosition:N}:{},A?{}:{color:"transparent"},w),J=F||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:X,heightInt:W,blurWidth:d,blurHeight:c,blurDataURL:R||"",objectFit:V.objectFit})+'")':'url("'+O+'")',Y=o.includes(V.objectFit)?"fill"===V.objectFit?"100% 100%":"cover":V.objectFit,K=J?{backgroundSize:Y,backgroundPosition:V.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:o,sizes:s,loader:l}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:a,kind:u}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),d=a.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:a.map((e,i)=>l({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:i+1)+u).join(", "),src:l({config:t,src:r,quality:o,width:a[d]})}}({config:u,src:f,unoptimized:m,width:X,quality:H,sizes:p,loader:G});return{props:{...I,loading:$?"lazy":g,fetchPriority:C,width:X,height:W,decoding:S,className:b,style:{...V,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:j||Q.src},meta:{unoptimized:m,priority:h,placeholder:O,fill:x}}}},46533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let i=r(59630),n=r(84441),o=r(60687),s=n._(r(43210)),l=i._(r(51215)),a=i._(r(30512)),u=r(44953),d=r(12756),c=r(17903);r(50148);let f=r(69148),p=i._(r(1933)),m=r(53038),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,i,n,o,s){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function b(e){return s.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,s.forwardRef)((e,t)=>{let{src:r,srcSet:i,sizes:n,height:l,width:a,decoding:u,className:d,style:c,fetchPriority:f,placeholder:p,loading:h,unoptimized:v,fill:y,onLoadRef:_,onLoadingCompleteRef:x,setBlurComplete:w,setShowAltText:j,sizesInput:E,onLoad:P,onError:O,...R}=e,C=(0,s.useCallback)(e=>{e&&(O&&(e.src=e.src),e.complete&&g(e,p,_,x,w,v,E))},[r,p,_,x,w,O,v,E]),S=(0,m.useMergedRef)(t,C);return(0,o.jsx)("img",{...R,...b(f),loading:h,width:a,height:l,decoding:u,"data-nimg":y?"fill":"1",className:d,style:c,sizes:n,srcSet:i,src:r,ref:S,onLoad:e=>{g(e.currentTarget,p,_,x,w,v,E)},onError:e=>{j(!0),"empty"!==p&&w(!0),O&&O(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,i={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...b(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,i),null):(0,o.jsx)(a.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...i},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,s.forwardRef)((e,t)=>{let r=(0,s.useContext)(f.RouterContext),i=(0,s.useContext)(c.ImageConfigContext),n=(0,s.useMemo)(()=>{var e;let t=h||i||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:o}},[i]),{onLoad:l,onLoadingComplete:a}=e,m=(0,s.useRef)(l);(0,s.useEffect)(()=>{m.current=l},[l]);let g=(0,s.useRef)(a);(0,s.useEffect)(()=>{g.current=a},[a]);let[b,_]=(0,s.useState)(!1),[x,w]=(0,s.useState)(!1),{props:j,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:b,showAltText:x});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v,{...j,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:_,setShowAltText:w,sizesInput:e.sizes,ref:t}),E.priority?(0,o.jsx)(y,{isAppRouter:!r,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let i=r(43210),n=()=>{},o=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function l(){if(r&&r.mountedInstances){let t=i.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),o(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},48976:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49603:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\node_modules\\next\\dist\\client\\image-component.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return n}});let i=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},getImageProps:function(){return l}});let i=r(33356),n=r(9131),o=r(49603),s=i._(r(32091));function l(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let a=o.Image},70872:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var i=r(37413),n=r(89339),o=r(97576),s=r(70099),l=r.n(s),a=r(4536),u=r.n(a);let d=(e,t)=>void 0===e||void 0===t?"Price unavailable":new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2}).format(e/100);async function c({params:e}){let{handle:t}=await e,r=null;console.log(`PDP: Fetching product with handle: ${t}`);try{let{products:e}=await n.j.products.list({handle:t,sales_channel_id:["sc_01JXEB1SMN7PBE8Y93SSSQWGNS"]});if(console.log(`PDP: Initial fetch by handle '${t}' (Sales Channel sc_01JXEB1SMN7PBE8Y93SSSQWGNS):`,JSON.stringify(e,null,2)),e&&e.length>0&&e[0].id){let t=e[0].id;console.log(`PDP: Found product ID: ${t}. Retrieving full details...`),r=(await n.j.products.retrieve(t)).product,console.log("PDP: Retrieved detailed product:",JSON.stringify(r,null,2))}else console.log(`PDP: Product with handle '${t}' not found by list endpoint or product has no ID.`),(0,o.notFound)()}catch(e){console.error(`PDP: Error during fetch for product with handle ${t}:`,e),(0,o.notFound)()}r||(console.log(`PDP: Product object is unexpectedly null for handle '${t}'.`),(0,o.notFound)());let s=r.collection?.title||r.subtitle||"Brand",a=r.variants?.[0],c=d(a?.prices?.[0]?.amount,a?.prices?.[0]?.currency_code);return(0,i.jsxs)("div",{className:"bg-white text-black py-8",children:[" ",(0,i.jsxs)("div",{className:"container mx-auto px-4 lg:px-8",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-600 mb-6",children:[" ",(0,i.jsx)(u(),{href:"/",className:"hover:underline text-gray-700",children:"Home"}),(0,i.jsx)("span",{className:"mx-2",children:"/"}),(0,i.jsx)("span",{className:"font-medium text-black",children:r.title})," "]}),(0,i.jsxs)("div",{className:"lg:grid lg:grid-cols-2 lg:gap-12 items-start",children:[(0,i.jsx)("div",{className:"mb-8 lg:mb-0",children:r.thumbnail?(0,i.jsxs)("div",{className:"relative bg-gray-50 w-[400px] h-[533px]",children:[" ",(0,i.jsx)(l(),{src:r.thumbnail,alt:r.title||"Product image",fill:!0,className:"object-contain w-full h-full"})]}):(0,i.jsxs)("div",{className:"w-[400px] h-[533px] bg-gray-50 flex items-center justify-center text-gray-500",children:[" ","No Image Available"]})}),(0,i.jsxs)("div",{className:"sticky top-24",children:[" ",(0,i.jsx)("p",{className:"text-xs uppercase tracking-wider text-black mb-1 font-semibold",children:"EXCLUSIVE"})," ",(0,i.jsx)("h2",{className:"text-2xl font-semibold mb-1 text-black",children:s}),(0,i.jsx)("h1",{className:"text-xl text-gray-800 mb-3",children:r.title})," ",(0,i.jsx)("p",{className:"text-2xl font-medium text-black mb-6",children:c}),r.options&&r.options.map(e=>(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsxs)("label",{htmlFor:`option-${e.id}`,className:"block text-sm font-medium text-black mb-1",children:[e.title,":"]}),(0,i.jsx)("select",{id:`option-${e.id}`,name:`option-${e.id}`,className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-400 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-none",children:e.values.map(e=>(0,i.jsx)("option",{value:e.id,children:e.value},e.id))})]},e.id)),(0,i.jsx)("button",{type:"button",className:"w-full bg-black text-white py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-white hover:text-black border border-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black mb-3 transition-colors duration-150",children:"ADD TO BAG"}),(0,i.jsxs)("button",{type:"button",className:"w-full border border-black text-black py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black flex items-center justify-center transition-colors duration-150",children:[(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z",clipRule:"evenodd"})}),"ADD TO FAVORITES"]}),(0,i.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-black border-b border-gray-300 pb-2",children:"EDITOR'S NOTE"})," ",(0,i.jsx)("p",{className:"text-sm text-gray-700 mt-2",children:r.description||"Detailed editor's note about this exclusive piece..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-black border-b border-gray-300 pb-2",children:"PRODUCT DETAILS"})," ",(0,i.jsxs)("ul",{className:"list-disc list-inside text-sm text-gray-700 mt-2 space-y-1",children:[(0,i.jsx)("li",{children:"Spring '25 Collection"}),(0,i.jsx)("li",{children:"Composition: Example Material"}),(0,i.jsx)("li",{children:"Imported"}),(0,i.jsxs)("li",{children:["Product Code: ",r.id]})]})]})]})]})]})]})]})}},70899:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,s.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,a.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,n.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(68388),n=r(52637),o=r(51846),s=r(31162),l=r(84971),a=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return s},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return a},redirect:function(){return l}});let i=r(52836),n=r(49026),o=r(19121).actionAsyncStorage;function s(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(n.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=n.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function l(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?n.RedirectType.push:n.RedirectType.replace),s(e,t,i.RedirectStatusCode.TemporaryRedirect)}function a(e,t){throw void 0===t&&(t=n.RedirectType.replace),s(e,t,i.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,n.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89339:(e,t,r)=>{"use strict";r.d(t,{j:()=>s});var i=r(81282);let n="pk_43c79b9ecb6770b72a12d710968dca86c959aa28bac1a22a5c076f27e5490bfc",o={};n&&(o["x-publishable-api-key"]=n);let s=new i.Ay({baseUrl:"http://localhost:9000",maxRetries:3,customHeaders:o})},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return n.RedirectType},forbidden:function(){return s.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return a.unstable_rethrow}});let i=r(86897),n=r(49026),o=r(62765),s=r(48976),l=r(70899),a=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,20,282,137],()=>r(7642));module.exports=i})();