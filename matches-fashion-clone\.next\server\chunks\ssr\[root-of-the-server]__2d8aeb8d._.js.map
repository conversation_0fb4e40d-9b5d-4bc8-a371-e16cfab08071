{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/MegaMenu.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\n\r\ninterface MegaMenuProps {\r\n  type: 'women' | 'men' | 'just-in' | 'designers' | 'clothing' | 'dresses' | 'shoes' | 'bags' | 'accessories' | 'jewellery' | 'home' | 'edits' | 'outlet';\r\n}\r\n\r\nconst MegaMenu = ({ type }: MegaMenuProps) => {\r\n  const womenCategories = [\r\n    {\r\n      title: 'Just In',\r\n      links: [\r\n        { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },\r\n        { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },\r\n        { name: 'Back in stock', href: '/womens/lists/back-in-stock' },\r\n        { name: 'Coming soon', href: '/womens/lists/coming-soon' },\r\n        { name: 'Exclusives', href: '/womens/lists/exclusives' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Clothing',\r\n      links: [\r\n        { name: 'Shop all', href: '/womens/shop/clothing' },\r\n        { name: 'Dresses', href: '/womens/shop/clothing/dresses' },\r\n        { name: 'Coats', href: '/womens/shop/clothing/coats' },\r\n        { name: 'Jackets', href: '/womens/shop/clothing/jackets' },\r\n        { name: 'Knitwear', href: '/womens/shop/clothing/knitwear' },\r\n        { name: 'Tops', href: '/womens/shop/clothing/tops' },\r\n        { name: 'Trousers', href: '/womens/shop/clothing/trousers' },\r\n        { name: 'Jeans', href: '/womens/shop/clothing/jeans' },\r\n        { name: 'Skirts', href: '/womens/shop/clothing/skirts' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Shoes',\r\n      links: [\r\n        { name: 'Shop all', href: '/womens/shop/shoes' },\r\n        { name: 'Boots', href: '/womens/shop/shoes/boots' },\r\n        { name: 'Heels', href: '/womens/shop/shoes/heels' },\r\n        { name: 'Flats', href: '/womens/shop/shoes/flats' },\r\n        { name: 'Trainers', href: '/womens/shop/shoes/sneakers' },\r\n        { name: 'Sandals', href: '/womens/shop/shoes/sandals' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Bags',\r\n      links: [\r\n        { name: 'Shop all', href: '/womens/shop/bags' },\r\n        { name: 'Tote bags', href: '/womens/shop/bags/tote-bags' },\r\n        { name: 'Shoulder bags', href: '/womens/shop/bags/shoulder-bags' },\r\n        { name: 'Cross-body bags', href: '/womens/shop/bags/cross-body-bags' },\r\n        { name: 'Clutch bags', href: '/womens/shop/bags/clutch-bags' },\r\n        { name: 'Mini bags', href: '/womens/shop/bags/mini-bags' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Accessories',\r\n      links: [\r\n        { name: 'View all', href: '/womens/shop/accessories' },\r\n        { name: 'Jewellery', href: '/womens/shop/jewellery-and-watches' },\r\n        { name: 'Sunglasses', href: '/womens/shop/accessories/sunglasses' },\r\n        { name: 'Belts', href: '/womens/shop/accessories/belts' },\r\n        { name: 'Scarves', href: '/womens/shop/accessories/scarves' },\r\n        { name: 'Hats', href: '/womens/shop/accessories/hats' },\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const menCategories = [\r\n    {\r\n      title: 'Just In',\r\n      links: [\r\n        { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },\r\n        { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },\r\n        { name: 'Back in stock', href: '/mens/lists/back-in-stock' },\r\n        { name: 'Coming soon', href: '/mens/lists/coming-soon' },\r\n        { name: 'Exclusives', href: '/mens/lists/exclusives' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Clothing',\r\n      links: [\r\n        { name: 'Shop all', href: '/mens/shop/clothing' },\r\n        { name: 'Suits', href: '/mens/shop/clothing/suits' },\r\n        { name: 'Blazers', href: '/mens/shop/clothing/blazers' },\r\n        { name: 'Coats', href: '/mens/shop/clothing/coats' },\r\n        { name: 'Knitwear', href: '/mens/shop/clothing/knitwear' },\r\n        { name: 'Shirts', href: '/mens/shop/clothing/casual-shirts' },\r\n        { name: 'T-shirts', href: '/mens/shop/clothing/t-shirts' },\r\n        { name: 'Trousers', href: '/mens/shop/clothing/trousers' },\r\n        { name: 'Jeans', href: '/mens/shop/clothing/jeans' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Shoes',\r\n      links: [\r\n        { name: 'Shop all', href: '/mens/shop/shoes' },\r\n        { name: 'Formal shoes', href: '/mens/shop/shoes/dress-shoes' },\r\n        { name: 'Boots', href: '/mens/shop/shoes/boots' },\r\n        { name: 'Trainers', href: '/mens/shop/shoes/sneakers' },\r\n        { name: 'Loafers', href: '/mens/shop/shoes/loafers' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Bags',\r\n      links: [\r\n        { name: 'Shop all', href: '/mens/shop/bags' },\r\n        { name: 'Backpacks', href: '/mens/shop/bags/backpacks' },\r\n        { name: 'Travel bags', href: '/mens/shop/bags/travel-bags' },\r\n        { name: 'Briefcases', href: '/mens/shop/bags/briefcases' },\r\n        { name: 'Messenger bags', href: '/mens/shop/bags/messenger-bags' },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Accessories',\r\n      links: [\r\n        { name: 'View all', href: '/mens/shop/accessories' },\r\n        { name: 'Watches', href: '/mens/shop/accessories/jewellery/watches' },\r\n        { name: 'Sunglasses', href: '/mens/shop/accessories/sunglasses' },\r\n        { name: 'Belts', href: '/mens/shop/accessories/belts' },\r\n        { name: 'Wallets', href: '/mens/shop/accessories/wallets' },\r\n        { name: 'Ties', href: '/mens/shop/accessories/ties' },\r\n      ]\r\n    }\r\n  ];\r\n\r\n  // Define categories for different menu types\r\n  const getMenuContent = () => {\r\n    switch (type) {\r\n      case 'women':\r\n        return {\r\n          categories: womenCategories,\r\n          designers: ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Khaite', 'Toteme'],\r\n          showProducts: true\r\n        };\r\n      case 'men':\r\n        return {\r\n          categories: menCategories,\r\n          designers: ['Tom Ford', 'Brunello Cucinelli', 'Stone Island', 'Thom Browne', 'Bottega Veneta', 'Gucci'],\r\n          showProducts: true\r\n        };\r\n      case 'just-in':\r\n        return {\r\n          categories: [\r\n            {\r\n              title: 'Women',\r\n              links: [\r\n                { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },\r\n                { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },\r\n                { name: 'Back in stock', href: '/womens/lists/back-in-stock' },\r\n                { name: 'Coming soon', href: '/womens/lists/coming-soon' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'Men',\r\n              links: [\r\n                { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },\r\n                { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },\r\n                { name: 'Back in stock', href: '/mens/lists/back-in-stock' },\r\n                { name: 'Coming soon', href: '/mens/lists/coming-soon' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'Categories',\r\n              links: [\r\n                { name: 'Clothing', href: '/just-in/clothing' },\r\n                { name: 'Shoes', href: '/just-in/shoes' },\r\n                { name: 'Bags', href: '/just-in/bags' },\r\n                { name: 'Accessories', href: '/just-in/accessories' },\r\n              ]\r\n            }\r\n          ],\r\n          designers: ['New Arrivals', 'Trending Now', 'Editor\\'s Picks'],\r\n          showProducts: true\r\n        };\r\n      case 'designers':\r\n        return {\r\n          categories: [\r\n            {\r\n              title: 'A-D',\r\n              links: [\r\n                { name: 'Acne Studios', href: '/designers/acne-studios' },\r\n                { name: 'Bottega Veneta', href: '/designers/bottega-veneta' },\r\n                { name: 'Celine', href: '/designers/celine' },\r\n                { name: 'Dior', href: '/designers/dior' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'E-H',\r\n              links: [\r\n                { name: 'Fendi', href: '/designers/fendi' },\r\n                { name: 'Gucci', href: '/designers/gucci' },\r\n                { name: 'Hermes', href: '/designers/hermes' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'I-M',\r\n              links: [\r\n                { name: 'Jacquemus', href: '/designers/jacquemus' },\r\n                { name: 'Khaite', href: '/designers/khaite' },\r\n                { name: 'Loewe', href: '/designers/loewe' },\r\n                { name: 'Moncler', href: '/designers/moncler' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'N-S',\r\n              links: [\r\n                { name: 'Off-White', href: '/designers/off-white' },\r\n                { name: 'Prada', href: '/designers/prada' },\r\n                { name: 'Saint Laurent', href: '/designers/saint-laurent' },\r\n              ]\r\n            },\r\n            {\r\n              title: 'T-Z',\r\n              links: [\r\n                { name: 'The Row', href: '/designers/the-row' },\r\n                { name: 'Toteme', href: '/designers/toteme' },\r\n                { name: 'Valentino', href: '/designers/valentino' },\r\n                { name: 'Zimmermann', href: '/designers/zimmermann' },\r\n              ]\r\n            }\r\n          ],\r\n          designers: ['Featured Designers', 'New Designers', 'Luxury Brands'],\r\n          showProducts: true\r\n        };\r\n      default:\r\n        return {\r\n          categories: [\r\n            {\r\n              title: 'Women',\r\n              links: [\r\n                { name: 'Shop all', href: `/womens/${type}` },\r\n                { name: 'New arrivals', href: `/womens/${type}/new` },\r\n                { name: 'Best sellers', href: `/womens/${type}/best-sellers` },\r\n              ]\r\n            },\r\n            {\r\n              title: 'Men',\r\n              links: [\r\n                { name: 'Shop all', href: `/mens/${type}` },\r\n                { name: 'New arrivals', href: `/mens/${type}/new` },\r\n                { name: 'Best sellers', href: `/mens/${type}/best-sellers` },\r\n              ]\r\n            }\r\n          ],\r\n          designers: ['Featured Brands', 'New Arrivals', 'Best Sellers'],\r\n          showProducts: false\r\n        };\r\n    }\r\n  };\r\n\r\n  const menuContent = getMenuContent();\r\n  const { categories, designers: featuredDesigners, showProducts } = menuContent;\r\n\r\n  // Calculate grid columns based on content\r\n  const totalColumns = categories.length + 1; // +1 for featured designers\r\n  const gridCols = totalColumns <= 3 ? 'grid-cols-3' :\r\n                   totalColumns <= 4 ? 'grid-cols-4' :\r\n                   totalColumns <= 5 ? 'grid-cols-5' : 'grid-cols-6';\r\n\r\n  return (\r\n    <div className=\"max-w-7xl mx-auto px-4 py-8\">\r\n        <div className={`grid ${gridCols} gap-8`}>\r\n          {/* Categories */}\r\n          {categories.map((category, index) => (\r\n            <div key={index} className=\"space-y-4\">\r\n              <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\r\n                {category.title}\r\n              </h3>\r\n              <ul className=\"space-y-2\">\r\n                {category.links.map((link, linkIndex) => (\r\n                  <li key={linkIndex}>\r\n                    <Link\r\n                      href={link.href}\r\n                      className=\"text-sm text-gray-600 hover:text-black transition-colors\"\r\n                    >\r\n                      {link.name}\r\n                    </Link>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          ))}\r\n\r\n          {/* Featured Designers */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\r\n              Featured {type === 'designers' ? 'Collections' : 'Designers'}\r\n            </h3>\r\n            <ul className=\"space-y-2\">\r\n              {featuredDesigners.map((designer, index) => (\r\n                <li key={index}>\r\n                  <Link\r\n                    href={`/${type}/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\r\n                    className=\"text-sm text-gray-600 hover:text-black transition-colors\"\r\n                  >\r\n                    {designer}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Featured Products Section - Only show for certain menu types */}\r\n        {showProducts && (\r\n          <div className=\"mt-8 pt-8 border-t border-gray-200\">\r\n            <div className=\"grid grid-cols-4 gap-6\">\r\n              {[1, 2, 3, 4].map((item) => (\r\n                <div key={item} className=\"group cursor-pointer\">\r\n                  <div className=\"aspect-square bg-gray-100 mb-3 overflow-hidden\">\r\n                    <div className=\"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\r\n                      <span className=\"text-gray-500 text-sm\">Product Image</span>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-1\">\r\n                    <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Designer Name</p>\r\n                    <p className=\"text-sm font-medium\">Product Name</p>\r\n                    <p className=\"text-sm font-semibold\">£XXX</p>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MegaMenu;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAqC;gBACzE;oBAAE,MAAM;oBAAkB,MAAM;gBAAsC;gBACtE;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;gBAC7D;oBAAE,MAAM;oBAAe,MAAM;gBAA4B;gBACzD;oBAAE,MAAM;oBAAc,MAAM;gBAA2B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAwB;gBAClD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAQ,MAAM;gBAA6B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAU,MAAM;gBAA+B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAqB;gBAC/C;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAY,MAAM;gBAA8B;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAA6B;aACvD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAoB;gBAC9C;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAkC;gBACjE;oBAAE,MAAM;oBAAmB,MAAM;gBAAoC;gBACrE;oBAAE,MAAM;oBAAe,MAAM;gBAAgC;gBAC7D;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;aAC1D;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAA2B;gBACrD;oBAAE,MAAM;oBAAa,MAAM;gBAAqC;gBAChE;oBAAE,MAAM;oBAAc,MAAM;gBAAsC;gBAClE;oBAAE,MAAM;oBAAS,MAAM;gBAAiC;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAAmC;gBAC5D;oBAAE,MAAM;oBAAQ,MAAM;gBAAgC;aACvD;QACH;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAmC;gBACvE;oBAAE,MAAM;oBAAkB,MAAM;gBAAoC;gBACpE;oBAAE,MAAM;oBAAiB,MAAM;gBAA4B;gBAC3D;oBAAE,MAAM;oBAAe,MAAM;gBAA0B;gBACvD;oBAAE,MAAM;oBAAc,MAAM;gBAAyB;aACtD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAsB;gBAChD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA8B;gBACvD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAU,MAAM;gBAAoC;gBAC5D;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;aACpD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAmB;gBAC7C;oBAAE,MAAM;oBAAgB,MAAM;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,MAAM;gBAAyB;gBAChD;oBAAE,MAAM;oBAAY,MAAM;gBAA4B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAA2B;aACrD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAkB;gBAC5C;oBAAE,MAAM;oBAAa,MAAM;gBAA4B;gBACvD;oBAAE,MAAM;oBAAe,MAAM;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAc,MAAM;gBAA6B;gBACzD;oBAAE,MAAM;oBAAkB,MAAM;gBAAiC;aAClE;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAyB;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA2C;gBACpE;oBAAE,MAAM;oBAAc,MAAM;gBAAoC;gBAChE;oBAAE,MAAM;oBAAS,MAAM;gBAA+B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAAiC;gBAC1D;oBAAE,MAAM;oBAAQ,MAAM;gBAA8B;aACrD;QACH;KACD;IAED,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,WAAW;wBAAC;wBAAS;wBAAiB;wBAAkB;wBAAW;wBAAU;qBAAS;oBACtF,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;oBACZ,WAAW;wBAAC;wBAAY;wBAAsB;wBAAgB;wBAAe;wBAAkB;qBAAQ;oBACvG,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAsB,MAAM;gCAAqC;gCACzE;oCAAE,MAAM;oCAAkB,MAAM;gCAAsC;gCACtE;oCAAE,MAAM;oCAAiB,MAAM;gCAA8B;gCAC7D;oCAAE,MAAM;oCAAe,MAAM;gCAA4B;6BAC1D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAsB,MAAM;gCAAmC;gCACvE;oCAAE,MAAM;oCAAkB,MAAM;gCAAoC;gCACpE;oCAAE,MAAM;oCAAiB,MAAM;gCAA4B;gCAC3D;oCAAE,MAAM;oCAAe,MAAM;gCAA0B;6BACxD;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM;gCAAoB;gCAC9C;oCAAE,MAAM;oCAAS,MAAM;gCAAiB;gCACxC;oCAAE,MAAM;oCAAQ,MAAM;gCAAgB;gCACtC;oCAAE,MAAM;oCAAe,MAAM;gCAAuB;6BACrD;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAgB;wBAAgB;qBAAkB;oBAC9D,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAgB,MAAM;gCAA0B;gCACxD;oCAAE,MAAM;oCAAkB,MAAM;gCAA4B;gCAC5D;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAQ,MAAM;gCAAkB;6BACzC;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;6BAC7C;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAW,MAAM;gCAAqB;6BAC/C;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAS,MAAM;gCAAmB;gCAC1C;oCAAE,MAAM;oCAAiB,MAAM;gCAA2B;6BAC3D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAW,MAAM;gCAAqB;gCAC9C;oCAAE,MAAM;oCAAU,MAAM;gCAAoB;gCAC5C;oCAAE,MAAM;oCAAa,MAAM;gCAAuB;gCAClD;oCAAE,MAAM;oCAAc,MAAM;gCAAwB;6BACrD;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAsB;wBAAiB;qBAAgB;oBACnE,cAAc;gBAChB;YACF;gBACE,OAAO;oBACL,YAAY;wBACV;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM,CAAC,QAAQ,EAAE,MAAM;gCAAC;gCAC5C;oCAAE,MAAM;oCAAgB,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;gCAAC;gCACpD;oCAAE,MAAM;oCAAgB,MAAM,CAAC,QAAQ,EAAE,KAAK,aAAa,CAAC;gCAAC;6BAC9D;wBACH;wBACA;4BACE,OAAO;4BACP,OAAO;gCACL;oCAAE,MAAM;oCAAY,MAAM,CAAC,MAAM,EAAE,MAAM;gCAAC;gCAC1C;oCAAE,MAAM;oCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC;gCAAC;gCAClD;oCAAE,MAAM;oCAAgB,MAAM,CAAC,MAAM,EAAE,KAAK,aAAa,CAAC;gCAAC;6BAC5D;wBACH;qBACD;oBACD,WAAW;wBAAC;wBAAmB;wBAAgB;qBAAe;oBAC9D,cAAc;gBAChB;QACJ;IACF;IAEA,MAAM,cAAc;IACpB,MAAM,EAAE,UAAU,EAAE,WAAW,iBAAiB,EAAE,YAAY,EAAE,GAAG;IAEnE,0CAA0C;IAC1C,MAAM,eAAe,WAAW,MAAM,GAAG,GAAG,4BAA4B;IACxE,MAAM,WAAW,gBAAgB,IAAI,gBACpB,gBAAgB,IAAI,gBACpB,gBAAgB,IAAI,gBAAgB;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC;;oBAErC,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;2BANL;;;;;kCAoBZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA8D;oCAChE,SAAS,cAAc,gBAAgB;;;;;;;0CAEnD,8OAAC;gCAAG,WAAU;0CACX,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;4CACzE,WAAU;sDAET;;;;;;uCALI;;;;;;;;;;;;;;;;;;;;;;YAchB,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAAe,WAAU;;8CACxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;2BAT/B;;;;;;;;;;;;;;;;;;;;;AAkB1B;uCAEe", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/SearchModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { X, Search, TrendingUp } from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\ninterface SearchModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\ninterface Product {\r\n  id: number;\r\n  name: string;\r\n  brand: string;\r\n  price: number;\r\n  category: string;\r\n}\r\n\r\n// Mock products data - moved outside component to avoid dependency issues\r\nconst mockProducts: Product[] = [\r\n  { id: 1, name: 'Ophidia GG Supreme bag', brand: 'Gucci', price: 1200, category: 'Bags' },\r\n  { id: 2, name: 'Opyum pumps', brand: 'Saint Laurent', price: 895, category: 'Shoes' },\r\n  { id: 3, name: 'Cashmere coat', brand: 'The Row', price: 2890, category: 'Coats' },\r\n  { id: 4, name: 'Intrecciato wallet', brand: 'Bottega Veneta', price: 450, category: 'Accessories' },\r\n];\r\n\r\nconst SearchModal = ({ isOpen, onClose }: SearchModalProps) => {\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [searchResults, setSearchResults] = useState<Product[]>([]);\r\n\r\n  // Mock search data\r\n  const trendingSearches = [\r\n    'Gucci bags',\r\n    'Saint Laurent shoes',\r\n    'The Row coats',\r\n    'Bottega Veneta',\r\n    'Khaite dresses',\r\n    'Designer jeans',\r\n    'Luxury sneakers',\r\n    'Evening dresses'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (searchQuery.length > 2) {\r\n      // Simulate search results\r\n      const filtered = mockProducts.filter(product =>\r\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        product.category.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n      setSearchResults(filtered);\r\n    } else {\r\n      setSearchResults([]);\r\n    }\r\n  }, [searchQuery]);\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 bg-white\">\r\n      {/* Header */}\r\n      <div className=\"border-b border-gray-200 p-4\">\r\n        <div className=\"max-w-4xl mx-auto flex items-center space-x-4\">\r\n          <div className=\"flex-1 relative\">\r\n            <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search for designers, products, or categories...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"w-full pl-12 pr-4 py-3 text-lg border-none outline-none\"\r\n              autoFocus\r\n            />\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n          >\r\n            <X size={24} />\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"max-w-4xl mx-auto p-4\">\r\n        {searchQuery.length === 0 ? (\r\n          /* Trending Searches */\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\r\n              <TrendingUp size={20} className=\"mr-2\" />\r\n              Trending Searches\r\n            </h3>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\r\n              {trendingSearches.map((search, index) => (\r\n                <button\r\n                  key={index}\r\n                  onClick={() => setSearchQuery(search)}\r\n                  className=\"text-left p-3 border border-gray-200 hover:border-black transition-colors text-sm\"\r\n                >\r\n                  {search}\r\n                </button>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Popular Categories */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"text-lg font-semibold mb-4\">Popular Categories</h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                <Link href=\"/womens/shop/clothing/dresses\" className=\"group\">\r\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\r\n                    <span className=\"text-gray-500 text-sm\">Dresses</span>\r\n                  </div>\r\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Women&apos;s Dresses</h4>\r\n                </Link>\r\n                <Link href=\"/womens/shop/bags\" className=\"group\">\r\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\r\n                    <span className=\"text-gray-500 text-sm\">Bags</span>\r\n                  </div>\r\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Designer Bags</h4>\r\n                </Link>\r\n                <Link href=\"/mens/shop/clothing/suits\" className=\"group\">\r\n                  <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center\">\r\n                    <span className=\"text-gray-500 text-sm\">Suits</span>\r\n                  </div>\r\n                  <h4 className=\"font-medium group-hover:text-gray-600\">Men&apos;s Suits</h4>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          /* Search Results */\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">\r\n              Search Results for &quot;{searchQuery}&quot; ({searchResults.length})\r\n            </h3>\r\n            {searchResults.length > 0 ? (\r\n              <div className=\"space-y-4\">\r\n                {searchResults.map((product) => (\r\n                  <Link\r\n                    key={product.id}\r\n                    href={`/product/${product.id}`}\r\n                    className=\"flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors\"\r\n                    onClick={onClose}\r\n                  >\r\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\">\r\n                      <span className=\"text-xs text-gray-500\">IMG</span>\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <p className=\"text-xs text-gray-500 uppercase tracking-wide\">{product.brand}</p>\r\n                      <h4 className=\"font-medium\">{product.name}</h4>\r\n                      <p className=\"text-sm text-gray-600\">£{product.price}</p>\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500\">{product.category}</div>\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-12\">\r\n                <p className=\"text-gray-500 mb-4\">No results found for &quot;{searchQuery}&quot;</p>\r\n                <p className=\"text-sm text-gray-400\">Try searching for designers, product names, or categories</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SearchModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAmBA,0EAA0E;AAC1E,MAAM,eAA0B;IAC9B;QAAE,IAAI;QAAG,MAAM;QAA0B,OAAO;QAAS,OAAO;QAAM,UAAU;IAAO;IACvF;QAAE,IAAI;QAAG,MAAM;QAAe,OAAO;QAAiB,OAAO;QAAK,UAAU;IAAQ;IACpF;QAAE,IAAI;QAAG,MAAM;QAAiB,OAAO;QAAW,OAAO;QAAM,UAAU;IAAQ;IACjF;QAAE,IAAI;QAAG,MAAM;QAAsB,OAAO;QAAkB,OAAO;QAAK,UAAU;IAAc;CACnG;AAED,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAoB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAEhE,mBAAmB;IACnB,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,0BAA0B;YAC1B,MAAM,WAAW,aAAa,MAAM,CAAC,CAAA,UACnC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YAEjE,iBAAiB;QACnB,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;oCAAmE,MAAM;;;;;;8CAC3F,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,SAAS;;;;;;;;;;;;sCAGb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,IACtB,qBAAqB,iBACrB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAET;mCAJI;;;;;;;;;;sCAUX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;;8DACnD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAoB,WAAU;;8DACvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;sDAExD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA4B,WAAU;;8DAC/C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAM9D,kBAAkB,iBAClB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;gCAA6B;gCACf;gCAAY;gCAAS,cAAc,MAAM;gCAAC;;;;;;;wBAErE,cAAc,MAAM,GAAG,kBACtB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;oCAC9B,WAAU;oCACV,SAAS;;sDAET,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAiD,QAAQ,KAAK;;;;;;8DAC3E,8OAAC;oDAAG,WAAU;8DAAe,QAAQ,IAAI;;;;;;8DACzC,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAE,QAAQ,KAAK;;;;;;;;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDAAyB,QAAQ,QAAQ;;;;;;;mCAbnD,QAAQ,EAAE;;;;;;;;;iDAkBrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAqB;wCAA4B;wCAAY;;;;;;;8CAC1E,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/context/AuthContext.tsx"], "sourcesContent": ["'use client';\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\ninterface User {\r\n  email: string;\r\n  password: string; // stored in localStorage just for demo\r\n}\r\n\r\ninterface AuthContextValue {\r\n  user: User | null;\r\n  login: (email: string, password: string) => boolean;\r\n  register: (email: string, password: string) => boolean;\r\n  logout: () => void;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined);\r\n\r\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n\r\n  useEffect(() => {\r\n    const stored = localStorage.getItem('authUser');\r\n    if (stored) {\r\n      setUser(JSON.parse(stored));\r\n    }\r\n  }, []);\r\n\r\n  const login = (email: string, password: string) => {\r\n    const stored = localStorage.getItem('authUser');\r\n    if (stored) {\r\n      const u = JSON.parse(stored) as User;\r\n      if (u.email === email && u.password === password) {\r\n        setUser(u);\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  const register = (email: string, password: string) => {\r\n    const newUser: User = { email, password };\r\n    localStorage.setItem('authUser', JSON.stringify(newUser));\r\n    setUser(newUser);\r\n    return true;\r\n  };\r\n\r\n  const logout = () => {\r\n    setUser(null);\r\n    localStorage.removeItem('authUser');\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={{ user, login, register, logout }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = () => {\r\n  const ctx = useContext(AuthContext);\r\n  if (!ctx) throw new Error('useAuth must be used within AuthProvider');\r\n  return ctx;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEzD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,QAAQ,KAAK,KAAK,CAAC;QACrB;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC,OAAe;QAC5B,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,MAAM,IAAI,KAAK,KAAK,CAAC;YACrB,IAAI,EAAE,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK,UAAU;gBAChD,QAAQ;gBACR,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,MAAM,UAAgB;YAAE;YAAO;QAAS;QACxC,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;QAChD,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAO;YAAU;QAAO;kBAC1D;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACvB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/context/CartContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\nimport type { Product } from '@/components/ProductGrid';\r\nimport type { Variant } from '@/components/ProductCard';\r\n\r\nexport interface CartItem {\r\n  id: string; // unique id for cart item\r\n  product: Product;\r\n  variant: Variant;\r\n  size: string;\r\n  quantity: number;\r\n}\r\n\r\ninterface CartContextValue {\r\n  items: CartItem[];\r\n  addItem: (product: Product, variant: Variant, size: string, quantity?: number) => void;\r\n  removeItem: (id: string) => void;\r\n  clearCart: () => void;\r\n}\r\n\r\nconst CartContext = createContext<CartContextValue | undefined>(undefined);\r\n\r\nconst STORAGE_KEY = 'cartItems';\r\n\r\nfunction randomId() {\r\n  return Math.random().toString(36).slice(2) + Date.now().toString(36);\r\n}\r\n\r\nexport const CartProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const [items, setItems] = useState<CartItem[]>([]);\r\n\r\n  // load from localStorage\r\n  useEffect(() => {\r\n    const stored = localStorage.getItem(STORAGE_KEY);\r\n    if (stored) setItems(JSON.parse(stored));\r\n  }, []);\r\n\r\n  // persist to localStorage whenever items change\r\n  useEffect(() => {\r\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(items));\r\n  }, [items]);\r\n\r\n  const addItem = (product: Product, variant: Variant, size: string, quantity = 1) => {\r\n    setItems((prev) => [\r\n      ...prev,\r\n      { id: randomId(), product, variant, size, quantity },\r\n    ]);\r\n  };\r\n\r\n  const removeItem = (id: string) => {\r\n    setItems((prev) => prev.filter((i) => i.id !== id));\r\n  };\r\n\r\n  const clearCart = () => setItems([]);\r\n\r\n  return (\r\n    <CartContext.Provider value={{ items, addItem, removeItem, clearCart }}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useCart = () => {\r\n  const ctx = useContext(CartContext);\r\n  if (!ctx) throw new Error('useCart must be used within CartProvider');\r\n  return ctx;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEhE,MAAM,cAAc;AAEpB,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACnE;AAEO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEjD,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ,SAAS,KAAK,KAAK,CAAC;IAClC,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD,GAAG;QAAC;KAAM;IAEV,MAAM,UAAU,CAAC,SAAkB,SAAkB,MAAc,WAAW,CAAC;QAC7E,SAAS,CAAC,OAAS;mBACd;gBACH;oBAAE,IAAI;oBAAY;oBAAS;oBAAS;oBAAM;gBAAS;aACpD;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACjD;IAEA,MAAM,YAAY,IAAM,SAAS,EAAE;IAEnC,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAS;YAAY;QAAU;kBAClE;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACvB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';\r\nimport MegaMenu from './MegaMenu';\r\nimport SearchModal from './SearchModal';\r\nimport { useAuth } from '@/context/AuthContext';\r\nimport { useCart } from '@/context/CartContext';\r\n\r\nconst Header = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [activeMenu, setActiveMenu] = useState<string | null>(null);\r\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\r\n  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);\r\n  const { user, logout } = useAuth();\r\n  const { items } = useCart();\r\n\r\n  useEffect(() => {\r\n    if (isMobileMenuOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isMobileMenuOpen]);\r\n\r\n  const toggleMobileMenu = () => {\r\n    setIsMobileMenuOpen(!isMobileMenuOpen);\r\n  };\r\n\r\n  const handleMenuHover = (menu: string) => {\r\n    if (hoverTimeout) {\r\n      clearTimeout(hoverTimeout);\r\n      setHoverTimeout(null);\r\n    }\r\n    setActiveMenu(menu);\r\n  };\r\n\r\n  const handleMenuLeave = () => {\r\n    const timeout = setTimeout(() => {\r\n      setActiveMenu(null);\r\n    }, 300);\r\n    setHoverTimeout(timeout);\r\n  };\r\n\r\n  return (\r\n    <header className=\"relative bg-white border-b border-gray-200\">\r\n      {/* Top Bar - Hidden on mobile */}\r\n      <div className=\"hidden md:block bg-black text-white text-xs py-2\">\r\n        <div className=\"max-w-7xl mx-auto px-4 flex justify-between items-center\">\r\n          <div className=\"flex space-x-6\">\r\n            <Link href=\"/help\" className=\"hover:underline\">Help Centre</Link>\r\n            <Link href=\"/delivery\" className=\"hover:underline\">Delivery</Link>\r\n            <Link href=\"/returns\" className=\"hover:underline\">Returns</Link>\r\n          </div>\r\n          <div className=\"flex space-x-6\">\r\n            <Link href=\"/stores\" className=\"hover:underline\">Visit Us</Link>\r\n            <Link href=\"/apps\" className=\"hover:underline\">Our Apps</Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Header */}\r\n      <div\r\n        className=\"max-w-7xl mx-auto px-4 relative\"\r\n        onMouseLeave={handleMenuLeave}\r\n      >\r\n        {/* First Line: Logo - Men - Women - Search Bar */}\r\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\r\n          {/* Mobile Menu Button */}\r\n          <button\r\n            className=\"flex md:hidden p-2 mr-4\"\r\n            onClick={toggleMobileMenu}\r\n            aria-label=\"Toggle menu\"\r\n          >\r\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\r\n          </button>\r\n\r\n          {/* Left Side: Logo + Primary Navigation */}\r\n          <div className=\"flex items-center\">\r\n            {/* Logo */}\r\n            <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\r\n              <span className=\"text-2xl md:text-3xl font-bold tracking-wider\">MATCHES</span>\r\n            </Link>\r\n\r\n            {/* Primary Categories - Men & Women */}\r\n            <nav className=\"hidden md:flex items-center space-x-8\">\r\n              <div onMouseEnter={() => handleMenuHover('men')}>\r\n                <Link\r\n                  href=\"/mens\"\r\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n                >\r\n                  Men\r\n                </Link>\r\n              </div>\r\n              <div onMouseEnter={() => handleMenuHover('women')}>\r\n                <Link\r\n                  href=\"/womens\"\r\n                  className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n                >\r\n                  Women\r\n                </Link>\r\n              </div>\r\n            </nav>\r\n          </div>\r\n\r\n          {/* Right Side: Search Bar + Icons */}\r\n          <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n            {/* Search Bar */}\r\n            <div className=\"hidden md:flex items-center bg-gray-100 rounded-full px-4 py-2 w-80\">\r\n              <Search size={16} className=\"text-gray-400 mr-2\" />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search for products, designers...\"\r\n                className=\"bg-transparent text-sm flex-1 outline-none placeholder-gray-500\"\r\n                onClick={() => setIsSearchOpen(true)}\r\n                readOnly\r\n              />\r\n            </div>\r\n\r\n            {/* Mobile Search Button */}\r\n            <button\r\n              className=\"flex md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n              onClick={() => setIsSearchOpen(true)}\r\n            >\r\n              <Search size={20} />\r\n            </button>\r\n\r\n            {/* User Icon / Login */}\r\n            <Link\r\n              href={user ? '/account' : '/account/login'}\r\n              className=\"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n            >\r\n              <User size={20} />\r\n            </Link>\r\n\r\n            {/* Settings (only if logged in) */}\r\n            {user && (\r\n              <button\r\n                onClick={logout}\r\n                className=\"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n                aria-label=\"Settings\"\r\n              >\r\n                <Settings size={20} />\r\n              </button>\r\n            )}\r\n\r\n            {/* Wishlists Link */}\r\n            <Link\r\n              href=\"/account/wishlists\"\r\n              className=\"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n            >\r\n              <Heart size={20} />\r\n            </Link>\r\n\r\n            {/* Shopping Bag */}\r\n            <Link\r\n              href=\"/cart\"\r\n              className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\"\r\n            >\r\n              <ShoppingBag size={20} />\r\n              <span className=\"absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\r\n                {items.length}\r\n              </span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Second Line: Sub Categories */}\r\n        <div className=\"hidden md:block border-t border-gray-100 py-3\">\r\n          <nav className=\"flex items-center space-x-8\">\r\n            <div onMouseEnter={() => handleMenuHover('just-in')}>\r\n              <Link\r\n                href=\"/just-in\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Just In\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('designers')}>\r\n              <Link\r\n                href=\"/designers\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Designers\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('clothing')}>\r\n              <Link\r\n                href=\"/clothing\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Clothing\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('dresses')}>\r\n              <Link\r\n                href=\"/dresses\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Dresses\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('shoes')}>\r\n              <Link\r\n                href=\"/shoes\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Shoes\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('bags')}>\r\n              <Link\r\n                href=\"/bags\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Bags\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('accessories')}>\r\n              <Link\r\n                href=\"/accessories\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Accessories\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('jewellery')}>\r\n              <Link\r\n                href=\"/jewellery-watches\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Jewellery &amp; Watches\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('home')}>\r\n              <Link\r\n                href=\"/home\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Home\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('edits')}>\r\n              <Link\r\n                href=\"/edits\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Edits\r\n              </Link>\r\n            </div>\r\n            <div onMouseEnter={() => handleMenuHover('outlet')}>\r\n              <Link\r\n                href=\"/outlet\"\r\n                className=\"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\r\n              >\r\n                Outlet\r\n              </Link>\r\n            </div>\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Mega Menu */}\r\n        {activeMenu && (\r\n          <div\r\n            className=\"absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-200 z-50\"\r\n            onMouseEnter={() => handleMenuHover(activeMenu)}\r\n          >\r\n            <MegaMenu\r\n              type={\r\n                activeMenu as\r\n                  | 'women'\r\n                  | 'men'\r\n                  | 'just-in'\r\n                  | 'designers'\r\n                  | 'clothing'\r\n                  | 'dresses'\r\n                  | 'shoes'\r\n                  | 'bags'\r\n                  | 'accessories'\r\n                  | 'jewellery'\r\n                  | 'home'\r\n                  | 'edits'\r\n                  | 'outlet'\r\n              }\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Mobile Menu Overlay */}\r\n      {isMobileMenuOpen && (\r\n        <div className=\"fixed inset-0 z-50 md:hidden\">\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" onClick={toggleMobileMenu} />\r\n          <div className=\"fixed left-0 top-0 h-full w-72 sm:w-80 bg-white shadow-xl\">\r\n            <div className=\"flex items-center justify-between p-4 border-b\">\r\n              <span className=\"text-xl font-bold\">MATCHES</span>\r\n              <button onClick={toggleMobileMenu}>\r\n                <X size={24} />\r\n              </button>\r\n            </div>\r\n            <nav className=\"p-4 overflow-y-auto\">\r\n              <div className=\"space-y-4\">\r\n                {/* Primary Categories */}\r\n                <div className=\"space-y-2\">\r\n                  <Link\r\n                    href=\"/womens\"\r\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\r\n                    onClick={toggleMobileMenu}\r\n                  >\r\n                    Women\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/mens\"\r\n                    className=\"block text-lg font-medium py-2 border-b border-gray-100\"\r\n                    onClick={toggleMobileMenu}\r\n                  >\r\n                    Men\r\n                  </Link>\r\n                </div>\r\n\r\n                {/* Secondary Categories */}\r\n                <div className=\"pt-4 space-y-2\">\r\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Shop</h3>\r\n                  <Link href=\"/just-in\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Just In</Link>\r\n                  <Link href=\"/designers\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Designers</Link>\r\n                  <Link href=\"/clothing\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Clothing</Link>\r\n                  <Link href=\"/dresses\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Dresses</Link>\r\n                  <Link href=\"/shoes\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Shoes</Link>\r\n                  <Link href=\"/bags\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Bags</Link>\r\n                  <Link href=\"/accessories\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Accessories</Link>\r\n                  <Link href=\"/jewellery-watches\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Jewellery &amp; Watches</Link>\r\n                  <Link href=\"/home\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Home</Link>\r\n                  <Link href=\"/edits\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Edits</Link>\r\n                  <Link href=\"/outlet\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Outlet</Link>\r\n                </div>\r\n\r\n                {/* Account & Support */}\r\n                <div className=\"pt-4 space-y-2 border-t border-gray-200\">\r\n                  <h3 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2\">Account</h3>\r\n                  <Link\r\n                    href={user ? '/account' : '/account/login'}\r\n                    className=\"block text-sm py-1 hover:text-gray-600\"\r\n                    onClick={toggleMobileMenu}\r\n                  >\r\n                    {user ? 'My Account' : 'Sign In'}\r\n                  </Link>\r\n                  <Link href=\"/settings\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Settings</Link>\r\n                  <Link href=\"/help\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Help Centre</Link>\r\n                  <Link href=\"/delivery\" className=\"block text-sm py-1 hover:text-gray-600\" onClick={toggleMobileMenu}>Delivery</Link>\r\n                </div>\r\n              </div>\r\n            </nav>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Search Modal */}\r\n      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc;YAChB,aAAa;YACb,gBAAgB;QAClB;QACA,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,WAAW;YACzB,cAAc;QAChB,GAAG;QACH,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAkB;;;;;;8CACnD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAkB;;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAkB;;;;;;8CACjD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBACC,WAAU;gBACV,cAAc;;kCAGd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,WAAU;gCACV,SAAS;gCACT,cAAW;0CAEV,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;kDAIlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,cAAc,IAAM,gBAAgB;0DACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;gDAAI,cAAc,IAAM,gBAAgB;0DACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAQP,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,SAAS,IAAM,gBAAgB;gDAC/B,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gBAAgB;kDAE/B,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;kDAIhB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,OAAO,aAAa;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;oCAIb,sBACC,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAKpB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;kDAIf,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DACb,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;8CAIH,8OAAC;oCAAI,cAAc,IAAM,gBAAgB;8CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;oBAQN,4BACC,8OAAC;wBACC,WAAU;wBACV,cAAc,IAAM,gBAAgB;kCAEpC,cAAA,8OAAC,8HAAA,CAAA,UAAQ;4BACP,MACE;;;;;;;;;;;;;;;;;YAqBT,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuC,SAAS;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,8OAAC;wCAAO,SAAS;kDACf,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;sDAMH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACtG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACpG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAe,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACxG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAC9G,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DAClG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAU,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;sDAIrG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmE;;;;;;8DACjF,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,OAAO,aAAa;oDAC1B,WAAU;oDACV,SAAS;8DAER,OAAO,eAAe;;;;;;8DAEzB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACrG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;8DACjG,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;oDAAyC,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjH,8OAAC,iIAAA,CAAA,UAAW;gBAAC,QAAQ;gBAAc,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAGxE;uCAEe", "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/Footer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { Instagram, Facebook, Youtube, Twitter } from 'lucide-react';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-white border-t border-gray-200\">\r\n      {/* Newsletter Section */}\r\n      <div className=\"bg-gray-50 py-12\">\r\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\r\n          <h2 className=\"text-2xl font-bold mb-4\">Stay in the know</h2>\r\n          <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\r\n            Be the first to discover new arrivals, exclusive collections, and styling tips\r\n          </p>\r\n          <div className=\"flex max-w-md mx-auto\">\r\n            <input\r\n              type=\"email\"\r\n              placeholder=\"Enter your email address\"\r\n              className=\"flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black\"\r\n            />\r\n            <button className=\"luxury-button\">\r\n              Sign up\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Footer Content */}\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\r\n          {/* MATCHES */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">MATCHES</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"/bio\" className=\"text-sm text-gray-600 hover:text-black\">About Us</Link></li>\r\n              <li><Link href=\"/careers\" className=\"text-sm text-gray-600 hover:text-black\">Careers</Link></li>\r\n              <li><Link href=\"/affiliates\" className=\"text-sm text-gray-600 hover:text-black\">Affiliates</Link></li>\r\n              <li><Link href=\"/press\" className=\"text-sm text-gray-600 hover:text-black\">Press</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Services */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Services</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"/private-shopping\" className=\"text-sm text-gray-600 hover:text-black\">Private Shopping</Link></li>\r\n              <li><Link href=\"/loyalty\" className=\"text-sm text-gray-600 hover:text-black\">Loyalty</Link></li>\r\n              <li><Link href=\"/rental\" className=\"text-sm text-gray-600 hover:text-black\">MATCHES Rental</Link></li>\r\n              <li><Link href=\"/gift-cards\" className=\"text-sm text-gray-600 hover:text-black\">Gift Cards</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Legal */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Legal</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"/terms\" className=\"text-sm text-gray-600 hover:text-black\">Terms and Conditions</Link></li>\r\n              <li><Link href=\"/privacy\" className=\"text-sm text-gray-600 hover:text-black\">Privacy Policy</Link></li>\r\n              <li><Link href=\"/cookies\" className=\"text-sm text-gray-600 hover:text-black\">Cookie Policy</Link></li>\r\n              <li><Link href=\"/modern-slavery\" className=\"text-sm text-gray-600 hover:text-black\">Modern Slavery Statement</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Visit Us */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Visit Us</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"/stores/5carlosplace\" className=\"text-sm text-gray-600 hover:text-black\">5 Carlos Place</Link></li>\r\n              <li><Link href=\"/stores/marylebone\" className=\"text-sm text-gray-600 hover:text-black\">Marylebone</Link></li>\r\n              <li><Link href=\"/stores/wimbledon\" className=\"text-sm text-gray-600 hover:text-black\">Wimbledon</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Help Centre */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Help Centre</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"/help\" className=\"text-sm text-gray-600 hover:text-black\">Help Centre</Link></li>\r\n              <li><Link href=\"/returns\" className=\"text-sm text-gray-600 hover:text-black\">Returning an item</Link></li>\r\n              <li><Link href=\"/delivery\" className=\"text-sm text-gray-600 hover:text-black\">Delivery</Link></li>\r\n              <li><Link href=\"/size-guide\" className=\"text-sm text-gray-600 hover:text-black\">Size Guide</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Social Media & Apps */}\r\n          <div className=\"lg:col-span-1\">\r\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Follow Us</h3>\r\n            <div className=\"flex space-x-4 mb-6\">\r\n              <Link href=\"https://instagram.com/matches\" className=\"text-gray-600 hover:text-black\">\r\n                <Instagram size={20} />\r\n              </Link>\r\n              <Link href=\"https://facebook.com/matches\" className=\"text-gray-600 hover:text-black\">\r\n                <Facebook size={20} />\r\n              </Link>\r\n              <Link href=\"https://youtube.com/matches\" className=\"text-gray-600 hover:text-black\">\r\n                <Youtube size={20} />\r\n              </Link>\r\n              <Link href=\"https://twitter.com/matches\" className=\"text-gray-600 hover:text-black\">\r\n                <Twitter size={20} />\r\n              </Link>\r\n            </div>\r\n            <h4 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Our Apps</h4>\r\n            <div className=\"space-y-2\">\r\n              <Link href=\"#\" className=\"block\">\r\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\r\n                  Download on the App Store\r\n                </div>\r\n              </Link>\r\n              <Link href=\"#\" className=\"block\">\r\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\r\n                  Get it on Google Play\r\n                </div>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Bar */}\r\n      <div className=\"border-t border-gray-200 py-6\">\r\n        <div className=\"max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center\">\r\n          <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\r\n            <span className=\"text-sm text-gray-600\">Shipping to</span>\r\n            <button className=\"text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black\">\r\n              United Kingdom\r\n            </button>\r\n          </div>\r\n          <div className=\"text-sm text-gray-600\">\r\n            © Copyright 2024 MATCHES\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAyC;;;;;;;;;;;sDACzE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;sDAChF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAyC;;;;;;;;;;;sDAC5E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAyC;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAyC;;;;;;;;;;;sDACvF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAyC;;;;;;;;;;;sDAC1E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyC;;;;;;;;;;;sDAC9E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;sDACnD,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA+B,WAAU;sDAClD,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;sDAElB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;sDAIjE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAO,WAAU;8CAA0E;;;;;;;;;;;;sCAI9F,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 2990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/QCTestingPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Monitor, Smartphone, Tablet, Eye, X } from 'lucide-react';\r\n\r\nconst QCTestingPanel = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');\r\n\r\n  const breakpoints = [\r\n    { id: 'mobile', name: 'Mobile', width: '375px', icon: Smartphone },\r\n    { id: 'tablet', name: 'Tablet', width: '768px', icon: Tablet },\r\n    { id: 'desktop', name: 'Desktop', width: '1920px', icon: Monitor },\r\n  ];\r\n\r\n  const testPages = [\r\n    { name: 'Homepage', path: '/' },\r\n    { name: 'Women\\'s Landing', path: '/womens' },\r\n    { name: 'Women\\'s Clothing', path: '/womens/shop/clothing' },\r\n    { name: 'Men\\'s Landing', path: '/mens' },\r\n    { name: 'Men\\'s Clothing', path: '/mens/shop/clothing' },\r\n  ];\r\n\r\n  const qcChecklist = [\r\n    'Header navigation is properly aligned',\r\n    'Logo is clearly visible and properly sized',\r\n    'Mega menus display correctly on hover',\r\n    'Product grids maintain proper spacing',\r\n    'Typography hierarchy is consistent',\r\n    'Interactive elements have proper hover states',\r\n    'Footer content is well-organized',\r\n    'Mobile navigation works smoothly',\r\n    'Search functionality is accessible',\r\n    'Product cards display correctly',\r\n  ];\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <button\r\n        onClick={() => setIsOpen(true)}\r\n        className=\"fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-50\"\r\n        title=\"Open QC Testing Panel\"\r\n      >\r\n        <Eye size={20} />\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\r\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b\">\r\n          <h2 className=\"text-xl font-bold\">QC Testing Panel - Luxury Fashion Standards</h2>\r\n          <button\r\n            onClick={() => setIsOpen(false)}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6 space-y-6\">\r\n          {/* Breakpoint Testing */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Responsive Breakpoint Testing</h3>\r\n            <div className=\"grid grid-cols-3 gap-4 mb-4\">\r\n              {breakpoints.map((bp) => {\r\n                const IconComponent = bp.icon;\r\n                return (\r\n                  <button\r\n                    key={bp.id}\r\n                    onClick={() => setCurrentBreakpoint(bp.id)}\r\n                    className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${\r\n                      currentBreakpoint === bp.id\r\n                        ? 'border-black bg-gray-50'\r\n                        : 'border-gray-200 hover:border-gray-400'\r\n                    }`}\r\n                  >\r\n                    <IconComponent size={24} />\r\n                    <span className=\"font-medium\">{bp.name}</span>\r\n                    <span className=\"text-sm text-gray-500\">{bp.width}</span>\r\n                  </button>\r\n                );\r\n              })}\r\n            </div>\r\n            <p className=\"text-sm text-gray-600\">\r\n              Current testing viewport: <strong>{breakpoints.find(bp => bp.id === currentBreakpoint)?.width}</strong>\r\n            </p>\r\n          </div>\r\n\r\n          {/* Page Testing */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Page Testing</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n              {testPages.map((page, index) => (\r\n                <a\r\n                  key={index}\r\n                  href={page.path}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"p-3 border border-gray-200 rounded hover:border-black transition-colors text-sm\"\r\n                >\r\n                  {page.name} →\r\n                </a>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* QC Checklist */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion QC Checklist</h3>\r\n            <div className=\"space-y-2\">\r\n              {qcChecklist.map((item, index) => (\r\n                <label key={index} className=\"flex items-center space-x-3\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"w-4 h-4 text-black border-gray-300 rounded focus:ring-black\"\r\n                  />\r\n                  <span className=\"text-sm\">{item}</span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Design Standards */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Luxury Fashion Design Standards</h3>\r\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n                <div>\r\n                  <h4 className=\"font-medium mb-2\">Typography</h4>\r\n                  <ul className=\"space-y-1 text-gray-600\">\r\n                    <li>• Clean, geometric sans-serif fonts</li>\r\n                    <li>• Consistent hierarchy and spacing</li>\r\n                    <li>• Uppercase tracking for headings</li>\r\n                  </ul>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium mb-2\">Layout</h4>\r\n                  <ul className=\"space-y-1 text-gray-600\">\r\n                    <li>• No rounded corners (clean geometric design)</li>\r\n                    <li>• Sophisticated spacing hierarchy</li>\r\n                    <li>• Pixel-perfect alignment</li>\r\n                  </ul>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium mb-2\">Interactions</h4>\r\n                  <ul className=\"space-y-1 text-gray-600\">\r\n                    <li>• Smooth hover transitions</li>\r\n                    <li>• Luxury micro-interactions</li>\r\n                    <li>• Responsive touch targets</li>\r\n                  </ul>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"font-medium mb-2\">Content</h4>\r\n                  <ul className=\"space-y-1 text-gray-600\">\r\n                    <li>• High-quality product imagery</li>\r\n                    <li>• Rich mega menu layouts</li>\r\n                    <li>• Editorial content integration</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Testing Instructions */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Testing Instructions</h3>\r\n            <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n              <ol className=\"list-decimal list-inside space-y-2 text-sm\">\r\n                <li>Test each page at all three breakpoints (375px, 768px, 1920px)</li>\r\n                <li>Verify navigation functionality and mega menu interactions</li>\r\n                <li>Check product grid layouts and card hover states</li>\r\n                <li>Ensure search modal opens and functions correctly</li>\r\n                <li>Validate footer content organization and links</li>\r\n                <li>Test mobile navigation and touch interactions</li>\r\n                <li>Verify pixel-perfect alignment and spacing</li>\r\n                <li>Check for luxury fashion aesthetic consistency</li>\r\n              </ol>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QCTestingPanel;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAClB;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,8MAAA,CAAA,aAAU;QAAC;QACjE;YAAE,IAAI;YAAU,MAAM;YAAU,OAAO;YAAS,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC7D;YAAE,IAAI;YAAW,MAAM;YAAW,OAAO;YAAU,MAAM,wMAAA,CAAA,UAAO;QAAC;KAClE;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAoB,MAAM;QAAU;QAC5C;YAAE,MAAM;YAAqB,MAAM;QAAwB;QAC3D;YAAE,MAAM;YAAkB,MAAM;QAAQ;QACxC;YAAE,MAAM;YAAmB,MAAM;QAAsB;KACxD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YACC,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,OAAM;sBAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;;;;;;IAGjB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,gBAAgB,GAAG,IAAI;wCAC7B,qBACE,8OAAC;4CAEC,SAAS,IAAM,qBAAqB,GAAG,EAAE;4CACzC,WAAW,CAAC,6EAA6E,EACvF,sBAAsB,GAAG,EAAE,GACvB,4BACA,yCACJ;;8DAEF,8OAAC;oDAAc,MAAM;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAe,GAAG,IAAI;;;;;;8DACtC,8OAAC;oDAAK,WAAU;8DAAyB,GAAG,KAAK;;;;;;;2CAV5C,GAAG,EAAE;;;;;oCAahB;;;;;;8CAEF,8OAAC;oCAAE,WAAU;;wCAAwB;sDACT,8OAAC;sDAAQ,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,oBAAoB;;;;;;;;;;;;;;;;;;sCAK5F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,QAAO;4CACP,KAAI;4CACJ,WAAU;;gDAET,KAAK,IAAI;gDAAC;;2CANN;;;;;;;;;;;;;;;;sCAab,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDACC,MAAK;oDACL,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAW;;;;;;;2CALjB;;;;;;;;;;;;;;;;sCAYlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;uCAEe", "debugId": null}}, {"offset": {"line": 3633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/LoadingIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport Router from 'next/router';\r\nimport NProgress from 'nprogress';\r\nimport 'nprogress/nprogress.css';\r\n\r\nconst LoadingIndicator = () => {\r\n  useEffect(() => {\r\n    const handleStart = () => NProgress.start();\r\n    const handleStop = () => NProgress.done();\r\n\r\n    Router.events.on('routeChangeStart', handleStart);\r\n    Router.events.on('routeChangeComplete', handleStop);\r\n    Router.events.on('routeChangeError', handleStop);\r\n\r\n    return () => {\r\n      Router.events.off('routeChangeStart', handleStart);\r\n      Router.events.off('routeChangeComplete', handleStop);\r\n      Router.events.off('routeChangeError', handleStop);\r\n    };\r\n  }, []);\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingIndicator;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;;AAOA,MAAM,mBAAmB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,IAAM,sIAAA,CAAA,UAAS,CAAC,KAAK;QACzC,MAAM,aAAa,IAAM,sIAAA,CAAA,UAAS,CAAC,IAAI;QAEvC,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB;QACrC,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,EAAE,CAAC,uBAAuB;QACxC,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB;QAErC,OAAO;YACL,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB;YACtC,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB;YACzC,8HAAA,CAAA,UAAM,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB;QACxC;IACF,GAAG,EAAE;IAEL,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 3666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ReactNode } from 'react';\nimport { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';\nimport { ErrorBoundaryState } from '../../lib/types';\n\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      hasError: true,\n      error,\n      errorInfo,\n    });\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // Log to external service in production\n    if (process.env.NODE_ENV === 'production') {\n      // TODO: Integrate with error tracking service (e.g., Sentry)\n      this.logErrorToService(error, errorInfo);\n    }\n  }\n\n  private logErrorToService = (error: Error, errorInfo: React.ErrorInfo) => {\n    // Placeholder for error tracking service integration\n    console.log('Logging error to external service:', {\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n    });\n  };\n\n  private handleRetry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  private handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  private handleGoBack = () => {\n    window.history.back();\n  };\n\n  private handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Use custom fallback if provided\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default error UI\n      return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-md w-full space-y-8 text-center\">\n            <div>\n              <AlertTriangle className=\"mx-auto h-16 w-16 text-red-500\" />\n              <h1 className=\"mt-6 text-3xl font-bold text-gray-900\">\n                Something went wrong\n              </h1>\n              <p className=\"mt-2 text-sm text-gray-600\">\n                We apologize for the inconvenience. An unexpected error has occurred.\n              </p>\n              \n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <details className=\"mt-4 text-left\">\n                  <summary className=\"cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900\">\n                    Error Details (Development Only)\n                  </summary>\n                  <div className=\"mt-2 p-4 bg-red-50 border border-red-200 rounded-md\">\n                    <p className=\"text-sm font-mono text-red-800 break-all\">\n                      {this.state.error.message}\n                    </p>\n                    {this.state.error.stack && (\n                      <pre className=\"mt-2 text-xs text-red-700 overflow-auto max-h-40\">\n                        {this.state.error.stack}\n                      </pre>\n                    )}\n                  </div>\n                </details>\n              )}\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n                <button\n                  onClick={this.handleRetry}\n                  className=\"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n                >\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  Try Again\n                </button>\n                \n                <button\n                  onClick={this.handleReload}\n                  className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n                >\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  Reload Page\n                </button>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n                <button\n                  onClick={this.handleGoBack}\n                  className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n                >\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Go Back\n                </button>\n                \n                <button\n                  onClick={this.handleGoHome}\n                  className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n                >\n                  <Home className=\"w-4 h-4 mr-2\" />\n                  Go Home\n                </button>\n              </div>\n            </div>\n\n            <div className=\"text-xs text-gray-500\">\n              <p>If this problem persists, please contact our support team.</p>\n              <p className=\"mt-1\">\n                Error ID: {Date.now().toString(36).toUpperCase()}\n              </p>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Higher-order component for easier usage\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary {...errorBoundaryProps}>\n      <Component {...props} />\n    </ErrorBoundary>\n  );\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n}\n\n// Hook for error reporting\nexport function useErrorHandler() {\n  return React.useCallback((error: Error, errorInfo?: React.ErrorInfo) => {\n    console.error('Manual error report:', error, errorInfo);\n    \n    if (process.env.NODE_ENV === 'production') {\n      // TODO: Report to error tracking service\n    }\n  }, []);\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,IAAI,CAAC,QAAQ,CAAC;YACZ,UAAU;YACV;YACA;QACF;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;QAEA,wCAAwC;QACxC,uCAA2C;;QAG3C;IACF;IAEQ,oBAAoB,CAAC,OAAc;QACzC,qDAAqD;QACrD,QAAQ,GAAG,CAAC,sCAAsC;YAChD,OAAO,MAAM,OAAO;YACpB,OAAO,MAAM,KAAK;YAClB,gBAAgB,UAAU,cAAc;YACxC,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;IACF,EAAE;IAEM,cAAc;QACpB,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAE;IAEM,eAAe;QACrB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE;IAEM,eAAe;QACrB,OAAO,OAAO,CAAC,IAAI;IACrB,EAAE;IAEM,eAAe;QACrB,OAAO,QAAQ,CAAC,MAAM;IACxB,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,kCAAkC;YAClC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,mBAAmB;YACnB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;gCAIzC,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAQ,WAAU;sDAAuE;;;;;;sDAG1F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;gDAE1B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,kBACrB,8OAAC;oDAAI,WAAU;8DACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAQnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAI,CAAC,WAAW;4CACzB,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIxC,8OAAC;4CACC,SAAS,IAAI,CAAC,YAAY;4CAC1B,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAK1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAI,CAAC,YAAY;4CAC1B,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIxC,8OAAC;4CACC,SAAS,IAAI,CAAC,YAAY;4CAC1B,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;;wCAAO;wCACP,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;QAM1D;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS,kBACd,SAAiC,EACjC,kBAAyD;IAEzD,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAe,GAAG,kBAAkB;sBACnC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAGO,SAAS;IACd,OAAO,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,OAAc;QACtC,QAAQ,KAAK,CAAC,wBAAwB,OAAO;QAE7C,IAAI,oDAAyB,cAAc;QACzC,yCAAyC;QAC3C;IACF,GAAG,EAAE;AACP;uCAEe", "debugId": null}}, {"offset": {"line": 3996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/context/AccountContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\nimport { useAuth } from './AuthContext';\r\n\r\nexport interface Address {\r\n  id: string;\r\n  line1: string;\r\n  city: string;\r\n  state: string;\r\n  zip: string;\r\n  country: string;\r\n}\r\n\r\nexport interface Order {\r\n  id: string;\r\n  date: string;\r\n  total: number;\r\n  status: string;\r\n}\r\n\r\nexport interface WishlistItem {\r\n  id: string;\r\n  title: string;\r\n}\r\n\r\nexport interface Wishlist {\r\n  id: string;\r\n  name: string;\r\n  note: string;\r\n  items: WishlistItem[];\r\n}\r\n\r\ninterface AccountData {\r\n  addresses: Address[];\r\n  orders: Order[];\r\n  wishlists: Wishlist[];\r\n}\r\n\r\nconst defaultData: AccountData = {\r\n  addresses: [],\r\n  orders: [],\r\n  wishlists: [],\r\n};\r\n\r\ninterface AccountContextValue extends AccountData {\r\n  addAddress: (a: Omit<Address, 'id'>) => void;\r\n  removeAddress: (id: string) => void;\r\n  addWishlist: (name: string, note?: string) => void;\r\n  removeWishlist: (id: string) => void;\r\n  updateWishlistNote: (id: string, note: string) => void;\r\n  addWishlistItem: (listId: string, title: string) => void;\r\n  removeWishlistItem: (listId: string, itemId: string) => void;\r\n}\r\n\r\nconst AccountContext = createContext<AccountContextValue | undefined>(undefined);\r\n\r\nfunction randomId() {\r\n  return Math.random().toString(36).slice(2) + Date.now().toString(36);\r\n}\r\n\r\nexport const AccountProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const { user } = useAuth();\r\n  const [data, setData] = useState<AccountData>(defaultData);\r\n\r\n  // load data when user changes\r\n  useEffect(() => {\r\n    if (user) {\r\n      const stored = localStorage.getItem('accountData-' + user.email);\r\n      if (stored) {\r\n        setData(JSON.parse(stored));\r\n      } else {\r\n        setData(defaultData);\r\n      }\r\n    } else {\r\n      setData(defaultData);\r\n    }\r\n  }, [user]);\r\n\r\n  // persist to localStorage\r\n  useEffect(() => {\r\n    if (user) {\r\n      localStorage.setItem('accountData-' + user.email, JSON.stringify(data));\r\n    }\r\n  }, [data, user]);\r\n\r\n  const addAddress = (a: Omit<Address, 'id'>) => {\r\n    setData((d) => ({ ...d, addresses: [...d.addresses, { ...a, id: randomId() }] }));\r\n  };\r\n\r\n  const removeAddress = (id: string) => {\r\n    setData((d) => ({ ...d, addresses: d.addresses.filter((a) => a.id !== id) }));\r\n  };\r\n\r\n  const addWishlist = (name: string, note = '') => {\r\n    setData((d) => ({\r\n      ...d,\r\n      wishlists: [...d.wishlists, { id: randomId(), name, note, items: [] }],\r\n    }));\r\n  };\r\n\r\n  const removeWishlist = (id: string) => {\r\n    setData((d) => ({ ...d, wishlists: d.wishlists.filter((w) => w.id !== id) }));\r\n  };\r\n\r\n  const updateWishlistNote = (id: string, note: string) => {\r\n    setData((d) => ({\r\n      ...d,\r\n      wishlists: d.wishlists.map((w) => (w.id === id ? { ...w, note } : w)),\r\n    }));\r\n  };\r\n\r\n  const addWishlistItem = (listId: string, title: string) => {\r\n    setData((d) => ({\r\n      ...d,\r\n      wishlists: d.wishlists.map((w) =>\r\n        w.id === listId\r\n          ? { ...w, items: [...w.items, { id: randomId(), title }] }\r\n          : w\r\n      ),\r\n    }));\r\n  };\r\n\r\n  const removeWishlistItem = (listId: string, itemId: string) => {\r\n    setData((d) => ({\r\n      ...d,\r\n      wishlists: d.wishlists.map((w) =>\r\n        w.id === listId ? { ...w, items: w.items.filter((i) => i.id !== itemId) } : w\r\n      ),\r\n    }));\r\n  };\r\n\r\n  return (\r\n    <AccountContext.Provider\r\n      value={{\r\n        ...data,\r\n        addAddress,\r\n        removeAddress,\r\n        addWishlist,\r\n        removeWishlist,\r\n        updateWishlistNote,\r\n        addWishlistItem,\r\n        removeWishlistItem,\r\n      }}\r\n    >\r\n      {children}\r\n    </AccountContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAccount = () => {\r\n  const ctx = useContext(AccountContext);\r\n  if (!ctx) throw new Error('useAccount must be used within AccountProvider');\r\n  return ctx;\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAuCA,MAAM,cAA2B;IAC/B,WAAW,EAAE;IACb,QAAQ,EAAE;IACV,WAAW,EAAE;AACf;AAYA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEtE,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACnE;AAEO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAiC;IACzE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,MAAM,SAAS,aAAa,OAAO,CAAC,iBAAiB,KAAK,KAAK;YAC/D,IAAI,QAAQ;gBACV,QAAQ,KAAK,KAAK,CAAC;YACrB,OAAO;gBACL,QAAQ;YACV;QACF,OAAO;YACL,QAAQ;QACV;IACF,GAAG;QAAC;KAAK;IAET,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,aAAa,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,KAAK,SAAS,CAAC;QACnE;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,aAAa,CAAC;QAClB,QAAQ,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;gBAAE,WAAW;uBAAI,EAAE,SAAS;oBAAE;wBAAE,GAAG,CAAC;wBAAE,IAAI;oBAAW;iBAAE;YAAC,CAAC;IACjF;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;gBAAE,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAAI,CAAC;IAC7E;IAEA,MAAM,cAAc,CAAC,MAAc,OAAO,EAAE;QAC1C,QAAQ,CAAC,IAAM,CAAC;gBACd,GAAG,CAAC;gBACJ,WAAW;uBAAI,EAAE,SAAS;oBAAE;wBAAE,IAAI;wBAAY;wBAAM;wBAAM,OAAO,EAAE;oBAAC;iBAAE;YACxE,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;gBAAE,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAAI,CAAC;IAC7E;IAEA,MAAM,qBAAqB,CAAC,IAAY;QACtC,QAAQ,CAAC,IAAM,CAAC;gBACd,GAAG,CAAC;gBACJ,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;wBAAE,GAAG,CAAC;wBAAE;oBAAK,IAAI;YACpE,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,QAAgB;QACvC,QAAQ,CAAC,IAAM,CAAC;gBACd,GAAG,CAAC;gBACJ,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,IAC1B,EAAE,EAAE,KAAK,SACL;wBAAE,GAAG,CAAC;wBAAE,OAAO;+BAAI,EAAE,KAAK;4BAAE;gCAAE,IAAI;gCAAY;4BAAM;yBAAE;oBAAC,IACvD;YAER,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,QAAQ,CAAC,IAAM,CAAC;gBACd,GAAG,CAAC;gBACJ,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,IAC1B,EAAE,EAAE,KAAK,SAAS;wBAAE,GAAG,CAAC;wBAAE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAAQ,IAAI;YAEhF,CAAC;IACH;IAEA,qBACE,8OAAC,eAAe,QAAQ;QACtB,OAAO;YACL,GAAG,IAAI;YACP;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,aAAa;IACxB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACvB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,OAAO;AACT", "debugId": null}}]}