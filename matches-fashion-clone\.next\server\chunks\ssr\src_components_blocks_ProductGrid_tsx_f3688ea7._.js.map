{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/blocks/ProductGrid.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ProductGridProps {\n  data: {\n    title?: string;\n    subtitle?: string;\n    products_source?: 'medusa_category' | 'medusa_collection' | 'manual';\n    product_count?: number;\n    layout?: 'grid-2' | 'grid-3' | 'grid-4';\n    show_filters?: boolean;\n  };\n  context: 'main' | 'sidebar';\n  medusaCategoryId?: string;\n  medusaCollectionId?: string;\n}\n\nconst ProductGrid: React.FC<ProductGridProps> = ({ \n  data, \n  context, \n  medusaCategoryId, \n  medusaCollectionId \n}) => {\n  const {\n    title,\n    subtitle,\n    products_source = 'medusa_category',\n    product_count = 8,\n    layout = 'grid-4',\n    show_filters = false\n  } = data;\n\n  // This would integrate with your existing product fetching logic\n  // For now, showing placeholder\n  \n  const gridClasses = {\n    'grid-2': 'grid-cols-1 md:grid-cols-2',\n    'grid-3': 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    'grid-4': 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\n  };\n\n  return (\n    <section className={`${context === 'sidebar' ? 'p-4' : 'py-8'}`}>\n      {(title || subtitle) && (\n        <div className=\"text-center mb-8\">\n          {subtitle && (\n            <p className=\"text-sm uppercase tracking-wide text-gray-600 mb-2\">\n              {subtitle}\n            </p>\n          )}\n          {title && (\n            <h2 className=\"text-2xl md:text-3xl font-bold\">\n              {title}\n            </h2>\n          )}\n        </div>\n      )}\n\n      {show_filters && context === 'main' && (\n        <div className=\"mb-6 p-4 bg-gray-50 rounded\">\n          <p className=\"text-sm text-gray-600\">\n            Filters would be rendered here (integrate with your filter system)\n          </p>\n        </div>\n      )}\n\n      <div className={`grid ${gridClasses[layout]} gap-6`}>\n        {Array.from({ length: product_count }).map((_, index) => (\n          <div key={index} className=\"group\">\n            <div className=\"aspect-[3/4] bg-gray-200 rounded mb-3 flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Product {index + 1}</span>\n            </div>\n            <div className=\"space-y-1\">\n              <h3 className=\"font-medium text-sm\">Product Name</h3>\n              <p className=\"text-xs text-gray-600\">Designer Name</p>\n              <p className=\"font-medium\">$XXX</p>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"mt-6 text-center text-sm text-gray-600\">\n        <p>\n          Source: {products_source} \n          {medusaCategoryId && ` | Category: ${medusaCategoryId}`}\n          {medusaCollectionId && ` | Collection: ${medusaCollectionId}`}\n        </p>\n        <p className=\"mt-2\">\n          Integrate this with your existing ProductCarousel or product fetching logic\n        </p>\n      </div>\n    </section>\n  );\n};\n\nexport default ProductGrid;\n"], "names": [], "mappings": ";;;;;AAgBA,MAAM,cAA0C,CAAC,EAC/C,IAAI,EACJ,OAAO,EACP,gBAAgB,EAChB,kBAAkB,EACnB;IACC,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,kBAAkB,iBAAiB,EACnC,gBAAgB,CAAC,EACjB,SAAS,QAAQ,EACjB,eAAe,KAAK,EACrB,GAAG;IAEJ,iEAAiE;IACjE,+BAA+B;IAE/B,MAAM,cAAc;QAClB,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAQ,WAAW,GAAG,YAAY,YAAY,QAAQ,QAAQ;;YAC5D,CAAC,SAAS,QAAQ,mBACjB,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;oBAGJ,uBACC,8OAAC;wBAAG,WAAU;kCACX;;;;;;;;;;;;YAMR,gBAAgB,YAAY,wBAC3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC;0BAChD,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAc,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC7C,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAwB;wCAAS,QAAQ;;;;;;;;;;;;0CAE3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAc;;;;;;;;;;;;;uBAPrB;;;;;;;;;;0BAad,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAE;4BACQ;4BACR,oBAAoB,CAAC,aAAa,EAAE,kBAAkB;4BACtD,sBAAsB,CAAC,eAAe,EAAE,oBAAoB;;;;;;;kCAE/D,8OAAC;wBAAE,WAAU;kCAAO;;;;;;;;;;;;;;;;;;AAM5B;uCAEe", "debugId": null}}]}