module.exports = {

"[project]/src/components/blocks/HeroSection.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_blocks_HeroSection_tsx_565f207c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/blocks/HeroSection.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/blocks/ProductGrid.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_blocks_ProductGrid_tsx_f3688ea7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/blocks/ProductGrid.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/blocks/ContentBlock.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_blocks_ContentBlock_tsx_0de60a38._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/blocks/ContentBlock.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/blocks/index.ts [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_blocks_index_ts_13af1ece._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/blocks/index.ts [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/templates/HomepageTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_HomepageTemplate_tsx_96872513._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/HomepageTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/CategoryGridTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_CategoryGridTemplate_tsx_f423835b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/CategoryGridTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/ProductListTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_ProductListTemplate_tsx_1b7f7397._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/ProductListTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/FullWidthTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_FullWidthTemplate_tsx_c919d881._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/FullWidthTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/SidebarLeftTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_SidebarLeftTemplate_tsx_6fe0f352._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/SidebarLeftTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/SidebarRightTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_SidebarRightTemplate_tsx_44fda2cc._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/SidebarRightTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/components/templates/DefaultTemplate.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_templates_DefaultTemplate_tsx_9815fedf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/templates/DefaultTemplate.tsx [app-rsc] (ecmascript)");
    });
});
}}),

};