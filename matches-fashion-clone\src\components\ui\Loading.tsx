import React from 'react';
import { clsx } from 'clsx';
import { Loader2, ShoppingBag, Package, Truck } from 'lucide-react';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'bars' | 'pulse';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  className?: string;
}

export const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  text,
  fullScreen = false,
  overlay = false,
  className,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-black',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-400',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  const renderSpinner = () => (
    <Loader2
      className={clsx(
        'animate-spin',
        sizeClasses[size],
        colorClasses[color]
      )}
    />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={clsx(
            'rounded-full animate-pulse',
            size === 'sm' && 'w-1 h-1',
            size === 'md' && 'w-2 h-2',
            size === 'lg' && 'w-3 h-3',
            size === 'xl' && 'w-4 h-4',
            color === 'primary' && 'bg-black',
            color === 'secondary' && 'bg-gray-600',
            color === 'white' && 'bg-white',
            color === 'gray' && 'bg-gray-400'
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1.4s',
          }}
        />
      ))}
    </div>
  );

  const renderBars = () => (
    <div className="flex items-end space-x-1">
      {[0, 1, 2, 3].map((i) => (
        <div
          key={i}
          className={clsx(
            'animate-pulse',
            size === 'sm' && 'w-1',
            size === 'md' && 'w-1.5',
            size === 'lg' && 'w-2',
            size === 'xl' && 'w-3',
            color === 'primary' && 'bg-black',
            color === 'secondary' && 'bg-gray-600',
            color === 'white' && 'bg-white',
            color === 'gray' && 'bg-gray-400'
          )}
          style={{
            height: `${12 + (i * 4)}px`,
            animationDelay: `${i * 0.1}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <div
      className={clsx(
        'rounded-full animate-pulse',
        sizeClasses[size],
        color === 'primary' && 'bg-black',
        color === 'secondary' && 'bg-gray-600',
        color === 'white' && 'bg-white',
        color === 'gray' && 'bg-gray-400'
      )}
    />
  );

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'bars':
        return renderBars();
      case 'pulse':
        return renderPulse();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div
      className={clsx(
        'flex flex-col items-center justify-center space-y-3',
        fullScreen && 'min-h-screen',
        className
      )}
    >
      {renderLoader()}
      {text && (
        <p
          className={clsx(
            'font-medium',
            textSizeClasses[size],
            colorClasses[color]
          )}
        >
          {text}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

// Specialized loading components
export const PageLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <Loading fullScreen size="lg" text={text} />
);

export const ButtonLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'sm' }) => (
  <Loading size={size} variant="spinner" />
);

export const InlineLoading: React.FC<{ text?: string; size?: 'sm' | 'md' }> = ({
  text,
  size = 'sm',
}) => (
  <div className="flex items-center space-x-2">
    <Loading size={size} variant="spinner" />
    {text && <span className="text-sm text-gray-600">{text}</span>}
  </div>
);

export const OverlayLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <Loading overlay size="lg" text={text} />
);

// Context-specific loading components
export const ShoppingLoading: React.FC<{ text?: string }> = ({
  text = 'Loading products...',
}) => (
  <div className="flex flex-col items-center justify-center space-y-4 py-12">
    <ShoppingBag className="w-12 h-12 text-gray-400 animate-pulse" />
    <p className="text-gray-600 font-medium">{text}</p>
  </div>
);

export const CheckoutLoading: React.FC<{ text?: string }> = ({
  text = 'Processing your order...',
}) => (
  <div className="flex flex-col items-center justify-center space-y-4 py-12">
    <Package className="w-12 h-12 text-gray-400 animate-pulse" />
    <p className="text-gray-600 font-medium">{text}</p>
  </div>
);

export const ShippingLoading: React.FC<{ text?: string }> = ({
  text = 'Calculating shipping...',
}) => (
  <div className="flex flex-col items-center justify-center space-y-4 py-8">
    <Truck className="w-8 h-8 text-gray-400 animate-pulse" />
    <p className="text-gray-600 text-sm">{text}</p>
  </div>
);

// Loading states for different sections
export const ProductGridLoading: React.FC = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {Array.from({ length: 8 }).map((_, index) => (
      <div key={index} className="space-y-3">
        <div className="aspect-[3/4] bg-gray-200 animate-pulse rounded" />
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 animate-pulse rounded" />
          <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
          <div className="h-5 bg-gray-200 animate-pulse rounded w-1/2" />
        </div>
      </div>
    ))}
  </div>
);

export const ProductDetailLoading: React.FC = () => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <div className="space-y-4">
      <div className="aspect-square bg-gray-200 animate-pulse rounded" />
      <div className="grid grid-cols-4 gap-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="aspect-square bg-gray-200 animate-pulse rounded" />
        ))}
      </div>
    </div>
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 animate-pulse rounded w-1/3" />
        <div className="h-8 bg-gray-200 animate-pulse rounded w-2/3" />
        <div className="h-6 bg-gray-200 animate-pulse rounded w-1/4" />
      </div>
      <div className="space-y-4">
        <div className="h-5 bg-gray-200 animate-pulse rounded w-1/5" />
        <div className="grid grid-cols-4 gap-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="h-10 bg-gray-200 animate-pulse rounded" />
          ))}
        </div>
      </div>
      <div className="space-y-2">
        <div className="h-12 bg-gray-200 animate-pulse rounded" />
        <div className="h-10 bg-gray-200 animate-pulse rounded" />
      </div>
    </div>
  </div>
);

export const CarouselLoading: React.FC<{ itemCount?: number }> = ({ itemCount = 4 }) => (
  <div className="flex space-x-4 overflow-hidden">
    {Array.from({ length: itemCount }).map((_, index) => (
      <div key={index} className="flex-shrink-0 w-64 space-y-3">
        <div className="aspect-[3/4] bg-gray-200 animate-pulse rounded" />
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 animate-pulse rounded" />
          <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
          <div className="h-5 bg-gray-200 animate-pulse rounded w-1/2" />
        </div>
      </div>
    ))}
  </div>
);

export default Loading;
