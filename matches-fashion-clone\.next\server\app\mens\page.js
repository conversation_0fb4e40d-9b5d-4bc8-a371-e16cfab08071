"use strict";(()=>{var e={};e.id=534,e.ids=[534],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5504:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=r(65239),t=r(48088),i=r(88170),n=r.n(i),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d={children:["",{children:["mens",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24258)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/mens/page",pathname:"/mens",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24258:(e,s,r)=>{r.r(s),r.d(s,{default:()=>n});var a=r(37413),t=r(4536),i=r.n(t);function n(){return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsxs)("section",{className:"relative h-screen flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-400"}),(0,a.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,a.jsx)("p",{className:"text-sm uppercase tracking-wide mb-4",children:"introducing the new collections"}),(0,a.jsx)("h1",{className:"text-6xl md:text-8xl font-bold mb-6",children:"Modern Luxury"}),(0,a.jsx)("button",{className:"luxury-button",children:"SHOP"})]})]}),(0,a.jsx)("nav",{className:"bg-white border-b border-gray-200 sticky top-0 z-40",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex justify-start md:justify-center space-x-4 md:space-x-8 py-4 overflow-x-auto whitespace-nowrap",children:[(0,a.jsx)(i(),{href:"/mens/designers",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Designers"}),(0,a.jsx)(i(),{href:"/mens/shop/clothing",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Clothing"}),(0,a.jsx)(i(),{href:"/mens/shop/shoes",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Shoes"}),(0,a.jsx)(i(),{href:"/mens/shop/bags",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Bags"}),(0,a.jsx)(i(),{href:"/mens/shop/accessories",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Accessories"}),(0,a.jsx)(i(),{href:"/mens/stories",className:"text-sm font-medium uppercase tracking-wide hover:text-gray-600",children:"Stories"})]})})}),(0,a.jsxs)("section",{className:"max-w-7xl mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)(i(),{href:"/mens/shop/clothing/suits",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Suits Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"tailored perfection"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Modern Suiting"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/shop/shoes/sneakers",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Sneakers Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"street luxury"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Premium Sneakers"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/designers/tom-ford",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Tom Ford Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"sophisticated elegance"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Tom Ford"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/shop/bags",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Bags Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"functional luxury"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Essential Bags"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/shop/clothing/knitwear",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Knitwear Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"comfort meets style"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Luxury Knitwear"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/shop/accessories/watches",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Watches Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"timeless precision"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Luxury Timepieces"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mt-12",children:[(0,a.jsxs)(i(),{href:"/mens/lists/workwear",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Workwear Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"professional style"}),(0,a.jsx)("h3",{className:"text-xl font-medium",children:"Executive Essentials"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]}),(0,a.jsxs)(i(),{href:"/mens/lists/weekend",className:"group",children:[(0,a.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden",children:(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"Weekend Image"})})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs uppercase tracking-wide text-gray-500",children:"relaxed luxury"}),(0,a.jsx)("h3",{className:"text-xl font-medium",children:"Weekend Edit"}),(0,a.jsx)("button",{className:"text-sm font-medium uppercase tracking-wide",children:"SHOP"})]})]})]}),(0,a.jsxs)("div",{className:"mt-16",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"Featured Designers"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8",children:["Tom Ford","Brunello Cucinelli","Stone Island","Thom Browne","Bottega Veneta","Gucci"].map((e,s)=>(0,a.jsxs)(i(),{href:`/mens/designers/${e.toLowerCase().replace(/\s+/g,"-")}`,className:"text-center group",children:[(0,a.jsx)("div",{className:"aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300",children:(0,a.jsx)("span",{className:"text-xs text-gray-600",children:"Logo"})}),(0,a.jsx)("h3",{className:"text-sm font-medium",children:e})]},s))})]})]})]})}},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,20,137],()=>r(5504));module.exports=a})();