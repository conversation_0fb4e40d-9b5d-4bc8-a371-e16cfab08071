"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[855],{1855:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(5155),r=a(2115),l=a(6932),c=a(6474),i=a(4416),n=a(8088),o=a(7677),d=a(7469);function m(e){let{product:s,name:a,price:l,colorHex:c,href:i,variant:m}=e,[x,h]=(0,r.useState)(!1),{addItem:u}=(0,n._)();return(0,t.jsxs)("div",{className:"relative group",onMouseEnter:()=>{window.innerWidth>=1024&&h(!0)},onMouseLeave:()=>{window.innerWidth>=1024&&h(!1)},children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.RC,{modules:[d.Vx,d.dK],navigation:{enabled:!0},pagination:{clickable:!0},loop:!0,className:"swiper-product",children:m.images.map((e,s)=>(0,t.jsx)(o.qr,{children:(0,t.jsx)("a",{href:i,children:(0,t.jsx)("img",{src:e,alt:"Image ".concat(s+1),className:"w-full object-cover",loading:"lazy"})})},s))}),(0,t.jsx)("button",{onClick:()=>h(!x),className:"absolute bottom-2 right-2 bg-white p-1 rounded-full shadow-md block lg:hidden z-10",children:"\uD83D\uDED2"}),x&&(0,t.jsx)("div",{className:"absolute inset-x-2 bottom-2 bg-white shadow-md flex flex-wrap justify-center gap-1 p-2 z-20",children:m.sizes.map(e=>(0,t.jsx)("button",{className:"border px-2 py-1 text-xs md:text-sm hover:bg-black hover:text-white",onClick:()=>{u(s,m,e),h(!1)},children:e},e))})]}),(0,t.jsxs)("div",{className:"pt-3 text-center",children:[(0,t.jsx)("h3",{className:"text-sm uppercase",children:a}),(0,t.jsxs)("p",{className:"text-sm font-semibold",children:["₫ ",l.toLocaleString()]}),(0,t.jsx)("div",{className:"flex justify-center mt-2",children:(0,t.jsx)("span",{className:"w-4 h-4 border border-gray-300 rounded-full",style:{backgroundColor:c}})})]})]})}a(2252),a(9408),a(6970);var x=a(3165),h=a(7358);let u=e=>{let{title:s,categories:a,designers:n,colors:o=["Black","White","Blue","Red","Green"],initialProducts:d=[],query:u}=e,[p,b]=(0,r.useState)(d),[g,j]=(0,r.useState)("newest"),[v,N]=(0,r.useState)(!1),[f,w]=(0,r.useState)([]),[y,k]=(0,r.useState)([]),[C,S]=(0,r.useState)([]),[P,E]=(0,r.useState)([]),[L,_]=(0,r.useState)([0,5e3]);(0,r.useEffect)(()=>{(async()=>{if(u&&h.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT)try{let e=await fetch(h.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:u})}),s=await e.json();s.data&&Array.isArray(s.data.products)&&b(s.data.products)}catch(e){console.error("Failed to fetch products",e)}})()},[u]);let z=[...p.filter(e=>{let s=0===f.length||f.includes(e.category),a=0===y.length||y.includes(e.brand),t=0===C.length||e.variants.some(e=>C.some(s=>e.sizes.includes(s))),r=0===P.length||e.variants.some(e=>P.includes(e.color)),l=e.price>=L[0]&&e.price<=L[1];return s&&a&&t&&r&&l})].sort((e,s)=>"price-low"===g?e.price-s.price:"price-high"===g?s.price-e.price:0);return(0,t.jsxs)("div",{className:"bg-white min-h-screen",children:[(0,t.jsx)("div",{className:"h-40 md:h-60 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center",children:(0,t.jsx)("h1",{className:"text-3xl font-bold uppercase",children:s})}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between py-4 border-b",children:[(0,t.jsxs)("button",{className:"flex items-center space-x-2 text-sm uppercase",onClick:()=>N(!0),children:[(0,t.jsx)(l.A,{size:16}),(0,t.jsx)("span",{children:"Filter"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black",children:[(0,t.jsx)("option",{value:"newest",children:"Newest"}),(0,t.jsx)("option",{value:"price-low",children:"Price: Low to High"}),(0,t.jsx)("option",{value:"price-high",children:"Price: High to Low"}),(0,t.jsx)("option",{value:"popular",children:"Most Popular"})]}),(0,t.jsx)(c.A,{size:16,className:"absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none"})]})]}),(0,t.jsx)("div",{className:"grid gap-6 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 py-6",children:z.map(e=>{var s;return(0,t.jsx)(m,{product:e,name:e.name,price:e.price,href:e.href,colorHex:(null==(s=e.variants[0])?void 0:s.color)||"transparent",variant:e.variants[0]},e.id)})}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsx)("button",{className:"luxury-button-outline",children:"Load More"})})]}),v&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/50",onClick:()=>N(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-white p-6 overflow-y-auto",children:[(0,t.jsxs)("button",{className:"flex items-center space-x-1 mb-4 text-sm uppercase",onClick:()=>N(!1),children:[(0,t.jsx)(i.A,{size:16}),(0,t.jsx)("span",{children:"Close"})]}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Categories"}),(0,t.jsx)("ul",{className:"space-y-2",children:a.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"drawer-cat-".concat(s),type:"checkbox",checked:f.includes(e),onChange:()=>w(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,t.jsx)("label",{htmlFor:"drawer-cat-".concat(s),className:"text-sm text-gray-600",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Designers"}),(0,t.jsx)("ul",{className:"space-y-2 max-h-48 overflow-y-auto pr-2",children:n.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"drawer-designer-".concat(s),type:"checkbox",checked:y.includes(e),onChange:()=>k(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,t.jsx)("label",{htmlFor:"drawer-designer-".concat(s),className:"text-sm text-gray-600",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Size"}),(0,t.jsx)("div",{className:"grid grid-cols-3 gap-2",children:["XS","S","M","L","XL"].map(e=>(0,t.jsx)("button",{onClick:()=>S(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"border py-2 text-sm transition-colors ".concat(C.includes(e)?"border-black bg-black text-white":"border-gray-300 hover:border-black"),children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Color"}),(0,t.jsx)("ul",{className:"space-y-2",children:o.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"drawer-color-".concat(s),type:"checkbox",checked:P.includes(e),onChange:()=>E(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e]),className:"accent-black"}),(0,t.jsxs)("label",{htmlFor:"drawer-color-".concat(s),className:"text-sm text-gray-600 flex items-center space-x-1",children:[(0,t.jsx)("span",{className:"w-3 h-3 inline-block border",style:{backgroundColor:e.toLowerCase()}}),(0,t.jsx)("span",{children:e})]})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Price"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("input",{type:"number",min:0,max:5e3,value:L[0],onChange:e=>{let s=Number(e.target.value);_(e=>[Math.min(s,e[1]),e[1]])},className:"w-full border px-2 py-1 text-sm"}),(0,t.jsx)("input",{type:"number",min:0,max:5e3,value:L[1],onChange:e=>{let s=Number(e.target.value);_(e=>[e[0],Math.max(s,e[0])])},className:"w-full border px-2 py-1 text-sm"})]}),(0,t.jsx)(x.Range,{values:L,step:10,min:0,max:5e3,onChange:e=>_([e[0],e[1]]),renderTrack:e=>{let{props:s,children:a}=e;return(0,t.jsx)("div",{...s,className:"w-full h-2 rounded bg-gray-200",style:{...s.style,background:(0,x.getTrackBackground)({values:L,colors:["#d1d5db","#000","#d1d5db"],min:0,max:5e3})},children:a})},renderThumb:e=>{let{props:s}=e;return(0,t.jsx)("div",{...s,className:"w-4 h-4 bg-white border border-gray-400 rounded-full focus:outline-none"})}})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["\xa3",L[0]," - \xa3",L[1]]})]})]})]})]})]})}},8088:(e,s,a)=>{a.d(s,{CartProvider:()=>i,_:()=>n});var t=a(5155),r=a(2115);let l=(0,r.createContext)(void 0),c="cartItems",i=e=>{let{children:s}=e,[a,i]=(0,r.useState)([]);return(0,r.useEffect)(()=>{let e=localStorage.getItem(c);e&&i(JSON.parse(e))},[]),(0,r.useEffect)(()=>{localStorage.setItem(c,JSON.stringify(a))},[a]),(0,t.jsx)(l.Provider,{value:{items:a,addItem:function(e,s,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;i(r=>[...r,{id:Math.random().toString(36).slice(2)+Date.now().toString(36),product:e,variant:s,size:a,quantity:t}])},removeItem:e=>{i(s=>s.filter(s=>s.id!==e))},clearCart:()=>i([])},children:s})},n=()=>{let e=(0,r.useContext)(l);if(!e)throw Error("useCart must be used within CartProvider");return e}}}]);