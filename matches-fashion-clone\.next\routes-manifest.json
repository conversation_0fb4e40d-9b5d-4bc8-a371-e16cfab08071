{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/product/[handle]", "regex": "^/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhandle": "nxtPhandle"}, "namedRegex": "^/product/(?<nxtPhandle>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/account", "regex": "^/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/account(?:/)?$"}, {"page": "/account/addresses", "regex": "^/account/addresses(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/addresses(?:/)?$"}, {"page": "/account/login", "regex": "^/account/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/login(?:/)?$"}, {"page": "/account/orders", "regex": "^/account/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/orders(?:/)?$"}, {"page": "/account/profile", "regex": "^/account/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/profile(?:/)?$"}, {"page": "/account/register", "regex": "^/account/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/register(?:/)?$"}, {"page": "/account/wishlists", "regex": "^/account/wishlists(?:/)?$", "routeKeys": {}, "namedRegex": "^/account/wishlists(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/delivery", "regex": "^/delivery(?:/)?$", "routeKeys": {}, "namedRegex": "^/delivery(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/help/chat", "regex": "^/help/chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/help/chat(?:/)?$"}, {"page": "/mens", "regex": "^/mens(?:/)?$", "routeKeys": {}, "namedRegex": "^/mens(?:/)?$"}, {"page": "/mens/shop/clothing", "regex": "^/mens/shop/clothing(?:/)?$", "routeKeys": {}, "namedRegex": "^/mens/shop/clothing(?:/)?$"}, {"page": "/returns", "regex": "^/returns(?:/)?$", "routeKeys": {}, "namedRegex": "^/returns(?:/)?$"}, {"page": "/returns/request", "regex": "^/returns/request(?:/)?$", "routeKeys": {}, "namedRegex": "^/returns/request(?:/)?$"}, {"page": "/wishlist-share", "regex": "^/wishlist\\-share(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist\\-share(?:/)?$"}, {"page": "/womens", "regex": "^/womens(?:/)?$", "routeKeys": {}, "namedRegex": "^/womens(?:/)?$"}, {"page": "/womens/shop/clothing", "regex": "^/womens/shop/clothing(?:/)?$", "routeKeys": {}, "namedRegex": "^/womens/shop/clothing(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}