// ===== STRAPI TYPES =====

export interface StrapiMediaAttributes {
  url: string;
  width?: number;
  height?: number;
  alternativeText?: string | null;
  caption?: string | null;
  formats?: {
    thumbnail?: { url: string; width: number; height: number };
    small?: { url: string; width: number; height: number };
    medium?: { url: string; width: number; height: number };
    large?: { url: string; width: number; height: number };
  };
}

export interface StrapiMedia {
  data: {
    id: number;
    attributes: StrapiMediaAttributes;
  } | null;
}

export interface StrapiMultipleMedia {
  data: Array<{
    id: number;
    attributes: StrapiMediaAttributes;
  }> | null;
}

export interface StrapiHomepageAttributes {
  hero_title?: string;
  hero_subtitle?: string;
  hero_background_image?: StrapiMedia | null;
  editorial_block_title?: string;
  editorial_block_subtitle?: string;
  editorial_block_image?: StrapiMedia | null;
  editorial_block_link?: string;
  featured_product_block_title?: string;
  featured_product_block_subtitle?: string;
  featured_product_block_image?: StrapiMedia | null;
  featured_product_block_link?: string;
  small_blocks_row?: Array<{
    id?: string;
    title?: string;
    image?: StrapiMedia | string | null;
    href?: string;
  }>;
  designer_spotlight_title?: string;
  designer_spotlight_subtitle?: string;
  designer_spotlight_image?: StrapiMedia | null;
  designer_spotlight_link?: string;
  new_arrivals_block_title?: string;
  new_arrivals_block_subtitle?: string;
  new_arrivals_block_image?: StrapiMedia | null;
  new_arrivals_block_link?: string;
}

export interface StrapiResponse<T> {
  data: {
    id: number;
    attributes: T;
  };
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiCollectionResponse<T> {
  data: Array<{
    id: number;
    attributes: T;
  }>;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// ===== MEDUSA TYPES =====

export interface MedusaPrice {
  id: string;
  amount: number;
  currency_code: string;
  price_list_id?: string | null;
  min_quantity?: number;
  max_quantity?: number;
}

export interface MedusaProductOption {
  id: string;
  title: string;
  values: Array<{
    id: string;
    value: string;
    metadata?: Record<string, any>;
  }>;
}

export interface MedusaProductVariant {
  id: string;
  title: string;
  sku?: string | null;
  barcode?: string | null;
  ean?: string | null;
  upc?: string | null;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  weight?: number | null;
  length?: number | null;
  height?: number | null;
  width?: number | null;
  origin_country?: string | null;
  mid_code?: string | null;
  material?: string | null;
  prices: MedusaPrice[];
  options?: Array<{
    option_id: string;
    value: string;
  }>;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductImage {
  id: string;
  url: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductCollection {
  id: string;
  title: string;
  handle: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProductCategory {
  id: string;
  name: string;
  handle: string;
  parent_category_id?: string | null;
  parent_category?: MedusaProductCategory | null;
  category_children?: MedusaProductCategory[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MedusaProduct {
  id: string;
  title: string;
  subtitle?: string | null;
  description?: string | null;
  handle?: string | null;
  is_giftcard: boolean;
  status: 'draft' | 'proposed' | 'published' | 'rejected';
  thumbnail?: string | null;
  weight?: number | null;
  length?: number | null;
  height?: number | null;
  width?: number | null;
  origin_country?: string | null;
  mid_code?: string | null;
  material?: string | null;
  collection_id?: string | null;
  collection?: MedusaProductCollection | null;
  type_id?: string | null;
  tags?: Array<{
    id: string;
    value: string;
    metadata?: Record<string, any>;
  }>;
  images?: MedusaProductImage[];
  options?: MedusaProductOption[];
  variants?: MedusaProductVariant[];
  categories?: MedusaProductCategory[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

export interface MedusaProductDetail extends MedusaProduct {
  // Extended product details for product pages
  variants: MedusaProductVariant[];
  images: MedusaProductImage[];
  options: MedusaProductOption[];
}

// ===== API RESPONSE TYPES =====

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
  success: boolean;
}

// ===== UTILITY TYPES =====

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface FilterParams {
  category_id?: string[];
  collection_id?: string[];
  tags?: string[];
  price_min?: number;
  price_max?: number;
  is_giftcard?: boolean;
}

export interface SearchParams extends PaginationParams, FilterParams {
  q?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// ===== COMPONENT PROP TYPES =====

export interface ProductCardProps {
  product: MedusaProduct;
  className?: string;
  showQuickView?: boolean;
  showWishlist?: boolean;
}

export interface ProductGridProps {
  products: MedusaProduct[];
  loading?: boolean;
  error?: string | null;
  className?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

// ===== FORM TYPES =====

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface NewsletterFormData {
  email: string;
  preferences?: {
    womens?: boolean;
    mens?: boolean;
    sales?: boolean;
    newArrivals?: boolean;
  };
}

// ===== CART TYPES =====

export interface CartItem {
  id: string;
  variant_id: string;
  product: MedusaProduct;
  variant: MedusaProductVariant;
  quantity: number;
  unit_price: number;
  total: number;
}

export interface Cart {
  id: string;
  items: CartItem[];
  total: number;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  discount_total: number;
  currency_code: string;
}
