import React from 'react';
import { ContentBlock } from '../../lib/page-manager';
import dynamic from 'next/dynamic';

// Dynamically import content block components
const HeroSection = dynamic(() => import('./blocks/HeroSection'));
const ProductGrid = dynamic(() => import('./blocks/ProductGrid'));
const ContentBlock = dynamic(() => import('./blocks/ContentBlock'));

// Import placeholder components
const ImageGallery = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.ImageGallery })));
const CallToAction = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CallToAction })));
const FeaturedProducts = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.FeaturedProducts })));
const CategoryShowcase = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CategoryShowcase })));
const Testimonials = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.Testimonials })));
const NewsletterSignup = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.NewsletterSignup })));
const CategoryMenu = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.CategoryMenu })));
const FilterWidget = dynamic(() => import('./blocks/index').then(mod => ({ default: mod.FilterWidget })));

interface ContentBlockRendererProps {
  block: ContentBlock;
  context: 'main' | 'sidebar';
  pageType?: string;
  medusaCategoryId?: string;
  medusaCollectionId?: string;
}

const ContentBlockRenderer: React.FC<ContentBlockRendererProps> = ({
  block,
  context,
  pageType,
  medusaCategoryId,
  medusaCollectionId,
}) => {
  const { __component } = block;

  // Common props to pass to all components
  const commonProps = {
    data: block,
    context,
    pageType,
    medusaCategoryId,
    medusaCollectionId,
  };

  // Render the appropriate component based on the component type
  const renderComponent = () => {
    switch (__component) {
      case 'blocks.hero-section':
        return <HeroSection {...commonProps} />;
      
      case 'blocks.product-grid':
        return <ProductGrid {...commonProps} />;
      
      case 'blocks.content-block':
        return <ContentBlock {...commonProps} />;
      
      case 'blocks.image-gallery':
        return <ImageGallery {...commonProps} />;
      
      case 'blocks.call-to-action':
        return <CallToAction {...commonProps} />;
      
      case 'blocks.featured-products':
        return <FeaturedProducts {...commonProps} />;
      
      case 'blocks.category-showcase':
        return <CategoryShowcase {...commonProps} />;
      
      case 'blocks.testimonials':
        return <Testimonials {...commonProps} />;
      
      case 'blocks.newsletter-signup':
        return <NewsletterSignup {...commonProps} />;
      
      case 'blocks.category-menu':
        return <CategoryMenu {...commonProps} />;
      
      case 'blocks.filter-widget':
        return <FilterWidget {...commonProps} />;
      
      default:
        // Fallback for unknown components
        if (process.env.NODE_ENV === 'development') {
          return (
            <div className="p-4 border-2 border-dashed border-yellow-400 bg-yellow-50 rounded">
              <p className="text-yellow-800 font-medium">
                Unknown component: {__component}
              </p>
              <pre className="mt-2 text-xs text-yellow-700 overflow-auto">
                {JSON.stringify(block, null, 2)}
              </pre>
            </div>
          );
        }
        return null;
    }
  };

  return (
    <div 
      className={`content-block content-block--${__component.replace('blocks.', '').replace('-', '_')}`}
      data-component={__component}
      data-context={context}
    >
      {renderComponent()}
    </div>
  );
};

export default ContentBlockRenderer;
