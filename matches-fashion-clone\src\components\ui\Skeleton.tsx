import React from 'react';
import { clsx } from 'clsx';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  variant?: 'rectangular' | 'circular' | 'text';
  animation?: 'pulse' | 'wave' | 'none';
  children?: React.ReactNode;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  width,
  height,
  variant = 'rectangular',
  animation = 'pulse',
  children,
}) => {
  const baseClasses = 'bg-gray-200';
  
  const variantClasses = {
    rectangular: 'rounded',
    circular: 'rounded-full',
    text: 'rounded-sm',
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-pulse', // Could be enhanced with custom wave animation
    none: '',
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={clsx(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={style}
      aria-hidden="true"
    >
      {children}
    </div>
  );
};

// Specialized skeleton components
export const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({
  lines = 1,
  className,
}) => (
  <div className={clsx('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        variant="text"
        height={16}
        width={index === lines - 1 ? '75%' : '100%'}
      />
    ))}
  </div>
);

export const ImageSkeleton: React.FC<{
  width?: string | number;
  height?: string | number;
  className?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
}> = ({ width, height, className, aspectRatio }) => {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]',
  };

  return (
    <Skeleton
      className={clsx(
        aspectRatio && aspectRatioClasses[aspectRatio],
        className
      )}
      width={width}
      height={height}
    />
  );
};

export const ProductCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={clsx('space-y-3', className)}>
    <ImageSkeleton aspectRatio="portrait" className="w-full" />
    <div className="space-y-2">
      <TextSkeleton lines={1} />
      <TextSkeleton lines={1} />
      <Skeleton height={20} width="60%" />
    </div>
  </div>
);

export const ProductGridSkeleton: React.FC<{
  count?: number;
  columns?: number;
  className?: string;
}> = ({ count = 8, columns = 4, className }) => (
  <div
    className={clsx(
      'grid gap-6',
      {
        'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4': columns === 4,
        'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': columns === 3,
        'grid-cols-1 sm:grid-cols-2': columns === 2,
        'grid-cols-1': columns === 1,
      },
      className
    )}
  >
    {Array.from({ length: count }).map((_, index) => (
      <ProductCardSkeleton key={index} />
    ))}
  </div>
);

export const HeaderSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={clsx('flex items-center justify-between p-4', className)}>
    <Skeleton width={120} height={32} />
    <div className="flex items-center space-x-4">
      <Skeleton width={200} height={32} />
      <Skeleton variant="circular" width={32} height={32} />
      <Skeleton variant="circular" width={32} height={32} />
    </div>
  </div>
);

export const FooterSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={clsx('grid grid-cols-1 md:grid-cols-4 gap-8 p-8', className)}>
    {Array.from({ length: 4 }).map((_, index) => (
      <div key={index} className="space-y-4">
        <Skeleton height={20} width="80%" />
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, linkIndex) => (
            <Skeleton key={linkIndex} height={16} width="60%" />
          ))}
        </div>
      </div>
    ))}
  </div>
);

export const HeroSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={clsx('relative', className)}>
    <ImageSkeleton aspectRatio="landscape" className="w-full h-96" />
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center space-y-4">
        <Skeleton height={48} width={300} />
        <Skeleton height={24} width={200} />
        <Skeleton height={40} width={120} />
      </div>
    </div>
  </div>
);

export const ProductDetailSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={clsx('grid grid-cols-1 lg:grid-cols-2 gap-8', className)}>
    {/* Image gallery skeleton */}
    <div className="space-y-4">
      <ImageSkeleton aspectRatio="square" className="w-full" />
      <div className="grid grid-cols-4 gap-2">
        {Array.from({ length: 4 }).map((_, index) => (
          <ImageSkeleton key={index} aspectRatio="square" />
        ))}
      </div>
    </div>

    {/* Product info skeleton */}
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton height={16} width="40%" />
        <Skeleton height={32} width="80%" />
        <Skeleton height={24} width="30%" />
      </div>

      <div className="space-y-4">
        <Skeleton height={20} width="20%" />
        <div className="grid grid-cols-4 gap-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} height={40} />
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <Skeleton height={48} width="100%" />
        <Skeleton height={40} width="100%" />
      </div>

      <div className="space-y-2">
        <Skeleton height={20} width="30%" />
        <TextSkeleton lines={3} />
      </div>
    </div>
  </div>
);

export const CarouselSkeleton: React.FC<{
  itemCount?: number;
  className?: string;
}> = ({ itemCount = 4, className }) => (
  <div className={clsx('flex space-x-4 overflow-hidden', className)}>
    {Array.from({ length: itemCount }).map((_, index) => (
      <div key={index} className="flex-shrink-0 w-64">
        <ProductCardSkeleton />
      </div>
    ))}
  </div>
);

export default Skeleton;
