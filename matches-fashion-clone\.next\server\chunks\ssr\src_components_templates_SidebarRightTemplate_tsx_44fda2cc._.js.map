{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/SidebarRightTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\nconst SidebarRightTemplate: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <div className=\"max-w-7xl mx-auto px-4 py-8\">{children}</div>\n);\n\nexport default SidebarRightTemplate;\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,uBAAgE,CAAC,EAAE,QAAQ,EAAE,iBACjF,8OAAC;QAAI,WAAU;kBAA+B;;;;;;uCAGjC", "debugId": null}}]}