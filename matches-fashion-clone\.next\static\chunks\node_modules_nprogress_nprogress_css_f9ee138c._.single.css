/* [project]/node_modules/nprogress/nprogress.css [app-client] (css) */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  z-index: 1031;
  background: #29d;
  width: 100%;
  height: 2px;
  position: fixed;
  top: 0;
  left: 0;
}

#nprogress .peg {
  opacity: 1;
  width: 100px;
  height: 100%;
  display: block;
  position: absolute;
  right: 0;
  -webkit-transform: rotate(3deg)translate(0, -4px);
  -ms-transform: rotate(3deg)translate(0, -4px);
  transform: rotate(3deg)translate(0, -4px);
  box-shadow: 0 0 10px #29d, 0 0 5px #29d;
}

#nprogress .spinner {
  z-index: 1031;
  display: block;
  position: fixed;
  top: 15px;
  right: 15px;
}

#nprogress .spinner-icon {
  box-sizing: border-box;
  border: 2px solid #0000;
  border-color: #29d #0000 #0000 #29d;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  -webkit-animation: .4s linear infinite nprogress-spinner;
  animation: .4s linear infinite nprogress-spinner;
}

.nprogress-custom-parent {
  position: relative;
  overflow: hidden;
}

.nprogress-custom-parent #nprogress .spinner, .nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*# sourceMappingURL=node_modules_nprogress_nprogress_css_f9ee138c._.single.css.map*/