(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1226:()=>{},3503:(e,s,r)=>{"use strict";r.d(s,{default:()=>l});var t=r(2115),a=r(3784),n=r(6770),i=r.n(n);r(6702);let l=()=>((0,t.useEffect)(()=>{let e=()=>i().start(),s=()=>i().done();return a.default.events.on("routeChangeStart",e),a.default.events.on("routeChangeComplete",s),a.default.events.on("routeChangeError",s),()=>{a.default.events.off("routeChangeStart",e),a.default.events.off("routeChangeComplete",s),a.default.events.off("routeChangeError",s)}},[]),null)},4303:(e,s,r)=>{"use strict";r.d(s,{AccountProvider:()=>c,F:()=>d});var t=r(5155),a=r(2115),n=r(9576);let i={addresses:[],orders:[],wishlists:[]},l=(0,a.createContext)(void 0);function o(){return Math.random().toString(36).slice(2)+Date.now().toString(36)}let c=e=>{let{children:s}=e,{user:r}=(0,n.A)(),[c,d]=(0,a.useState)(i);return(0,a.useEffect)(()=>{if(r){let e=localStorage.getItem("accountData-"+r.email);e?d(JSON.parse(e)):d(i)}else d(i)},[r]),(0,a.useEffect)(()=>{r&&localStorage.setItem("accountData-"+r.email,JSON.stringify(c))},[c,r]),(0,t.jsx)(l.Provider,{value:{...c,addAddress:e=>{d(s=>({...s,addresses:[...s.addresses,{...e,id:o()}]}))},removeAddress:e=>{d(s=>({...s,addresses:s.addresses.filter(s=>s.id!==e)}))},addWishlist:function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";d(r=>({...r,wishlists:[...r.wishlists,{id:o(),name:e,note:s,items:[]}]}))},removeWishlist:e=>{d(s=>({...s,wishlists:s.wishlists.filter(s=>s.id!==e)}))},updateWishlistNote:(e,s)=>{d(r=>({...r,wishlists:r.wishlists.map(r=>r.id===e?{...r,note:s}:r)}))},addWishlistItem:(e,s)=>{d(r=>({...r,wishlists:r.wishlists.map(r=>r.id===e?{...r,items:[...r.items,{id:o(),title:s}]}:r)}))},removeWishlistItem:(e,s)=>{d(r=>({...r,wishlists:r.wishlists.map(r=>r.id===e?{...r,items:r.items.filter(e=>e.id!==s)}:r)}))}},children:s})},d=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAccount must be used within AccountProvider");return e}},4361:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,5080)),Promise.resolve().then(r.bind(r,6821)),Promise.resolve().then(r.bind(r,5793)),Promise.resolve().then(r.bind(r,3503)),Promise.resolve().then(r.bind(r,9634)),Promise.resolve().then(r.bind(r,4303)),Promise.resolve().then(r.bind(r,9576)),Promise.resolve().then(r.bind(r,8088))},5080:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(5155),a=r(2115),n=r(1243),i=r(3904),l=r(7550),o=r(7340);class c extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s),this.setState({hasError:!0,error:e,errorInfo:s}),this.props.onError&&this.props.onError(e,s),this.logErrorToService(e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.A,{className:"mx-auto h-16 w-16 text-red-500"}),(0,t.jsx)("h1",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Something went wrong"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"We apologize for the inconvenience. An unexpected error has occurred."}),!1]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,t.jsxs)("button",{onClick:this.handleRetry,className:"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Try Again"]}),(0,t.jsxs)("button",{onClick:this.handleReload,className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Reload Page"]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,t.jsxs)("button",{onClick:this.handleGoBack,className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,t.jsxs)("button",{onClick:this.handleGoHome,className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,t.jsx)("p",{children:"If this problem persists, please contact our support team."}),(0,t.jsxs)("p",{className:"mt-1",children:["Error ID: ",Date.now().toString(36).toUpperCase()]})]})]})}):this.props.children}constructor(e){super(e),this.logErrorToService=(e,s)=>{console.log("Logging error to external service:",{error:e.message,stack:e.stack,componentStack:s.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href})},this.handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})},this.handleGoHome=()=>{window.location.href="/"},this.handleGoBack=()=>{window.history.back()},this.handleReload=()=>{window.location.reload()},this.state={hasError:!1}}}let d=c},5793:(e,s,r)=>{"use strict";r.d(s,{default:()=>y});var t=r(5155),a=r(2115),n=r(6874),i=r.n(n),l=r(4416),o=r(4783),c=r(7924),d=r(1007),h=r(381),m=r(1976),x=r(6151);let g=e=>{let{type:s}=e,r=[{title:"Just In",links:[{name:"Just in this month",href:"/womens/just-in/just-in-this-month"},{name:"Just in 7 days",href:"/womens/just-in/just-in-last-7-days"},{name:"Back in stock",href:"/womens/lists/back-in-stock"},{name:"Coming soon",href:"/womens/lists/coming-soon"},{name:"Exclusives",href:"/womens/lists/exclusives"}]},{title:"Clothing",links:[{name:"Shop all",href:"/womens/shop/clothing"},{name:"Dresses",href:"/womens/shop/clothing/dresses"},{name:"Coats",href:"/womens/shop/clothing/coats"},{name:"Jackets",href:"/womens/shop/clothing/jackets"},{name:"Knitwear",href:"/womens/shop/clothing/knitwear"},{name:"Tops",href:"/womens/shop/clothing/tops"},{name:"Trousers",href:"/womens/shop/clothing/trousers"},{name:"Jeans",href:"/womens/shop/clothing/jeans"},{name:"Skirts",href:"/womens/shop/clothing/skirts"}]},{title:"Shoes",links:[{name:"Shop all",href:"/womens/shop/shoes"},{name:"Boots",href:"/womens/shop/shoes/boots"},{name:"Heels",href:"/womens/shop/shoes/heels"},{name:"Flats",href:"/womens/shop/shoes/flats"},{name:"Trainers",href:"/womens/shop/shoes/sneakers"},{name:"Sandals",href:"/womens/shop/shoes/sandals"}]},{title:"Bags",links:[{name:"Shop all",href:"/womens/shop/bags"},{name:"Tote bags",href:"/womens/shop/bags/tote-bags"},{name:"Shoulder bags",href:"/womens/shop/bags/shoulder-bags"},{name:"Cross-body bags",href:"/womens/shop/bags/cross-body-bags"},{name:"Clutch bags",href:"/womens/shop/bags/clutch-bags"},{name:"Mini bags",href:"/womens/shop/bags/mini-bags"}]},{title:"Accessories",links:[{name:"View all",href:"/womens/shop/accessories"},{name:"Jewellery",href:"/womens/shop/jewellery-and-watches"},{name:"Sunglasses",href:"/womens/shop/accessories/sunglasses"},{name:"Belts",href:"/womens/shop/accessories/belts"},{name:"Scarves",href:"/womens/shop/accessories/scarves"},{name:"Hats",href:"/womens/shop/accessories/hats"}]}],a=[{title:"Just In",links:[{name:"Just in this month",href:"/mens/just-in/just-in-this-month"},{name:"Just in 7 days",href:"/mens/just-in/just-in-last-7-days"},{name:"Back in stock",href:"/mens/lists/back-in-stock"},{name:"Coming soon",href:"/mens/lists/coming-soon"},{name:"Exclusives",href:"/mens/lists/exclusives"}]},{title:"Clothing",links:[{name:"Shop all",href:"/mens/shop/clothing"},{name:"Suits",href:"/mens/shop/clothing/suits"},{name:"Blazers",href:"/mens/shop/clothing/blazers"},{name:"Coats",href:"/mens/shop/clothing/coats"},{name:"Knitwear",href:"/mens/shop/clothing/knitwear"},{name:"Shirts",href:"/mens/shop/clothing/casual-shirts"},{name:"T-shirts",href:"/mens/shop/clothing/t-shirts"},{name:"Trousers",href:"/mens/shop/clothing/trousers"},{name:"Jeans",href:"/mens/shop/clothing/jeans"}]},{title:"Shoes",links:[{name:"Shop all",href:"/mens/shop/shoes"},{name:"Formal shoes",href:"/mens/shop/shoes/dress-shoes"},{name:"Boots",href:"/mens/shop/shoes/boots"},{name:"Trainers",href:"/mens/shop/shoes/sneakers"},{name:"Loafers",href:"/mens/shop/shoes/loafers"}]},{title:"Bags",links:[{name:"Shop all",href:"/mens/shop/bags"},{name:"Backpacks",href:"/mens/shop/bags/backpacks"},{name:"Travel bags",href:"/mens/shop/bags/travel-bags"},{name:"Briefcases",href:"/mens/shop/bags/briefcases"},{name:"Messenger bags",href:"/mens/shop/bags/messenger-bags"}]},{title:"Accessories",links:[{name:"View all",href:"/mens/shop/accessories"},{name:"Watches",href:"/mens/shop/accessories/jewellery/watches"},{name:"Sunglasses",href:"/mens/shop/accessories/sunglasses"},{name:"Belts",href:"/mens/shop/accessories/belts"},{name:"Wallets",href:"/mens/shop/accessories/wallets"},{name:"Ties",href:"/mens/shop/accessories/ties"}]}],{categories:n,designers:l,showProducts:o}=(()=>{switch(s){case"women":return{categories:r,designers:["Gucci","Saint Laurent","Bottega Veneta","The Row","Khaite","Toteme"],showProducts:!0};case"men":return{categories:a,designers:["Tom Ford","Brunello Cucinelli","Stone Island","Thom Browne","Bottega Veneta","Gucci"],showProducts:!0};case"just-in":return{categories:[{title:"Women",links:[{name:"Just in this month",href:"/womens/just-in/just-in-this-month"},{name:"Just in 7 days",href:"/womens/just-in/just-in-last-7-days"},{name:"Back in stock",href:"/womens/lists/back-in-stock"},{name:"Coming soon",href:"/womens/lists/coming-soon"}]},{title:"Men",links:[{name:"Just in this month",href:"/mens/just-in/just-in-this-month"},{name:"Just in 7 days",href:"/mens/just-in/just-in-last-7-days"},{name:"Back in stock",href:"/mens/lists/back-in-stock"},{name:"Coming soon",href:"/mens/lists/coming-soon"}]},{title:"Categories",links:[{name:"Clothing",href:"/just-in/clothing"},{name:"Shoes",href:"/just-in/shoes"},{name:"Bags",href:"/just-in/bags"},{name:"Accessories",href:"/just-in/accessories"}]}],designers:["New Arrivals","Trending Now","Editor's Picks"],showProducts:!0};case"designers":return{categories:[{title:"A-D",links:[{name:"Acne Studios",href:"/designers/acne-studios"},{name:"Bottega Veneta",href:"/designers/bottega-veneta"},{name:"Celine",href:"/designers/celine"},{name:"Dior",href:"/designers/dior"}]},{title:"E-H",links:[{name:"Fendi",href:"/designers/fendi"},{name:"Gucci",href:"/designers/gucci"},{name:"Hermes",href:"/designers/hermes"}]},{title:"I-M",links:[{name:"Jacquemus",href:"/designers/jacquemus"},{name:"Khaite",href:"/designers/khaite"},{name:"Loewe",href:"/designers/loewe"},{name:"Moncler",href:"/designers/moncler"}]},{title:"N-S",links:[{name:"Off-White",href:"/designers/off-white"},{name:"Prada",href:"/designers/prada"},{name:"Saint Laurent",href:"/designers/saint-laurent"}]},{title:"T-Z",links:[{name:"The Row",href:"/designers/the-row"},{name:"Toteme",href:"/designers/toteme"},{name:"Valentino",href:"/designers/valentino"},{name:"Zimmermann",href:"/designers/zimmermann"}]}],designers:["Featured Designers","New Designers","Luxury Brands"],showProducts:!0};default:return{categories:[{title:"Women",links:[{name:"Shop all",href:"/womens/".concat(s)},{name:"New arrivals",href:"/womens/".concat(s,"/new")},{name:"Best sellers",href:"/womens/".concat(s,"/best-sellers")}]},{title:"Men",links:[{name:"Shop all",href:"/mens/".concat(s)},{name:"New arrivals",href:"/mens/".concat(s,"/new")},{name:"Best sellers",href:"/mens/".concat(s,"/best-sellers")}]}],designers:["Featured Brands","New Arrivals","Best Sellers"],showProducts:!1}}})(),c=n.length+1;return(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"grid ".concat(c<=3?"grid-cols-3":c<=4?"grid-cols-4":c<=5?"grid-cols-5":"grid-cols-6"," gap-8"),children:[n.map((e,s)=>(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide text-gray-900",children:e.title}),(0,t.jsx)("ul",{className:"space-y-2",children:e.links.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-sm text-gray-600 hover:text-black transition-colors",children:e.name})},s))})]},s)),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"font-semibold text-sm uppercase tracking-wide text-gray-900",children:["Featured ","designers"===s?"Collections":"Designers"]}),(0,t.jsx)("ul",{className:"space-y-2",children:l.map((e,r)=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:"/".concat(s,"/designers/").concat(e.toLowerCase().replace(/\s+/g,"-")),className:"text-sm text-gray-600 hover:text-black transition-colors",children:e})},r))})]})]}),o&&(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,t.jsx)("div",{className:"grid grid-cols-4 gap-6",children:[1,2,3,4].map(e=>(0,t.jsxs)("div",{className:"group cursor-pointer",children:[(0,t.jsx)("div",{className:"aspect-square bg-gray-100 mb-3 overflow-hidden",children:(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"Product Image"})})}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Designer Name"}),(0,t.jsx)("p",{className:"text-sm font-medium",children:"Product Name"}),(0,t.jsx)("p",{className:"text-sm font-semibold",children:"\xa3XXX"})]})]},e))})})]})};var u=r(3109);let f=[{id:1,name:"Ophidia GG Supreme bag",brand:"Gucci",price:1200,category:"Bags"},{id:2,name:"Opyum pumps",brand:"Saint Laurent",price:895,category:"Shoes"},{id:3,name:"Cashmere coat",brand:"The Row",price:2890,category:"Coats"},{id:4,name:"Intrecciato wallet",brand:"Bottega Veneta",price:450,category:"Accessories"}],p=e=>{let{isOpen:s,onClose:r}=e,[n,o]=(0,a.useState)(""),[d,h]=(0,a.useState)([]);return((0,a.useEffect)(()=>{n.length>2?h(f.filter(e=>e.name.toLowerCase().includes(n.toLowerCase())||e.brand.toLowerCase().includes(n.toLowerCase())||e.category.toLowerCase().includes(n.toLowerCase()))):h([])},[n]),(0,a.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]),s)?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 bg-white",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 p-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(c.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,t.jsx)("input",{type:"text",placeholder:"Search for designers, products, or categories...",value:n,onChange:e=>o(e.target.value),className:"w-full pl-12 pr-4 py-3 text-lg border-none outline-none",autoFocus:!0})]}),(0,t.jsx)("button",{onClick:r,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,t.jsx)(l.A,{size:24})})]})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto p-4",children:0===n.length?(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,t.jsx)(u.A,{size:20,className:"mr-2"}),"Trending Searches"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:["Gucci bags","Saint Laurent shoes","The Row coats","Bottega Veneta","Khaite dresses","Designer jeans","Luxury sneakers","Evening dresses"].map((e,s)=>(0,t.jsx)("button",{onClick:()=>o(e),className:"text-left p-3 border border-gray-200 hover:border-black transition-colors text-sm",children:e},s))}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Popular Categories"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(i(),{href:"/womens/shop/clothing/dresses",className:"group",children:[(0,t.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"Dresses"})}),(0,t.jsx)("h4",{className:"font-medium group-hover:text-gray-600",children:"Women's Dresses"})]}),(0,t.jsxs)(i(),{href:"/womens/shop/bags",className:"group",children:[(0,t.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"Bags"})}),(0,t.jsx)("h4",{className:"font-medium group-hover:text-gray-600",children:"Designer Bags"})]}),(0,t.jsxs)(i(),{href:"/mens/shop/clothing/suits",className:"group",children:[(0,t.jsx)("div",{className:"aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"Suits"})}),(0,t.jsx)("h4",{className:"font-medium group-hover:text-gray-600",children:"Men's Suits"})]})]})]})]}):(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:['Search Results for "',n,'" (',d.length,")"]}),d.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,t.jsxs)(i(),{href:"/product/".concat(e.id),className:"flex items-center space-x-4 p-4 hover:bg-gray-50 transition-colors",onClick:r,children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"IMG"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.brand}),(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["\xa3",e.price]})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.category})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsxs)("p",{className:"text-gray-500 mb-4",children:['No results found for "',n,'"']}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Try searching for designers, product names, or categories"})]})]})})]}):null};var j=r(9576),b=r(8088);let y=()=>{let[e,s]=(0,a.useState)(!1),[r,n]=(0,a.useState)(null),[u,f]=(0,a.useState)(!1),[y,v]=(0,a.useState)(null),{user:N,logout:w}=(0,j.A)(),{items:k}=(0,b._)();(0,a.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]);let C=()=>{s(!e)},S=e=>{y&&(clearTimeout(y),v(null)),n(e)};return(0,t.jsxs)("header",{className:"relative bg-white border-b border-gray-200",children:[(0,t.jsx)("div",{className:"hidden md:block bg-black text-white text-xs py-2",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex space-x-6",children:[(0,t.jsx)(i(),{href:"/help",className:"hover:underline",children:"Help Centre"}),(0,t.jsx)(i(),{href:"/delivery",className:"hover:underline",children:"Delivery"}),(0,t.jsx)(i(),{href:"/returns",className:"hover:underline",children:"Returns"})]}),(0,t.jsxs)("div",{className:"flex space-x-6",children:[(0,t.jsx)(i(),{href:"/stores",className:"hover:underline",children:"Visit Us"}),(0,t.jsx)(i(),{href:"/apps",className:"hover:underline",children:"Our Apps"})]})]})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 relative",onMouseLeave:()=>{v(setTimeout(()=>{n(null)},300))},children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 md:h-20",children:[(0,t.jsx)("button",{className:"flex md:hidden p-2 mr-4",onClick:C,"aria-label":"Toggle menu",children:e?(0,t.jsx)(l.A,{size:24}):(0,t.jsx)(o.A,{size:24})}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i(),{href:"/",className:"flex-shrink-0 mr-8",children:(0,t.jsx)("span",{className:"text-2xl md:text-3xl font-bold tracking-wider",children:"MATCHES"})}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,t.jsx)("div",{onMouseEnter:()=>S("men"),children:(0,t.jsx)(i(),{href:"/mens",className:"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Men"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("women"),children:(0,t.jsx)(i(),{href:"/womens",className:"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Women"})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 md:space-x-4",children:[(0,t.jsxs)("div",{className:"hidden md:flex items-center bg-gray-100 rounded-full px-4 py-2 w-80",children:[(0,t.jsx)(c.A,{size:16,className:"text-gray-400 mr-2"}),(0,t.jsx)("input",{type:"text",placeholder:"Search for products, designers...",className:"bg-transparent text-sm flex-1 outline-none placeholder-gray-500",onClick:()=>f(!0),readOnly:!0})]}),(0,t.jsx)("button",{className:"flex md:hidden p-2 hover:bg-gray-100 rounded-full transition-colors",onClick:()=>f(!0),children:(0,t.jsx)(c.A,{size:20})}),(0,t.jsx)(i(),{href:N?"/account":"/account/login",className:"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,t.jsx)(d.A,{size:20})}),N&&(0,t.jsx)("button",{onClick:w,className:"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors","aria-label":"Settings",children:(0,t.jsx)(h.A,{size:20})}),(0,t.jsx)(i(),{href:"/account/wishlists",className:"hidden md:flex p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,t.jsx)(m.A,{size:20})}),(0,t.jsxs)(i(),{href:"/cart",className:"p-2 hover:bg-gray-100 rounded-full transition-colors relative",children:[(0,t.jsx)(x.A,{size:20}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:k.length})]})]})]}),(0,t.jsx)("div",{className:"hidden md:block border-t border-gray-100 py-3",children:(0,t.jsxs)("nav",{className:"flex items-center space-x-8",children:[(0,t.jsx)("div",{onMouseEnter:()=>S("just-in"),children:(0,t.jsx)(i(),{href:"/just-in",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Just In"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("designers"),children:(0,t.jsx)(i(),{href:"/designers",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Designers"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("clothing"),children:(0,t.jsx)(i(),{href:"/clothing",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Clothing"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("dresses"),children:(0,t.jsx)(i(),{href:"/dresses",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Dresses"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("shoes"),children:(0,t.jsx)(i(),{href:"/shoes",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Shoes"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("bags"),children:(0,t.jsx)(i(),{href:"/bags",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Bags"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("accessories"),children:(0,t.jsx)(i(),{href:"/accessories",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Accessories"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("jewellery"),children:(0,t.jsx)(i(),{href:"/jewellery-watches",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Jewellery & Watches"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("home"),children:(0,t.jsx)(i(),{href:"/home",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Home"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("edits"),children:(0,t.jsx)(i(),{href:"/edits",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Edits"})}),(0,t.jsx)("div",{onMouseEnter:()=>S("outlet"),children:(0,t.jsx)(i(),{href:"/outlet",className:"text-xs font-medium tracking-wide uppercase hover:text-gray-600 transition-colors",children:"Outlet"})})]})}),r&&(0,t.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white shadow-xl border-t border-gray-200 z-50",onMouseEnter:()=>S(r),children:(0,t.jsx)(g,{type:r})})]}),e&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50",onClick:C}),(0,t.jsxs)("div",{className:"fixed left-0 top-0 h-full w-72 sm:w-80 bg-white shadow-xl",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,t.jsx)("span",{className:"text-xl font-bold",children:"MATCHES"}),(0,t.jsx)("button",{onClick:C,children:(0,t.jsx)(l.A,{size:24})})]}),(0,t.jsx)("nav",{className:"p-4 overflow-y-auto",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i(),{href:"/womens",className:"block text-lg font-medium py-2 border-b border-gray-100",onClick:C,children:"Women"}),(0,t.jsx)(i(),{href:"/mens",className:"block text-lg font-medium py-2 border-b border-gray-100",onClick:C,children:"Men"})]}),(0,t.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2",children:"Shop"}),(0,t.jsx)(i(),{href:"/just-in",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Just In"}),(0,t.jsx)(i(),{href:"/designers",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Designers"}),(0,t.jsx)(i(),{href:"/clothing",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Clothing"}),(0,t.jsx)(i(),{href:"/dresses",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Dresses"}),(0,t.jsx)(i(),{href:"/shoes",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Shoes"}),(0,t.jsx)(i(),{href:"/bags",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Bags"}),(0,t.jsx)(i(),{href:"/accessories",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Accessories"}),(0,t.jsx)(i(),{href:"/jewellery-watches",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Jewellery & Watches"}),(0,t.jsx)(i(),{href:"/home",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Home"}),(0,t.jsx)(i(),{href:"/edits",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Edits"}),(0,t.jsx)(i(),{href:"/outlet",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Outlet"})]}),(0,t.jsxs)("div",{className:"pt-4 space-y-2 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2",children:"Account"}),(0,t.jsx)(i(),{href:N?"/account":"/account/login",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:N?"My Account":"Sign In"}),(0,t.jsx)(i(),{href:"/settings",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Settings"}),(0,t.jsx)(i(),{href:"/help",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Help Centre"}),(0,t.jsx)(i(),{href:"/delivery",className:"block text-sm py-1 hover:text-gray-600",onClick:C,children:"Delivery"})]})]})})]})]}),(0,t.jsx)(p,{isOpen:u,onClose:()=>f(!1)})]})}},6821:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(5155),a=r(6874),n=r.n(a),i=r(5684),l=r(488),o=r(2925),c=r(8175);let d=()=>(0,t.jsxs)("footer",{className:"bg-white border-t border-gray-200",children:[(0,t.jsx)("div",{className:"bg-gray-50 py-12",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Stay in the know"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Be the first to discover new arrivals, exclusive collections, and styling tips"}),(0,t.jsxs)("div",{className:"flex max-w-md mx-auto",children:[(0,t.jsx)("input",{type:"email",placeholder:"Enter your email address",className:"flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black"}),(0,t.jsx)("button",{className:"luxury-button",children:"Sign up"})]})]})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"MATCHES"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/bio",className:"text-sm text-gray-600 hover:text-black",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/careers",className:"text-sm text-gray-600 hover:text-black",children:"Careers"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/affiliates",className:"text-sm text-gray-600 hover:text-black",children:"Affiliates"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/press",className:"text-sm text-gray-600 hover:text-black",children:"Press"})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Services"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/private-shopping",className:"text-sm text-gray-600 hover:text-black",children:"Private Shopping"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/loyalty",className:"text-sm text-gray-600 hover:text-black",children:"Loyalty"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/rental",className:"text-sm text-gray-600 hover:text-black",children:"MATCHES Rental"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/gift-cards",className:"text-sm text-gray-600 hover:text-black",children:"Gift Cards"})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Legal"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/terms",className:"text-sm text-gray-600 hover:text-black",children:"Terms and Conditions"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/privacy",className:"text-sm text-gray-600 hover:text-black",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/cookies",className:"text-sm text-gray-600 hover:text-black",children:"Cookie Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/modern-slavery",className:"text-sm text-gray-600 hover:text-black",children:"Modern Slavery Statement"})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Visit Us"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/stores/5carlosplace",className:"text-sm text-gray-600 hover:text-black",children:"5 Carlos Place"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/stores/marylebone",className:"text-sm text-gray-600 hover:text-black",children:"Marylebone"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/stores/wimbledon",className:"text-sm text-gray-600 hover:text-black",children:"Wimbledon"})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Help Centre"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/help",className:"text-sm text-gray-600 hover:text-black",children:"Help Centre"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/returns",className:"text-sm text-gray-600 hover:text-black",children:"Returning an item"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/delivery",className:"text-sm text-gray-600 hover:text-black",children:"Delivery"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/size-guide",className:"text-sm text-gray-600 hover:text-black",children:"Size Guide"})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsx)("h3",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Follow Us"}),(0,t.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,t.jsx)(n(),{href:"https://instagram.com/matches",className:"text-gray-600 hover:text-black",children:(0,t.jsx)(i.A,{size:20})}),(0,t.jsx)(n(),{href:"https://facebook.com/matches",className:"text-gray-600 hover:text-black",children:(0,t.jsx)(l.A,{size:20})}),(0,t.jsx)(n(),{href:"https://youtube.com/matches",className:"text-gray-600 hover:text-black",children:(0,t.jsx)(o.A,{size:20})}),(0,t.jsx)(n(),{href:"https://twitter.com/matches",className:"text-gray-600 hover:text-black",children:(0,t.jsx)(c.A,{size:20})})]}),(0,t.jsx)("h4",{className:"font-semibold text-sm uppercase tracking-wide mb-4",children:"Our Apps"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n(),{href:"#",className:"block",children:(0,t.jsx)("div",{className:"bg-black text-white px-3 py-2 text-xs rounded",children:"Download on the App Store"})}),(0,t.jsx)(n(),{href:"#",className:"block",children:(0,t.jsx)("div",{className:"bg-black text-white px-3 py-2 text-xs rounded",children:"Get it on Google Play"})})]})]})]})}),(0,t.jsx)("div",{className:"border-t border-gray-200 py-6",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4 md:mb-0",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Shipping to"}),(0,t.jsx)("button",{className:"text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black",children:"United Kingdom"})]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"\xa9 Copyright 2024 MATCHES"})]})})]})},8088:(e,s,r)=>{"use strict";r.d(s,{CartProvider:()=>l,_:()=>o});var t=r(5155),a=r(2115);let n=(0,a.createContext)(void 0),i="cartItems",l=e=>{let{children:s}=e,[r,l]=(0,a.useState)([]);return(0,a.useEffect)(()=>{let e=localStorage.getItem(i);e&&l(JSON.parse(e))},[]),(0,a.useEffect)(()=>{localStorage.setItem(i,JSON.stringify(r))},[r]),(0,t.jsx)(n.Provider,{value:{items:r,addItem:function(e,s,r){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;l(a=>[...a,{id:Math.random().toString(36).slice(2)+Date.now().toString(36),product:e,variant:s,size:r,quantity:t}])},removeItem:e=>{l(s=>s.filter(s=>s.id!==e))},clearCart:()=>l([])},children:s})},o=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useCart must be used within CartProvider");return e}},9576:(e,s,r)=>{"use strict";r.d(s,{A:()=>l,AuthProvider:()=>i});var t=r(5155),a=r(2115);let n=(0,a.createContext)(void 0),i=e=>{let{children:s}=e,[r,i]=(0,a.useState)(null);return(0,a.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&i(JSON.parse(e))},[]),(0,t.jsx)(n.Provider,{value:{user:r,login:(e,s)=>{let r=localStorage.getItem("authUser");if(r){let t=JSON.parse(r);if(t.email===e&&t.password===s)return i(t),!0}return!1},register:(e,s)=>{let r={email:e,password:s};return localStorage.setItem("authUser",JSON.stringify(r)),i(r),!0},logout:()=>{i(null),localStorage.removeItem("authUser")}},children:s})},l=()=>{let e=(0,a.useContext)(n);if(!e)throw Error("useAuth must be used within AuthProvider");return e}},9634:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(5155),a=r(2115),n=r(6767),i=r(2280),l=r(4738),o=r(5038),c=r(4416);let d=()=>{var e;let[s,r]=(0,a.useState)(!1),[d,h]=(0,a.useState)("desktop"),m=[{id:"mobile",name:"Mobile",width:"375px",icon:n.A},{id:"tablet",name:"Tablet",width:"768px",icon:i.A},{id:"desktop",name:"Desktop",width:"1920px",icon:l.A}];return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:"QC Testing Panel - Luxury Fashion Standards"}),(0,t.jsx)("button",{onClick:()=>r(!1),className:"p-2 hover:bg-gray-100 rounded-full",children:(0,t.jsx)(c.A,{size:20})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Responsive Breakpoint Testing"}),(0,t.jsx)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:m.map(e=>{let s=e.icon;return(0,t.jsxs)("button",{onClick:()=>h(e.id),className:"p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ".concat(d===e.id?"border-black bg-gray-50":"border-gray-200 hover:border-gray-400"),children:[(0,t.jsx)(s,{size:24}),(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:e.width})]},e.id)})}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Current testing viewport: ",(0,t.jsx)("strong",{children:null==(e=m.find(e=>e.id===d))?void 0:e.width})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Page Testing"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[{name:"Homepage",path:"/"},{name:"Women's Landing",path:"/womens"},{name:"Women's Clothing",path:"/womens/shop/clothing"},{name:"Men's Landing",path:"/mens"},{name:"Men's Clothing",path:"/mens/shop/clothing"}].map((e,s)=>(0,t.jsxs)("a",{href:e.path,target:"_blank",rel:"noopener noreferrer",className:"p-3 border border-gray-200 rounded hover:border-black transition-colors text-sm",children:[e.name," →"]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Luxury Fashion QC Checklist"}),(0,t.jsx)("div",{className:"space-y-2",children:["Header navigation is properly aligned","Logo is clearly visible and properly sized","Mega menus display correctly on hover","Product grids maintain proper spacing","Typography hierarchy is consistent","Interactive elements have proper hover states","Footer content is well-organized","Mobile navigation works smoothly","Search functionality is accessible","Product cards display correctly"].map((e,s)=>(0,t.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,t.jsx)("input",{type:"checkbox",className:"w-4 h-4 text-black border-gray-300 rounded focus:ring-black"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Luxury Fashion Design Standards"}),(0,t.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Typography"}),(0,t.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,t.jsx)("li",{children:"• Clean, geometric sans-serif fonts"}),(0,t.jsx)("li",{children:"• Consistent hierarchy and spacing"}),(0,t.jsx)("li",{children:"• Uppercase tracking for headings"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Layout"}),(0,t.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,t.jsx)("li",{children:"• No rounded corners (clean geometric design)"}),(0,t.jsx)("li",{children:"• Sophisticated spacing hierarchy"}),(0,t.jsx)("li",{children:"• Pixel-perfect alignment"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Interactions"}),(0,t.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,t.jsx)("li",{children:"• Smooth hover transitions"}),(0,t.jsx)("li",{children:"• Luxury micro-interactions"}),(0,t.jsx)("li",{children:"• Responsive touch targets"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Content"}),(0,t.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,t.jsx)("li",{children:"• High-quality product imagery"}),(0,t.jsx)("li",{children:"• Rich mega menu layouts"}),(0,t.jsx)("li",{children:"• Editorial content integration"})]})]})]})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Testing Instructions"}),(0,t.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg",children:(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,t.jsx)("li",{children:"Test each page at all three breakpoints (375px, 768px, 1920px)"}),(0,t.jsx)("li",{children:"Verify navigation functionality and mega menu interactions"}),(0,t.jsx)("li",{children:"Check product grid layouts and card hover states"}),(0,t.jsx)("li",{children:"Ensure search modal opens and functions correctly"}),(0,t.jsx)("li",{children:"Validate footer content organization and links"}),(0,t.jsx)("li",{children:"Test mobile navigation and touch interactions"}),(0,t.jsx)("li",{children:"Verify pixel-perfect alignment and spacing"}),(0,t.jsx)("li",{children:"Check for luxury fashion aesthetic consistency"})]})})]})]})]})}):(0,t.jsx)("button",{onClick:()=>r(!0),className:"fixed bottom-4 right-4 bg-black text-white p-3 rounded-full shadow-lg hover:bg-gray-800 transition-colors z-50",title:"Open QC Testing Panel",children:(0,t.jsx)(o.A,{size:20})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[552,96,358],()=>s(4361)),_N_E=e.O()}]);