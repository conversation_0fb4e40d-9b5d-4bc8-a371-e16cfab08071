{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/templates/CategoryGridTemplate.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CategoryGridTemplateProps {\n  children: React.ReactNode;\n}\n\nconst CategoryGridTemplate: React.FC<CategoryGridTemplateProps> = ({ children }) => {\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-8\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        <aside className=\"lg:col-span-1\">\n          {/* Sidebar content will be rendered here by the page renderer */}\n        </aside>\n        <main className=\"lg:col-span-3\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryGridTemplate;\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,uBAA4D,CAAC,EAAE,QAAQ,EAAE;IAC7E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAU;;;;;;8BAGjB,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}]}