(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{2843:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(5155),l=s(6874),a=s.n(l),n=s(8088);function i(){let{items:e,removeItem:t,clearCart:s}=(0,n._)(),l=e.reduce((e,t)=>e+t.product.price*t.quantity,0);return 0===e.length?(0,r.jsxs)("div",{className:"max-w-xl mx-auto py-10 text-center space-y-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Your Bag"}),(0,r.jsx)("p",{children:"Your shopping bag is empty."}),(0,r.jsx)(a(),{href:"/womens/shop/clothing",className:"luxury-button",children:"Continue Shopping"})]}):(0,r.jsxs)("div",{className:"max-w-3xl mx-auto py-10 space-y-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Your Bag"}),(0,r.jsx)("ul",{className:"space-y-4",children:e.map(e=>(0,r.jsxs)("li",{className:"border p-4 flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.product.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Size: ",e.size," | Color: ",e.variant.color]}),(0,r.jsxs)("p",{className:"text-sm",children:["\xa3",e.product.price]})]}),(0,r.jsx)("button",{onClick:()=>t(e.id),className:"text-sm underline",children:"Remove"})]},e.id))}),(0,r.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:["\xa3",l]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:s,className:"luxury-button-outline flex-1",children:"Clear"}),(0,r.jsx)("button",{className:"luxury-button flex-1",children:"Checkout"})]})]})}},4857:(e,t,s)=>{Promise.resolve().then(s.bind(s,2843))},8088:(e,t,s)=>{"use strict";s.d(t,{CartProvider:()=>i,_:()=>c});var r=s(5155),l=s(2115);let a=(0,l.createContext)(void 0),n="cartItems",i=e=>{let{children:t}=e,[s,i]=(0,l.useState)([]);return(0,l.useEffect)(()=>{let e=localStorage.getItem(n);e&&i(JSON.parse(e))},[]),(0,l.useEffect)(()=>{localStorage.setItem(n,JSON.stringify(s))},[s]),(0,r.jsx)(a.Provider,{value:{items:s,addItem:function(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;i(l=>[...l,{id:Math.random().toString(36).slice(2)+Date.now().toString(36),product:e,variant:t,size:s,quantity:r}])},removeItem:e=>{i(t=>t.filter(t=>t.id!==e))},clearCart:()=>i([])},children:t})},c=()=>{let e=(0,l.useContext)(a);if(!e)throw Error("useCart must be used within CartProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(4857)),_N_E=e.O()}]);