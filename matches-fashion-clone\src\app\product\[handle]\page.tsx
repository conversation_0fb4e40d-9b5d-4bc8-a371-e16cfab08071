import { medusaClient } from "../../../../lib/medusa";
import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link"; // For breadcrumbs or other links

// Define a basic structure for Medusa product details (can be expanded)
interface MedusaProductDetail {
  id: string;
  title: string;
  subtitle: string | null; // For brand/designer line
  description: string | null;
  thumbnail: string | null;
  handle: string | null;
  images?: Array<{ id: string; url: string }>;
  options?: Array<{
    id: string;
    title: string;
    values: Array<{ id: string; value: string }>;
  }>;
  variants?: Array<{
    id: string;
    title: string;
    prices: Array<{ amount: number; currency_code: string; price_list_id: string | null }>;
    inventory_quantity: number;
    options?: Array<{ option_id: string; value: string }>;
  }>;
  collection?: { title: string; handle: string };
  // Add other fields as needed
}

interface ProductPageProps {
  params: Promise<{
    handle: string;
  }>;
}

// Helper to format price
const formatPrice = (amount?: number, currencyCode?: string) => {
  if (amount === undefined || currencyCode === undefined) return "Price unavailable";
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0, // Assuming prices are whole numbers if they end in 00
    maximumFractionDigits: 2,
  }).format(amount / 100); // Medusa prices are in smallest unit
};

export default async function ProductPage({ params }: ProductPageProps) {
  const { handle } = await params;
  let product: MedusaProductDetail | null = null;

  console.log(`PDP: Fetching product with handle: ${handle}`);

  try {
    // Step 1: Fetch product by handle to get its ID, ensuring it's in the correct sales channel
    const { products } = await medusaClient.products.list({ 
      handle, 
      sales_channel_id: ["sc_01JXEB1SMN7PBE8Y93SSSQWGNS"] 
    });
    console.log(`PDP: Initial fetch by handle '${handle}' (Sales Channel sc_01JXEB1SMN7PBE8Y93SSSQWGNS):`, JSON.stringify(products, null, 2));

    if (products && products.length > 0 && products[0].id) {
      const productId = products[0].id;
      console.log(`PDP: Found product ID: ${productId}. Retrieving full details...`);
      // Step 2: Retrieve the full product details by its ID
      const detailedProductResponse = await medusaClient.products.retrieve(productId);
      product = detailedProductResponse.product as MedusaProductDetail;
      console.log(`PDP: Retrieved detailed product:`, JSON.stringify(product, null, 2));
    } else {
      console.log(`PDP: Product with handle '${handle}' not found by list endpoint or product has no ID.`);
      notFound();
    }
  } catch (error) {
    console.error(`PDP: Error during fetch for product with handle ${handle}:`, error);
    notFound();
  }

  if (!product) {
    // Should be caught above, but as a safeguard.
    console.log(`PDP: Product object is unexpectedly null for handle '${handle}'.`);
    notFound();
  }

  const brandName = product.collection?.title || product.subtitle || "Brand"; // Use collection title or subtitle as brand
  const firstVariant = product.variants?.[0];
  const price = formatPrice(firstVariant?.prices?.[0]?.amount, firstVariant?.prices?.[0]?.currency_code);

  return (
    <div className="bg-white text-black py-8"> {/* Main text to black */}
      <div className="container mx-auto px-4 lg:px-8">
        {/* Breadcrumbs (simplified) */}
        <div className="text-sm text-gray-600 mb-6"> {/* Slightly darker gray for breadcrumbs */}
          <Link href="/" className="hover:underline text-gray-700">Home</Link>
          <span className="mx-2">/</span>
          {/* Ideally, add category breadcrumbs here */}
          <span className="font-medium text-black">{product.title}</span> {/* Product title in breadcrumb black */}
        </div>

        <div className="lg:grid lg:grid-cols-2 lg:gap-12 items-start">
          {/* Image Section (Left Column on Desktop) */}
          <div className="mb-8 lg:mb-0">
            {product.thumbnail ? (
              <div className="relative bg-gray-50 w-[400px] h-[533px]"> {/* Fixed W/H, approx 3:4 ratio for testing */}
                <Image
                  src={product.thumbnail}
                  alt={product.title || "Product image"}
                  fill={true} 
                  className="object-contain w-full h-full"
                />
              </div>
            ) : (
              <div className="w-[400px] h-[533px] bg-gray-50 flex items-center justify-center text-gray-500"> {/* Fixed W/H for placeholder */}
                No Image Available
              </div>
            )}
            {/* Placeholder for thumbnails/gallery controls */}
          </div>

          {/* Product Info Section (Right Column on Desktop) */}
          <div className="sticky top-24"> {/* Sticky for scrolling alongside images if gallery is long */}
            <p className="text-xs uppercase tracking-wider text-black mb-1 font-semibold">EXCLUSIVE</p> {/* Re-added and styled EXCLUSIVE badge */}
            <h2 className="text-2xl font-semibold mb-1 text-black">{brandName}</h2>
            <h1 className="text-xl text-gray-800 mb-3">{product.title}</h1> {/* Slightly lighter for title if brand is full black */}
            <p className="text-2xl font-medium text-black mb-6">{price}</p>
            
            {/* Variant Selection (Simplified) */}
            {product.options && product.options.map(option => (
              <div key={option.id} className="mb-4">
                <label htmlFor={`option-${option.id}`} className="block text-sm font-medium text-black mb-1">
                  {option.title}:
                </label>
                <select 
                  id={`option-${option.id}`} 
                  name={`option-${option.id}`}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-400 focus:outline-none focus:ring-black focus:border-black sm:text-sm rounded-none" // No rounded corners, black focus
                >
                  {option.values.map(value => (
                    <option key={value.id} value={value.id}>{value.value}</option>
                  ))}
                </select>
              </div>
            ))}

            <button 
              type="button"
              className="w-full bg-black text-white py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-white hover:text-black border border-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black mb-3 transition-colors duration-150" // Inverted hover
            >
              ADD TO BAG
            </button>
            <button 
              type="button"
              className="w-full border border-black text-black py-3 px-6 rounded-none text-center text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black flex items-center justify-center transition-colors duration-150"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
              ADD TO FAVORITES
            </button>

            {/* Collapsible Sections Placeholder */}
            <div className="mt-8 space-y-4">
              <div>
                <h3 className="text-sm font-medium text-black border-b border-gray-300 pb-2">EDITOR&apos;S NOTE</h3> {/* Black heading, lighter border */}
                <p className="text-sm text-gray-700 mt-2">{product.description || "Detailed editor&apos;s note about this exclusive piece..."}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-black border-b border-gray-300 pb-2">PRODUCT DETAILS</h3> {/* Black heading, lighter border */}
                <ul className="list-disc list-inside text-sm text-gray-700 mt-2 space-y-1">
                  <li>Spring &apos;25 Collection</li>
                  <li>Composition: Example Material</li>
                  <li>Imported</li>
                  <li>Product Code: {product.id}</li>
                </ul>
              </div>
              {/* Add Size & Fit, Shipping & Returns similarly */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Optional: Generate static paths
// export async function generateStaticParams() {
//   try {
//     const { products } = await medusaClient.products.list({ limit: 10, sales_channel_id: ["sc_01JXEB1SMN7PBE8Y93SSSQWGNS"] });
//     return products.map((product) => ({
//       handle: product.handle!,
//     }));
//   } catch (error) {
//     console.error("Failed to generate static params for products:", error);
//     return [];
//   }
// }
