"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={3264:(e,r,s)=>{s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>h,tree:()=>d});var t=s(65239),o=s(48088),a=s(88170),n=s.n(a),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=[],p={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,20,137],()=>s(3264));module.exports=t})();