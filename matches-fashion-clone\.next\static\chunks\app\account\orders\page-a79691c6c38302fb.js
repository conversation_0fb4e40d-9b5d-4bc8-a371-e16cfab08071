(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[478],{3931:(e,t,s)=>{Promise.resolve().then(s.bind(s,9328))},4303:(e,t,s)=>{"use strict";s.d(t,{AccountProvider:()=>u,F:()=>n});var r=s(5155),i=s(2115),l=s(9576);let a={addresses:[],orders:[],wishlists:[]},d=(0,i.createContext)(void 0);function o(){return Math.random().toString(36).slice(2)+Date.now().toString(36)}let u=e=>{let{children:t}=e,{user:s}=(0,l.A)(),[u,n]=(0,i.useState)(a);return(0,i.useEffect)(()=>{if(s){let e=localStorage.getItem("accountData-"+s.email);e?n(JSON.parse(e)):n(a)}else n(a)},[s]),(0,i.useEffect)(()=>{s&&localStorage.setItem("accountData-"+s.email,JSON.stringify(u))},[u,s]),(0,r.jsx)(d.Provider,{value:{...u,addAddress:e=>{n(t=>({...t,addresses:[...t.addresses,{...e,id:o()}]}))},removeAddress:e=>{n(t=>({...t,addresses:t.addresses.filter(t=>t.id!==e)}))},addWishlist:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";n(s=>({...s,wishlists:[...s.wishlists,{id:o(),name:e,note:t,items:[]}]}))},removeWishlist:e=>{n(t=>({...t,wishlists:t.wishlists.filter(t=>t.id!==e)}))},updateWishlistNote:(e,t)=>{n(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,note:t}:s)}))},addWishlistItem:(e,t)=>{n(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:[...s.items,{id:o(),title:t}]}:s)}))},removeWishlistItem:(e,t)=>{n(s=>({...s,wishlists:s.wishlists.map(s=>s.id===e?{...s,items:s.items.filter(e=>e.id!==t)}:s)}))}},children:t})},n=()=>{let e=(0,i.useContext)(d);if(!e)throw Error("useAccount must be used within AccountProvider");return e}},9328:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(5155),i=s(9576),l=s(4303),a=s(5695),d=s(2115),o=s(6874),u=s.n(o);function n(){let{user:e}=(0,i.A)(),{orders:t}=(0,l.F)(),s=(0,a.useRouter)();return((0,d.useEffect)(()=>{e||s.replace("/account/login")},[e,s]),e)?(0,r.jsxs)("div",{className:"max-w-xl mx-auto py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Order History"}),0===t.length?(0,r.jsx)("p",{children:"No orders found."}):(0,r.jsx)("ul",{className:"space-y-4",children:t.map(e=>(0,r.jsxs)("li",{className:"border p-4",children:[(0,r.jsxs)("p",{className:"font-medium",children:["Order #",e.id]}),(0,r.jsxs)("p",{className:"text-sm",children:["Date: ",e.date]}),(0,r.jsxs)("p",{className:"text-sm",children:["Total: \xa3",e.total.toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm",children:["Status: ",e.status]})]},e.id))}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(u(),{href:"/account",className:"luxury-button-outline",children:"Back to account"})})]}):null}},9576:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,AuthProvider:()=>a});var r=s(5155),i=s(2115);let l=(0,i.createContext)(void 0),a=e=>{let{children:t}=e,[s,a]=(0,i.useState)(null);return(0,i.useEffect)(()=>{let e=localStorage.getItem("authUser");e&&a(JSON.parse(e))},[]),(0,r.jsx)(l.Provider,{value:{user:s,login:(e,t)=>{let s=localStorage.getItem("authUser");if(s){let r=JSON.parse(s);if(r.email===e&&r.password===t)return a(r),!0}return!1},register:(e,t)=>{let s={email:e,password:t};return localStorage.setItem("authUser",JSON.stringify(s)),a(s),!0},logout:()=>{a(null),localStorage.removeItem("authUser")}},children:t})},d=()=>{let e=(0,i.useContext)(l);if(!e)throw Error("useAuth must be used within AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(3931)),_N_E=e.O()}]);