﻿import Link from "next/link";
import ProductCarousel from "@/components/ProductCarousel"; // Assuming @/ is src/
import { medusaClient } from "../../lib/medusa"; // Corrected path
import { fetchStrapiAPI } from "../../lib/strapi"; // Corrected path
import {
  StrapiHomepageAttributes,
  MedusaProduct,
  StrapiMedia,
  StrapiMediaAttributes
} from "../../lib/types";

// Helper to get Strapi media URL
const getStrapiMediaURL = (mediaObject?: StrapiMedia | null): string | null => {
  if (mediaObject && mediaObject.data && mediaObject.data.attributes && mediaObject.data.attributes.url) {
    const url = mediaObject.data.attributes.url;
    const strapiApiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';
    return url.startsWith('/') ? `${strapiApiUrl}${url}` : url;
  }
  return null;
};

const defaultHomepageData: StrapiHomepageAttributes = {
  hero_title: "SHOP",
  hero_subtitle: "24/7 STYLE",
  editorial_block_title: "STYLE INSPIRATION",
  editorial_block_subtitle: "Discover the latest trends",
  editorial_block_link: "/womens/stories",
  featured_product_block_title: "THE EDITORS",
  featured_product_block_subtitle: "Curated selections",
  featured_product_block_link: "/womens/shop/shoes",
  small_blocks_row: [
    { title: "DRESSES", image: "https://via.placeholder.com/300x400.png?text=Dresses", href: "/womens/shop/clothing/dresses", id:"sb1" },
    { title: "BAGS", image: "https://via.placeholder.com/300x400.png?text=Bags", href: "/womens/shop/bags", id:"sb2" },
    { title: "KNITWEAR", image: "https://via.placeholder.com/300x400.png?text=Knitwear", href: "/womens/shop/clothing/knitwear", id:"sb3" },
  ],
  designer_spotlight_title: "DESIGNER SPOTLIGHT",
  designer_spotlight_subtitle: "Exclusive collections",
  designer_spotlight_link: "/womens/designers/the-row",
  new_arrivals_block_title: "NEW ARRIVALS",
  new_arrivals_block_subtitle: "Just landed",
  new_arrivals_block_link: "/womens/just-in",
};

export default async function Home() {
  let homepageData: StrapiHomepageAttributes | null = null;
  let justInProductsMedusa: MedusaProduct[] = [];
  let hasDataErrors = false;

  // Fetch homepage data from Strapi with improved error handling
  const strapiResult = await fetchStrapiAPI<{ attributes: StrapiHomepageAttributes }>("/homepage", { populate: 'deep' });

  if (strapiResult.success && strapiResult.data?.attributes) {
    homepageData = strapiResult.data.attributes;
  } else {
    hasDataErrors = true;
    console.warn("Homepage data from Strapi failed to load:", strapiResult.error?.message);
    homepageData = {
      ...defaultHomepageData,
      hero_title: hasDataErrors ? "SHOP (Content Loading...)" : defaultHomepageData.hero_title,
      hero_subtitle: hasDataErrors ? "Please refresh if content doesn't load" : defaultHomepageData.hero_subtitle,
    };
  }

  // Fetch products from Medusa with improved error handling
  try {
    const { products } = await medusaClient.products.list({
      limit: 12,
      order: "-created_at",
      sales_channel_id: ["sc_01JXEB1SMN7PBE8Y93SSSQWGNS"]
    });
    justInProductsMedusa = products as MedusaProduct[];
  } catch (error) {
    console.error("Failed to fetch products from Medusa:", error);
    hasDataErrors = true;
    // Continue with empty array - the UI will handle the empty state gracefully
  }

  const justInProductsFormatted = justInProductsMedusa.map(product => ({
    id: product.id,
    name: product.title,
    designer: "Designer Placeholder", // Added placeholder for designer
    image: product.thumbnail || '',
    href: `/product/${product.handle || product.id}`,
  }));

  const heroImageUrl = getStrapiMediaURL(homepageData?.hero_background_image) || "https://via.placeholder.com/1600x900.png?text=Hero+Image";
  
  const editorialBlock = {
    title: homepageData?.editorial_block_title || defaultHomepageData.editorial_block_title!,
    subtitle: homepageData?.editorial_block_subtitle || defaultHomepageData.editorial_block_subtitle!,
    image: getStrapiMediaURL(homepageData?.editorial_block_image) || "https://via.placeholder.com/400x500.png?text=Editorial",
    href: homepageData?.editorial_block_link || defaultHomepageData.editorial_block_link!,
  };

  const featuredProductBlock = {
    title: homepageData?.featured_product_block_title || defaultHomepageData.featured_product_block_title!,
    subtitle: homepageData?.featured_product_block_subtitle || defaultHomepageData.featured_product_block_subtitle!,
    image: getStrapiMediaURL(homepageData?.featured_product_block_image) || "https://via.placeholder.com/400x500.png?text=Product",
    href: homepageData?.featured_product_block_link || defaultHomepageData.featured_product_block_link!,
  };
  
  const smallBlocks = homepageData?.small_blocks_row || defaultHomepageData.small_blocks_row!;

  const designerSpotlightBlock = {
    title: homepageData?.designer_spotlight_title || defaultHomepageData.designer_spotlight_title!,
    subtitle: homepageData?.designer_spotlight_subtitle || defaultHomepageData.designer_spotlight_subtitle!,
    image: getStrapiMediaURL(homepageData?.designer_spotlight_image) || "https://via.placeholder.com/400x500.png?text=Designer",
    href: homepageData?.designer_spotlight_link || defaultHomepageData.designer_spotlight_link!,
  };

  const newArrivalsBlock = {
    title: homepageData?.new_arrivals_block_title || defaultHomepageData.new_arrivals_block_title!,
    subtitle: homepageData?.new_arrivals_block_subtitle || defaultHomepageData.new_arrivals_block_subtitle!,
    image: getStrapiMediaURL(homepageData?.new_arrivals_block_image) || "https://via.placeholder.com/400x500.png?text=New+In",
    href: homepageData?.new_arrivals_block_link || defaultHomepageData.new_arrivals_block_link!,
  };

  return (
    <div className="bg-white">
      <section className="relative">
        <div 
          className="aspect-[16/9] lg:aspect-[21/9] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center"
          style={{ backgroundImage: `url(${heroImageUrl})` }}
        >
          {heroImageUrl.includes('placeholder.com') && ( // Show placeholder text if using placeholder URL
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-gray-500 text-lg">Hero Image</span>
            </div>
          )}
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-black bg-white bg-opacity-70 p-4 rounded">
            <p className="text-sm uppercase tracking-wide mb-2">{homepageData?.hero_subtitle || defaultHomepageData.hero_subtitle}</p>
            <h1 className="text-4xl md:text-6xl font-bold mb-4">{homepageData?.hero_title || defaultHomepageData.hero_title}</h1>
          </div>
        </div>
      </section>

      {justInProductsFormatted.length > 0 && (
        <ProductCarousel
          title="NEW STYLES"
          subtitle="JUST LANDED"
          count={justInProductsMedusa.length}
          ctaText="Shop Now"
          ctaHref="/womens/just-in/just-in-this-month"
          products={justInProductsFormatted}
          type="just-in"
        />
      )}

      <section className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          <Link href={editorialBlock.href} className="group relative overflow-hidden">
            <div 
              className="aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center"
              style={{ backgroundImage: `url(${editorialBlock.image})` }}
            >
               {editorialBlock.image?.includes('placeholder.com') && <span className="text-gray-500">Editorial Image</span>}
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">{editorialBlock.title}</h3>
              <p className="text-xs text-gray-600 mt-1">{editorialBlock.subtitle}</p>
            </div>
          </Link>

          <Link href={featuredProductBlock.href} className="group relative overflow-hidden">
            <div 
              className="aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center bg-cover bg-center"
              style={{ backgroundImage: `url(${featuredProductBlock.image})` }}
            >
              {featuredProductBlock.image?.includes('placeholder.com') && <span className="text-gray-500">Product Image</span>}
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">{featuredProductBlock.title}</h3>
              <p className="text-xs text-gray-600 mt-1">{featuredProductBlock.subtitle}</p>
            </div>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {smallBlocks.map((block) => {
            const imageUrl = typeof block.image === 'string' ? block.image : getStrapiMediaURL(block.image);
            return (
              <Link key={block.id || block.title} href={block.href || '#'} className="group relative overflow-hidden">
                <div 
                  className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center"
                  style={{ backgroundImage: `url(${imageUrl || 'https://via.placeholder.com/300x400.png?text=Image'})` }}
                >
                  {imageUrl?.includes('placeholder.com') && <span className="text-gray-500">{block.title} Image</span>}
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3">
                  <h3 className="text-xs font-medium uppercase tracking-wide">{block.title}</h3>
                </div>
              </Link>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Link href={designerSpotlightBlock.href} className="group relative overflow-hidden">
            <div 
              className="aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center bg-cover bg-center"
              style={{ backgroundImage: `url(${designerSpotlightBlock.image})` }}
            >
              {designerSpotlightBlock.image?.includes('placeholder.com') && <span className="text-gray-500">Designer Feature</span>}
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">{designerSpotlightBlock.title}</h3>
              <p className="text-xs text-gray-600 mt-1">{designerSpotlightBlock.subtitle}</p>
            </div>
          </Link>

          <Link href={newArrivalsBlock.href} className="group relative overflow-hidden">
            <div 
              className="aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center bg-cover bg-center"
              style={{ backgroundImage: `url(${newArrivalsBlock.image})` }}
            >
              {newArrivalsBlock.image?.includes('placeholder.com') && <span className="text-gray-500">New Arrivals</span>}
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4">
              <h3 className="text-sm font-medium uppercase tracking-wide">{newArrivalsBlock.title}</h3>
              <p className="text-xs text-gray-600 mt-1">{newArrivalsBlock.subtitle}</p>
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}
