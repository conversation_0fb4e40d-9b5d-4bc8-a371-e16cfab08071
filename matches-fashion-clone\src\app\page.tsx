﻿import { pageManager } from "../../lib/page-manager";
import DynamicPageRenderer from "@/components/DynamicPageRenderer";
import { notFound } from 'next/navigation';

export default async function Home() {
  try {
    // Try to get homepage from Strapi page management system
    const homepage = await pageManager.getHomepage();

    if (homepage) {
      // Use the new dynamic page system
      return <DynamicPageRenderer page={homepage} />;
    }

    // If no homepage found in page management, show fallback
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Welcome to Matches Fashion</h1>
          <p className="text-gray-600 mb-8">
            The Strapi page management system is not configured yet.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>To set up dynamic page management:</p>
            <p>1. Set up Strapi with the provided content types</p>
            <p>2. Create a homepage in Strapi with page_type: &quot;homepage&quot;</p>
            <p>3. See STRAPI-PAGE-MANAGEMENT-SETUP.md for details</p>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error loading homepage:", error);

    // Show fallback instead of not found
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Matches Fashion</h1>
          <p className="text-gray-600 mb-4">
            Unable to load page content. Please check your Strapi configuration.
          </p>
          <p className="text-sm text-gray-500">
            Error: {error instanceof Error ? error.message : 'Unknown error'}
          </p>
        </div>
      </div>
    );
  }
}
