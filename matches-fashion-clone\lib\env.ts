/**
 * Environment variable validation and configuration
 * This file ensures all required environment variables are present and valid
 */

// Define the shape of our environment configuration
interface EnvironmentConfig {
  // Required variables
  medusa: {
    backendUrl: string;
    publishableApiKey?: string;
  };
  strapi: {
    apiUrl: string;
    apiToken?: string;
  };
  app: {
    baseUrl: string;
    nodeEnv: 'development' | 'production' | 'test';
  };
  
  // Optional variables
  analytics?: {
    gaTrackingId?: string;
    gtmId?: string;
    facebookPixelId?: string;
  };
  errorTracking?: {
    sentryDsn?: string;
    sentryEnvironment?: string;
  };
  features: {
    chatWidget: boolean;
    wishlist: boolean;
    reviews: boolean;
    recommendations: boolean;
  };
  development: {
    analyze: boolean;
    verboseLogging: boolean;
  };
}

// Validation helper functions
const validateUrl = (url: string, name: string): string => {
  try {
    new URL(url);
    return url;
  } catch {
    throw new Error(`Invalid URL for ${name}: ${url}`);
  }
};

const validateEnum = <T extends string>(
  value: string,
  allowedValues: T[],
  name: string
): T => {
  if (allowedValues.includes(value as T)) {
    return value as T;
  }
  throw new Error(
    `Invalid value for ${name}: ${value}. Allowed values: ${allowedValues.join(', ')}`
  );
};

const parseBoolean = (value: string | undefined, defaultValue: boolean = false): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

// Get and validate environment variables
const getEnvVar = (name: string, required: boolean = false): string | undefined => {
  const value = process.env[name];
  
  if (required && !value) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  
  return value;
};

// Validate and create environment configuration
export const createEnvironmentConfig = (): EnvironmentConfig => {
  try {
    // Required variables
    const medusaBackendUrl = getEnvVar('NEXT_PUBLIC_MEDUSA_BACKEND_URL', true)!;
    const strapiApiUrl = getEnvVar('NEXT_PUBLIC_STRAPI_API_URL', true)!;
    const baseUrl = getEnvVar('NEXT_PUBLIC_BASE_URL', true)!;
    const nodeEnv = getEnvVar('NODE_ENV', true)!;

    // Optional variables
    const medusaPublishableApiKey = getEnvVar('NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY');
    const strapiApiToken = getEnvVar('STRAPI_API_TOKEN');
    
    // Analytics
    const gaTrackingId = getEnvVar('NEXT_PUBLIC_GA_TRACKING_ID');
    const gtmId = getEnvVar('NEXT_PUBLIC_GTM_ID');
    const facebookPixelId = getEnvVar('NEXT_PUBLIC_FACEBOOK_PIXEL_ID');
    
    // Error tracking
    const sentryDsn = getEnvVar('NEXT_PUBLIC_SENTRY_DSN');
    const sentryEnvironment = getEnvVar('SENTRY_ENVIRONMENT');
    
    // Feature flags
    const enableChatWidget = parseBoolean(getEnvVar('NEXT_PUBLIC_ENABLE_CHAT_WIDGET'), true);
    const enableWishlist = parseBoolean(getEnvVar('NEXT_PUBLIC_ENABLE_WISHLIST'), true);
    const enableReviews = parseBoolean(getEnvVar('NEXT_PUBLIC_ENABLE_REVIEWS'), true);
    const enableRecommendations = parseBoolean(getEnvVar('NEXT_PUBLIC_ENABLE_RECOMMENDATIONS'), true);
    
    // Development
    const analyze = parseBoolean(getEnvVar('ANALYZE'), false);
    const verboseLogging = parseBoolean(getEnvVar('VERBOSE_LOGGING'), false);

    // Validate URLs
    validateUrl(medusaBackendUrl, 'NEXT_PUBLIC_MEDUSA_BACKEND_URL');
    validateUrl(strapiApiUrl, 'NEXT_PUBLIC_STRAPI_API_URL');
    validateUrl(baseUrl, 'NEXT_PUBLIC_BASE_URL');

    // Validate node environment
    const validatedNodeEnv = validateEnum(
      nodeEnv,
      ['development', 'production', 'test'],
      'NODE_ENV'
    );

    // Create configuration object
    const config: EnvironmentConfig = {
      medusa: {
        backendUrl: medusaBackendUrl,
        publishableApiKey: medusaPublishableApiKey,
      },
      strapi: {
        apiUrl: strapiApiUrl,
        apiToken: strapiApiToken,
      },
      app: {
        baseUrl,
        nodeEnv: validatedNodeEnv,
      },
      features: {
        chatWidget: enableChatWidget,
        wishlist: enableWishlist,
        reviews: enableReviews,
        recommendations: enableRecommendations,
      },
      development: {
        analyze,
        verboseLogging,
      },
    };

    // Add optional configurations if present
    if (gaTrackingId || gtmId || facebookPixelId) {
      config.analytics = {
        gaTrackingId,
        gtmId,
        facebookPixelId,
      };
    }

    if (sentryDsn) {
      config.errorTracking = {
        sentryDsn,
        sentryEnvironment,
      };
    }

    return config;
  } catch (error) {
    console.error('Environment configuration error:', error);
    throw error;
  }
};

// Create and export the configuration
export const env = createEnvironmentConfig();

// Helper functions for common environment checks
export const isDevelopment = () => env.app.nodeEnv === 'development';
export const isProduction = () => env.app.nodeEnv === 'production';
export const isTest = () => env.app.nodeEnv === 'test';

// Validation function to check environment on app startup
export const validateEnvironment = (): void => {
  console.log('🔍 Validating environment configuration...');
  
  try {
    const config = createEnvironmentConfig();
    
    console.log('✅ Environment validation successful');
    console.log(`📍 Environment: ${config.app.nodeEnv}`);
    console.log(`🔗 Medusa Backend: ${config.medusa.backendUrl}`);
    console.log(`📝 Strapi CMS: ${config.strapi.apiUrl}`);
    console.log(`🌐 Base URL: ${config.app.baseUrl}`);
    
    if (config.development.verboseLogging) {
      console.log('🔧 Full configuration:', JSON.stringify(config, null, 2));
    }
    
    // Warn about missing optional configurations
    if (!config.medusa.publishableApiKey && isProduction()) {
      console.warn('⚠️  NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY is not set (recommended for production)');
    }
    
    if (!config.strapi.apiToken) {
      console.warn('⚠️  STRAPI_API_TOKEN is not set (may limit server-side functionality)');
    }
    
    if (!config.analytics && isProduction()) {
      console.warn('⚠️  No analytics configuration found (consider adding for production)');
    }
    
    if (!config.errorTracking && isProduction()) {
      console.warn('⚠️  No error tracking configuration found (recommended for production)');
    }
    
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    
    if (isProduction()) {
      // In production, fail fast if environment is invalid
      process.exit(1);
    } else {
      // In development, show helpful error message
      console.error('\n📋 Please check your .env.local file and ensure all required variables are set.');
      console.error('📄 See .env.example for a complete list of available variables.\n');
      throw error;
    }
  }
};

// Export individual environment variables for convenience
export const {
  medusa: medusaConfig,
  strapi: strapiConfig,
  app: appConfig,
  analytics: analyticsConfig,
  errorTracking: errorTrackingConfig,
  features: featureFlags,
  development: developmentConfig,
} = env;
