(()=>{var e={};e.id=946,e.ids=[946],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7956:(e,s,t)=>{Promise.resolve().then(t.bind(t,80996))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26808:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>h});var r=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(s,l);let h={children:["",{children:["womens",{children:["shop",{children:["clothing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94610)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/womens/shop/clothing/page",pathname:"/womens/shop/clothing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55220:(e,s,t)=>{Promise.resolve().then(t.bind(t,94610))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80996:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(60687),a=t(55498);function o(){let e=["XS","S","M","L","XL"],s=["Black","White","Blue","Red","Green"],t=Array.from({length:24},(t,r)=>{let a=s.slice(0,2).map((s,t)=>({color:s,images:[`https://picsum.photos/seed/womens-${r}-${t}-1/600/800`,`https://picsum.photos/seed/womens-${r}-${t}-2/600/800`,`https://picsum.photos/seed/womens-${r}-${t}-3/600/800`],sizes:e.filter(()=>Math.random()>.3)}));return{id:r+1,name:`Designer Item ${r+1}`,brand:["Gucci","Saint Laurent","Bottega Veneta","The Row","Khaite"][r%5],price:Math.floor(2e3*Math.random())+200,originalPrice:Math.floor(2e3*Math.random())+200,isOnSale:Math.random()>.7,category:["Dresses","Coats","Tops","Trousers","Knitwear"][r%5],href:`/womens/product/${r+1}`,variants:a}});return(0,r.jsx)(a.A,{title:"Women's Clothing",categories:["Shop all","Activewear","Beachwear","Bridal","Cardigans","Coats","Denim","Dresses","Jackets","Jeans","Jumpsuits","Knitwear","Lingerie and nightwear","Loungewear","Matching sets","Skirts","Suits","Swimwear","Tops","Trousers"],designers:["ALA\xcfA","Alexander McQueen","Balenciaga","Bottega Veneta","Dolce & Gabbana","Erdem","Gabriela Hearst","Gucci","Isabel Marant","Jacquemus","Khaite","LOEWE","Max Mara","Moncler","Saint Laurent","The Row","Toteme","Valentino Garavani","Zimmermann"],initialProducts:t})}},94610:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\womens\\\\shop\\\\clothing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\womens\\shop\\clothing\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,20,464,137,498],()=>t(26808));module.exports=r})();