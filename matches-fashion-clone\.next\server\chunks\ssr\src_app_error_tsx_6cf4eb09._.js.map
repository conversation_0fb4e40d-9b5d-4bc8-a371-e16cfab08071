{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/error.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';\n\ninterface ErrorPageProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\nexport default function ErrorPage({ error, reset }: ErrorPageProps) {\n  React.useEffect(() => {\n    // Log the error to console and external service\n    console.error('Route error:', error);\n    \n    if (process.env.NODE_ENV === 'production') {\n      // TODO: Log to external error tracking service\n      logErrorToService(error);\n    }\n  }, [error]);\n\n  const logErrorToService = (error: Error & { digest?: string }) => {\n    // Placeholder for error tracking service integration\n    console.log('Logging route error to external service:', {\n      message: error.message,\n      stack: error.stack,\n      digest: error.digest,\n      timestamp: new Date().toISOString(),\n      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',\n      url: typeof window !== 'undefined' ? window.location.href : 'unknown',\n    });\n  };\n\n  const handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  const handleGoBack = () => {\n    window.history.back();\n  };\n\n  const handleReload = () => {\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8 text-center\">\n        <div>\n          <AlertTriangle className=\"mx-auto h-16 w-16 text-red-500\" />\n          <h1 className=\"mt-6 text-3xl font-bold text-gray-900\">\n            Oops! Something went wrong\n          </h1>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            We encountered an unexpected error while loading this page.\n          </p>\n          \n          {process.env.NODE_ENV === 'development' && (\n            <details className=\"mt-4 text-left\">\n              <summary className=\"cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900\">\n                Error Details (Development Only)\n              </summary>\n              <div className=\"mt-2 p-4 bg-red-50 border border-red-200 rounded-md\">\n                <p className=\"text-sm font-mono text-red-800 break-all\">\n                  {error.message}\n                </p>\n                {error.stack && (\n                  <pre className=\"mt-2 text-xs text-red-700 overflow-auto max-h-40\">\n                    {error.stack}\n                  </pre>\n                )}\n                {error.digest && (\n                  <p className=\"mt-2 text-xs text-red-600\">\n                    Digest: {error.digest}\n                  </p>\n                )}\n              </div>\n            </details>\n          )}\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n            <button\n              onClick={reset}\n              className=\"inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n            >\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              Try Again\n            </button>\n            \n            <button\n              onClick={handleReload}\n              className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n            >\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              Reload Page\n            </button>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n            <button\n              onClick={handleGoBack}\n              className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Go Back\n            </button>\n            \n            <button\n              onClick={handleGoHome}\n              className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors\"\n            >\n              <Home className=\"w-4 h-4 mr-2\" />\n              Go Home\n            </button>\n          </div>\n        </div>\n\n        <div className=\"text-xs text-gray-500\">\n          <p>If this problem persists, please contact our support team.</p>\n          <p className=\"mt-1\">\n            Error ID: {Date.now().toString(36).toUpperCase()}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAUe,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAkB;IAChE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,gDAAgD;QAChD,QAAQ,KAAK,CAAC,gBAAgB;QAE9B,uCAA2C;;QAG3C;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,oBAAoB,CAAC;QACzB,qDAAqD;QACrD,QAAQ,GAAG,CAAC,4CAA4C;YACtD,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,QAAQ,MAAM,MAAM;YACpB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,OAAO,cAAc,cAAc,UAAU,SAAS,GAAG;YACpE,KAAK,6EAAuD;QAC9D;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,eAAe;QACnB,OAAO,OAAO,CAAC,IAAI;IACrB;IAEA,MAAM,eAAe;QACnB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;wBAIzC,oDAAyB,+BACxB,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAAuE;;;;;;8CAG1F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,MAAM,OAAO;;;;;;wCAEf,MAAM,KAAK,kBACV,8OAAC;4CAAI,WAAU;sDACZ,MAAM,KAAK;;;;;;wCAGf,MAAM,MAAM,kBACX,8OAAC;4CAAE,WAAU;;gDAA4B;gDAC9B,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAE,WAAU;;gCAAO;gCACP,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAM1D", "debugId": null}}]}