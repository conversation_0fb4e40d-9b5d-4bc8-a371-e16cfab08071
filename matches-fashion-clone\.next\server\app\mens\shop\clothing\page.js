(()=>{var e={};e.id=212,e.ids=[212],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13652:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>h});var t=r(65239),o=r(48088),a=r(88170),n=r.n(a),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let h={children:["",{children:["mens",{children:["shop",{children:["clothing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26720)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/mens/shop/clothing/page",pathname:"/mens/shop/clothing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26720:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\mens\\\\shop\\\\clothing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\mens\\shop\\clothing\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},58570:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(60687),o=r(55498);function a(){let e=["XS","S","M","L","XL"],s=["Black","White","Blue","Red","Green"],r=Array.from({length:24},(r,t)=>{let o=s.slice(0,2).map((s,r)=>({color:s,images:[`https://picsum.photos/seed/mens-${t}-${r}-1/600/800`,`https://picsum.photos/seed/mens-${t}-${r}-2/600/800`,`https://picsum.photos/seed/mens-${t}-${r}-3/600/800`],sizes:e.filter(()=>Math.random()>.3)}));return{id:t+1,name:`Designer Item ${t+1}`,brand:["Tom Ford","Brunello Cucinelli","Stone Island","Thom Browne","Bottega Veneta"][t%5],price:Math.floor(3e3*Math.random())+300,originalPrice:Math.floor(3e3*Math.random())+300,isOnSale:Math.random()>.7,category:["Suits","Blazers","Shirts","Trousers","Knitwear"][t%5],href:`/mens/product/${t+1}`,variants:o}});return(0,t.jsx)(o.A,{title:"Men's Clothing",categories:["Shop all","Activewear","Blazers","Coats","Denim","Jackets","Jeans","Knitwear","Loungewear","Polo shirts","Shirts","Shorts","Suits","Swimwear","T-shirts","Trousers","Underwear & nightwear"],designers:["Brunello Cucinelli","Bottega Veneta","Gucci","Saint Laurent","Stone Island","Thom Browne","Tom Ford","Valentino","Balenciaga","Prada","Ermenegildo Zegna","Loro Piana","Kiton","Brioni","Canali"],initialProducts:r})}},58714:(e,s,r)=>{Promise.resolve().then(r.bind(r,26720))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70613:(e,s,r)=>{Promise.resolve().then(r.bind(r,58570))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,20,464,137,498],()=>r(13652));module.exports=t})();