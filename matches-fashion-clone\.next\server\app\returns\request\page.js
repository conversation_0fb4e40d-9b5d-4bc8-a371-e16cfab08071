(()=>{var e={};e.id=244,e.ids=[244],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18438:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>d});var t=r(65239),a=r(48088),n=r(88170),o=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(s,i);let d={children:["",{children:["returns",{children:["request",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43714)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\request\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\returns\\request\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/returns/request/page",pathname:"/returns/request",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27835:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,93283))},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43714:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(37413),a=r(4536),n=r.n(a),o=r(93283);function l(){return(0,t.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,t.jsxs)("nav",{className:"text-sm text-gray-500",children:[(0,t.jsx)(n(),{href:"/returns",className:"hover:text-black",children:"Returns"}),(0,t.jsx)("span",{className:"mx-2",children:"/"}),(0,t.jsx)("span",{className:"text-black",children:"Request a Return"})]})}),(0,t.jsxs)("div",{className:"max-w-xl mx-auto px-4 py-12",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6 text-center",children:"Request a Return"}),(0,t.jsx)(o.default,{})]})]})}},60929:(e,s,r)=>{"use strict";r.d(s,{default:()=>n});var t=r(60687),a=r(43210);function n(){let[e,s]=(0,a.useState)({orderId:"",email:"",reason:"",details:""}),[r,n]=(0,a.useState)(!1),o=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))};return r?(0,t.jsxs)("div",{className:"p-6 bg-gray-50 text-center space-y-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Return request submitted"}),(0,t.jsx)("p",{children:"We'll email you with next steps shortly."})]}):(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n(!0)},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Order Number"}),(0,t.jsx)("input",{required:!0,name:"orderId",value:e.orderId,onChange:o,className:"w-full border border-gray-300 p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Email"}),(0,t.jsx)("input",{required:!0,type:"email",name:"email",value:e.email,onChange:o,className:"w-full border border-gray-300 p-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Reason for Return"}),(0,t.jsxs)("select",{name:"reason",value:e.reason,onChange:o,className:"w-full border border-gray-300 p-2",children:[(0,t.jsx)("option",{value:"",children:"Select..."}),(0,t.jsx)("option",{children:"Too small"}),(0,t.jsx)("option",{children:"Too large"}),(0,t.jsx)("option",{children:"Not as described"}),(0,t.jsx)("option",{children:"Ordered by mistake"}),(0,t.jsx)("option",{children:"Other"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Additional Details (optional)"}),(0,t.jsx)("textarea",{name:"details",value:e.details,onChange:o,className:"w-full border border-gray-300 p-2",rows:4})]}),(0,t.jsx)("button",{type:"submit",className:"luxury-button w-full",children:"Submit Request"})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64627:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,60929))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},93283:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\components\\\\ReturnRequestForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\components\\ReturnRequestForm.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,20,137],()=>r(18438));module.exports=t})();