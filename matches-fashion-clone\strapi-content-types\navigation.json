{"kind": "singleType", "collectionName": "navigations", "info": {"singularName": "navigation", "pluralName": "navigations", "displayName": "Navigation", "description": "Site navigation structure"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"main_menu": {"type": "component", "repeatable": true, "component": "navigation.menu-item"}, "footer_menu": {"type": "component", "repeatable": true, "component": "navigation.menu-item"}, "mobile_menu": {"type": "component", "repeatable": true, "component": "navigation.menu-item"}, "breadcrumb_settings": {"type": "json"}}}