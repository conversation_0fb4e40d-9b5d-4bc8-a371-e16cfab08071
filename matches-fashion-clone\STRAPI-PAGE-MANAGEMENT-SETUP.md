# Strapi Page Management System Setup

This guide shows you how to set up Strapi to manage all your frontend pages, just like WordPress manages WooCommerce storefronts.

## 🎯 **What This System Provides**

✅ **Complete Page Management**: Create, edit, and manage all frontend pages from Strapi
✅ **Dynamic Content Blocks**: Drag-and-drop content builder like WordPress page builders
✅ **SEO Management**: Meta titles, descriptions, and structured data
✅ **Navigation Management**: Dynamic menus and breadcrumbs
✅ **Template System**: Different page layouts and templates
✅ **E-commerce Integration**: Link pages to Medusa categories and collections
✅ **Hierarchical Pages**: Parent-child page relationships
✅ **URL Management**: Custom slugs and URL structures

## 🏗️ **Strapi Setup Steps**

### **1. Install Strapi Content Types**

Copy the content type files to your Strapi project:

```bash
# In your Strapi project directory
mkdir -p src/api/page/content-types/page
mkdir -p src/api/navigation/content-types/navigation
mkdir -p src/components/navigation
mkdir -p src/components/blocks

# Copy the content type files
cp matches-fashion-clone/strapi-content-types/page.json src/api/page/content-types/page/schema.json
cp matches-fashion-clone/strapi-content-types/navigation.json src/api/navigation/content-types/navigation/schema.json
cp matches-fashion-clone/strapi-components/navigation/menu-item.json src/components/navigation/menu-item.json
```

### **2. Create Content Block Components**

Create these component files in your Strapi project:

**`src/components/blocks/hero-section.json`**:
```json
{
  "collectionName": "components_blocks_hero_sections",
  "info": {
    "displayName": "Hero Section",
    "description": "Large banner section with background image"
  },
  "attributes": {
    "title": { "type": "string", "required": true },
    "subtitle": { "type": "string" },
    "description": { "type": "text" },
    "background_image": {
      "type": "media",
      "multiple": false,
      "allowedTypes": ["images"]
    },
    "cta_text": { "type": "string" },
    "cta_link": { "type": "string" },
    "overlay_opacity": { "type": "integer", "default": 40, "min": 0, "max": 100 },
    "text_color": {
      "type": "enumeration",
      "enum": ["white", "black"],
      "default": "white"
    },
    "text_alignment": {
      "type": "enumeration",
      "enum": ["left", "center", "right"],
      "default": "center"
    }
  }
}
```

**`src/components/blocks/content-block.json`**:
```json
{
  "collectionName": "components_blocks_content_blocks",
  "info": {
    "displayName": "Content Block",
    "description": "Flexible content block with text and images"
  },
  "attributes": {
    "title": { "type": "string" },
    "content": { "type": "richtext" },
    "image": {
      "type": "media",
      "multiple": false,
      "allowedTypes": ["images"]
    },
    "cta_text": { "type": "string" },
    "cta_link": { "type": "string" },
    "layout": {
      "type": "enumeration",
      "enum": ["text-only", "image-left", "image-right", "image-top"],
      "default": "text-only"
    },
    "background_color": { "type": "string" },
    "text_color": { "type": "string" }
  }
}
```

### **3. Restart Strapi**

After adding the content types and components:

```bash
# In your Strapi project
npm run develop
```

### **4. Configure Permissions**

In Strapi Admin:
1. Go to **Settings > Users & Permissions > Roles**
2. Edit **Public** role
3. Enable permissions for:
   - **Page**: `find`, `findOne`
   - **Navigation**: `find`

## 🎨 **Creating Your First Page**

### **1. Create Homepage**

1. Go to **Content Manager > Page**
2. Click **Create new entry**
3. Fill in:
   - **Title**: "Homepage"
   - **Slug**: "home" (auto-generated)
   - **Page Type**: "homepage"
   - **Template**: "homepage"
   - **Status**: "published"

4. Add content blocks:
   - **Hero Section**: Main banner
   - **Product Grid**: Featured products
   - **Content Block**: About section

### **2. Create Category Pages**

1. Create a new page:
   - **Title**: "Women's Clothing"
   - **Slug**: "womens/clothing"
   - **Page Type**: "category_index"
   - **Template**: "category-grid"
   - **Medusa Category ID**: "cat_123..." (from your Medusa backend)

### **3. Set Up Navigation**

1. Go to **Content Manager > Navigation**
2. Add main menu items:
   - **Label**: "Women", **URL**: "/womens"
   - **Label**: "Men", **URL**: "/mens"
   - **Label**: "Sale", **URL**: "/sale"

## 🔧 **Frontend Integration**

The frontend is already set up to use this system! Here's how it works:

### **Dynamic Routing**

- **`/[...slug]/page.tsx`**: Handles all dynamic pages
- **`/page.tsx`**: Updated to use Strapi homepage

### **Page Rendering**

1. **URL requested** (e.g., `/womens/clothing`)
2. **Strapi queried** for page with slug `womens/clothing`
3. **Page data retrieved** with content blocks
4. **Dynamic renderer** builds the page
5. **Content blocks** rendered based on type

### **Content Blocks**

Each content block type has its own React component:
- `HeroSection.tsx` - Hero banners
- `ContentBlock.tsx` - Text and image content
- `ProductGrid.tsx` - Product listings
- `CategoryShowcase.tsx` - Category displays

## 📝 **Content Management Workflow**

### **For Content Managers**

1. **Create Pages**: Use Strapi admin to create new pages
2. **Add Content**: Drag and drop content blocks
3. **Configure SEO**: Set meta titles and descriptions
4. **Manage Navigation**: Update menus and links
5. **Preview**: Use Strapi preview features
6. **Publish**: Make pages live

### **For Developers**

1. **Add Block Types**: Create new content block components
2. **Customize Templates**: Modify page templates
3. **Extend Functionality**: Add new page types
4. **Integrate APIs**: Connect to Medusa for product data

## 🚀 **Advanced Features**

### **E-commerce Integration**

Link pages to Medusa:
```javascript
// In your page data
{
  "medusa_category_id": "cat_123...",
  "medusa_collection_id": "pcol_456..."
}
```

### **SEO Optimization**

Automatic meta tag generation:
```javascript
// Generated automatically
<title>Page Title - Matches Fashion</title>
<meta name="description" content="Page description" />
<meta property="og:title" content="Page Title" />
```

### **Custom Fields**

Add custom data to pages:
```json
{
  "custom_fields": {
    "featured_designer": "The Row",
    "season": "Spring 2025",
    "special_offers": ["free_shipping", "gift_wrap"]
  }
}
```

## 🔄 **Migration from Static Pages**

### **Step 1**: Create Strapi Pages
- Convert existing static pages to Strapi pages
- Recreate content using content blocks

### **Step 2**: Update Routes
- Remove static page files
- Let dynamic routing handle all pages

### **Step 3**: Test and Optimize
- Test all page functionality
- Optimize content block performance

## 📊 **Benefits Over Static Pages**

| Feature | Static Pages | Strapi Pages |
|---------|-------------|--------------|
| Content Updates | Code deployment required | Instant via admin |
| SEO Management | Manual in code | GUI interface |
| Navigation | Hardcoded | Dynamic |
| Content Blocks | Fixed layouts | Flexible builder |
| Multi-language | Complex setup | Built-in support |
| User Permissions | Developer only | Role-based access |
| Preview | Local development | Live preview |
| Versioning | Git only | Built-in drafts |

## 🎯 **Next Steps**

1. **Set up Strapi** with the provided content types
2. **Create your first pages** using the admin interface
3. **Test the dynamic routing** on your frontend
4. **Add more content block types** as needed
5. **Train content managers** on the new system

This system gives you the same level of control as WordPress with WooCommerce, but with the performance and flexibility of a headless architecture! 🚀
