(()=>{var e={};e.id=238,e.ids=[238],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,r)=>{"use strict";var t=r(65773);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27634:(e,s,r)=>{Promise.resolve().then(r.bind(r,40064))},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40064:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l,dynamic:()=>n});var t=r(60687),a=r(16189),i=r(43210);let n="force-dynamic";function o(){(0,a.useSearchParams)();let[e,s]=(0,i.useState)(null),[r,n]=(0,i.useState)(!1);return r?(0,t.jsx)("p",{className:"p-10",children:"Invalid share link."}):e?(0,t.jsxs)("div",{className:"max-w-xl mx-auto py-10 space-y-4",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:e.name}),e.note&&(0,t.jsx)("p",{className:"italic",children:e.note}),0===e.items.length?(0,t.jsx)("p",{children:"No items."}):(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1",children:e.items.map((e,s)=>(0,t.jsx)("li",{children:e.title},s))})]}):(0,t.jsx)("p",{className:"p-10",children:"Loading..."})}function l(){return(0,t.jsx)(i.Suspense,{fallback:(0,t.jsx)("p",{className:"p-10",children:"Loading..."}),children:(0,t.jsx)(o,{})})}},56472:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>u,tree:()=>c});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let c={children:["",{children:["wishlist-share",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74570)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,60520)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,h=["C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/wishlist-share/page",pathname:"/wishlist-share",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64586:(e,s,r)=>{Promise.resolve().then(r.bind(r,74570))},74075:e=>{"use strict";e.exports=require("zlib")},74570:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,dynamic:()=>a});var t=r(12907);let a=(0,t.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx","dynamic"),i=(0,t.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\Github\\\\Pull1106\\\\matches-fashion-clone\\\\src\\\\app\\\\wishlist-share\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\matches-headless-ui\\Github\\Pull1106\\matches-fashion-clone\\src\\app\\wishlist-share\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,20,137],()=>r(56472));module.exports=t})();