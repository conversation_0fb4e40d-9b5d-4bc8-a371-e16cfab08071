1:"$Sreact.fragment"
2:I[5080,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
3:I[9576,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"AuthProvider"]
4:I[4303,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"AccountProvider"]
5:I[8088,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"CartProvider"]
6:I[3503,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
7:I[5793,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
8:I[7555,[],""]
9:I[1901,["39","static/chunks/app/error-d022555a792f05a9.js"],"default"]
a:I[1295,[],""]
b:I[6874,["244","static/chunks/app/returns/request/page-72e4d48b6071fcf1.js"],""]
c:I[6821,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
d:I[9634,["177","static/chunks/app/layout-ed1ca4bb201b52e4.js"],"default"]
e:I[163,["244","static/chunks/app/returns/request/page-72e4d48b6071fcf1.js"],"default"]
f:I[9665,[],"MetadataBoundary"]
11:I[9665,[],"OutletBoundary"]
14:I[4911,[],"AsyncMetadataOutlet"]
16:I[9665,[],"ViewportBoundary"]
18:I[6614,[],""]
:HL["/_next/static/css/5a62bbe44e5ae311.css","style"]
0:{"P":null,"b":"DMw7lI2my4mO6UPvB8OZe","p":"","c":["","returns","request"],"i":false,"f":[[["",{"children":["returns",{"children":["request",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/5a62bbe44e5ae311.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"min-h-screen flex flex-col antialiased overflow-x-hidden","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"children":["$","$L5",null,{"children":[["$","$L6",null,{}],["$","$L7",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L2",null,{"children":["$","$L8",null,{"parallelRouterKey":"children","error":"$9","errorStyles":[],"errorScripts":[],"template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-white","children":["$","div",null,{"className":"text-center max-w-md mx-auto px-4","children":[["$","h1",null,{"className":"text-6xl font-bold mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}],["$","div",null,{"className":"space-y-4","children":[["$","$Lb",null,{"href":"/","className":"luxury-button block","children":"Return Home"}],["$","div",null,{"className":"flex justify-center space-x-4 text-sm","children":[["$","$Lb",null,{"href":"/womens","className":"text-gray-600 hover:text-black","children":"Shop Women's"}],["$","$Lb",null,{"href":"/mens","className":"text-gray-600 hover:text-black","children":"Shop Men's"}]]}]]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}],["$","$Lc",null,{}],["$","$Ld",null,{}]]}]}]}]}]}]}]]}],{"children":["returns",["$","$1","c",{"children":[null,["$","$L8",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["request",["$","$1","c",{"children":[null,["$","$L8",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$La",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-white","children":[["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-4","children":["$","nav",null,{"className":"text-sm text-gray-500","children":[["$","$Lb",null,{"href":"/returns","className":"hover:text-black","children":"Returns"}],["$","span",null,{"className":"mx-2","children":"/"}],["$","span",null,{"className":"text-black","children":"Request a Return"}]]}]}],["$","div",null,{"className":"max-w-xl mx-auto px-4 py-12","children":[["$","h1",null,{"className":"text-3xl font-bold mb-6 text-center","children":"Request a Return"}],["$","$Le",null,{}]]}]]}],["$","$Lf",null,{"children":"$L10"}],null,["$","$L11",null,{"children":["$L12","$L13",["$","$L14",null,{"promise":"$@15"}]]}]]}],{},null,false]},null,false]},null,false]},[["$","div","l",{"className":"bg-white","children":[["$","div",null,{"className":"relative mb-8","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[4/3] w-full h-96","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"absolute inset-0 flex items-center justify-center","children":["$","div",null,{"className":"text-center space-y-4","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"300px","height":"48px"},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"200px","height":"24px"},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"120px","height":"40px"},"aria-hidden":"true","children":"$undefined"}]]}]}]]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-8","children":[["$","div",null,{"className":"mb-6","children":[["$","div",null,{"className":"h-6 bg-gray-200 animate-pulse rounded w-32 mb-2"}],["$","div",null,{"className":"h-8 bg-gray-200 animate-pulse rounded w-48"}]]}],["$","div",null,{"className":"flex space-x-4 overflow-hidden mb-12","children":[["$","div","0",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","1",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","2",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}],["$","div","3",{"className":"flex-shrink-0 w-64","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"bg-gray-200 rounded animate-pulse aspect-[3/4] w-full","style":{},"aria-hidden":"true","children":"$undefined"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","div","0",{"className":"bg-gray-200 rounded-sm animate-pulse","style":{"width":"75%","height":"16px"},"aria-hidden":"true","children":"$undefined"}]]}],["$","div",null,{"className":"bg-gray-200 rounded animate-pulse","style":{"width":"60%","height":"20px"},"aria-hidden":"true","children":"$undefined"}]]}]]}]}]]}]]}],["$","div",null,{"className":"max-w-7xl mx-auto px-4 py-8","children":[["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4","children":[["$","div","0",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}],["$","div","1",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}],["$","div","2",{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[3/4] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-2/3"}]]}]]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-4","children":[["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"aspect-[4/5] bg-gray-200 animate-pulse rounded"}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"h-4 bg-gray-200 animate-pulse rounded w-3/4"}],["$","div",null,{"className":"h-3 bg-gray-200 animate-pulse rounded w-1/2"}]]}]]}]]}]]}]]}],[],[]],false],["$","$1","h",{"children":[null,["$","$1","IZi-41jYSykzDVzs9eDRz",{"children":[["$","$L16",null,{"children":"$L17"}],null]}],null]}],false]],"m":"$undefined","G":["$18","$undefined"],"s":false,"S":true}
19:"$Sreact.suspense"
1a:I[4911,[],"AsyncMetadata"]
10:["$","$19",null,{"fallback":null,"children":["$","$L1a",null,{"promise":"$@1b"}]}]
13:null
17:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
12:null
1b:{"metadata":[["$","title","0",{"children":"Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK"}],["$","meta","1",{"name":"description","content":"Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands."}],["$","meta","2",{"name":"keywords","content":"luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
15:{"metadata":"$1b:metadata","error":null,"digest":"$undefined"}
