{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ProductCarousel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductCarousel.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCarousel.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/components/ProductCarousel.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProductCarousel.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCarousel.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/lib/medusa.js"], "sourcesContent": ["import Medusa from \"@medusajs/medusa-js\";\r\n\r\nconst MEDUSA_BACKEND_URL = process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL || \"http://localhost:9000\";\r\nconst PUBLISHABLE_API_KEY = process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_API_KEY;\r\n\r\nconst customHeaders = {};\r\nif (PUBLISHABLE_API_KEY) {\r\n  customHeaders['x-publishable-api-key'] = PUBLISHABLE_API_KEY;\r\n}\r\n\r\nexport const medusaClient = new Medusa({\r\n  baseUrl: MEDUSA_BACKEND_URL,\r\n  maxRetries: 3,\r\n  customHeaders: customHeaders\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,6DAA8C;AACzE,MAAM;AAEN,MAAM,gBAAgB,CAAC;AACvB,wCAAyB;IACvB,aAAa,CAAC,wBAAwB,GAAG;AAC3C;AAEO,MAAM,eAAe,IAAI,4JAAA,CAAA,UAAM,CAAC;IACrC,SAAS;IACT,YAAY;IACZ,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/lib/strapi.js"], "sourcesContent": ["// Use environment variables directly to avoid import issues\r\nconst STRAPI_API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || \"http://localhost:1337\";\r\nconst STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;\r\nconst isDevelopment = () => process.env.NODE_ENV === 'development';\r\n\r\n// Environment variables are already defined above\r\n\r\n// Enhanced error class for Strapi API errors\r\nexport class StrapiApiError extends Error {\r\n  constructor(message, status, statusText, url, responseBody, code) {\r\n    super(message);\r\n    this.name = 'StrapiApiError';\r\n    this.status = status;\r\n    this.statusText = statusText;\r\n    this.url = url;\r\n    this.responseBody = responseBody;\r\n    this.code = code;\r\n  }\r\n}\r\n\r\n// Retry configuration\r\nconst RETRY_CONFIG = {\r\n  maxRetries: 3,\r\n  retryDelay: 1000, // 1 second\r\n  retryableStatuses: [408, 429, 500, 502, 503, 504],\r\n};\r\n\r\n// Helper function to determine if an error is retryable\r\nconst isRetryableError = (status) => {\r\n  return RETRY_CONFIG.retryableStatuses.includes(status);\r\n};\r\n\r\n// Helper function to wait for a specified delay\r\nconst delay = (ms) => {\r\n  return new Promise(resolve => setTimeout(resolve, ms));\r\n};\r\n\r\n// Enhanced fetch function with retry logic and better error handling\r\nexport async function fetchStrapiAPI(path, params = {}, options = {}) {\r\n  const headers = {\r\n    'Content-Type': 'application/json',\r\n    ...options.headers,\r\n  };\r\n\r\n  if (STRAPI_API_TOKEN) {\r\n    headers['Authorization'] = `Bearer ${STRAPI_API_TOKEN}`;\r\n  }\r\n\r\n  const mergedOptions = {\r\n    ...options,\r\n    headers,\r\n  };\r\n\r\n  const requestPath = path.startsWith('/') ? path : `/${path}`;\r\n  const queryString = new URLSearchParams(params).toString();\r\n  const requestUrl = `${STRAPI_API_URL}/api${requestPath}${queryString ? `?${queryString}` : ''}`;\r\n\r\n  let lastError = null;\r\n\r\n  // Retry logic\r\n  for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {\r\n    try {\r\n      if (isDevelopment() && attempt > 0) {\r\n        console.log(`🔄 Retrying Strapi API request (attempt ${attempt + 1}/${RETRY_CONFIG.maxRetries + 1}): ${requestUrl}`);\r\n      }\r\n\r\n      const response = await fetch(requestUrl, mergedOptions);\r\n\r\n      if (!response.ok) {\r\n        const errorBody = await response.text();\r\n        const error = new StrapiApiError(\r\n          `Strapi API request failed: ${response.status} ${response.statusText}`,\r\n          response.status,\r\n          response.statusText,\r\n          requestUrl,\r\n          errorBody\r\n        );\r\n\r\n        // Log error details\r\n        console.error(\"Strapi API Error:\", {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          url: requestUrl,\r\n          body: errorBody,\r\n          attempt: attempt + 1,\r\n        });\r\n\r\n        // Check if we should retry\r\n        if (attempt < RETRY_CONFIG.maxRetries && isRetryableError(response.status)) {\r\n          lastError = error;\r\n          await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt)); // Exponential backoff\r\n          continue;\r\n        }\r\n\r\n        // Return structured error response\r\n        return {\r\n          data: undefined,\r\n          error: {\r\n            message: getUserFriendlyErrorMessage(response.status),\r\n            code: `STRAPI_${response.status}`,\r\n            status: response.status,\r\n            details: {\r\n              originalMessage: error.message,\r\n              url: requestUrl,\r\n              body: errorBody,\r\n            },\r\n          },\r\n          success: false,\r\n        };\r\n      }\r\n\r\n      // Success - parse and return data\r\n      const data = await response.json();\r\n\r\n      if (isDevelopment()) {\r\n        console.log(`✅ Strapi API success: ${requestUrl}`);\r\n      }\r\n\r\n      return {\r\n        data,\r\n        success: true,\r\n      };\r\n\r\n    } catch (error) {\r\n      const networkError = error;\r\n      lastError = networkError;\r\n\r\n      console.error(\"Network error fetching from Strapi API:\", {\r\n        error: networkError.message,\r\n        url: requestUrl,\r\n        attempt: attempt + 1,\r\n      });\r\n\r\n      // Retry on network errors\r\n      if (attempt < RETRY_CONFIG.maxRetries) {\r\n        await delay(RETRY_CONFIG.retryDelay * Math.pow(2, attempt));\r\n        continue;\r\n      }\r\n\r\n      // Return structured error response for network errors\r\n      return {\r\n        data: undefined,\r\n        error: {\r\n          message: \"Unable to connect to the content management system. Please check your internet connection and try again.\",\r\n          code: 'STRAPI_NETWORK_ERROR',\r\n          status: 0,\r\n          details: {\r\n            originalMessage: networkError.message,\r\n            url: requestUrl,\r\n          },\r\n        },\r\n        success: false,\r\n      };\r\n    }\r\n  }\r\n\r\n  // This should never be reached, but just in case\r\n  return {\r\n    data: undefined,\r\n    error: {\r\n      message: \"An unexpected error occurred while fetching data.\",\r\n      code: 'STRAPI_UNKNOWN_ERROR',\r\n      status: 0,\r\n      details: {\r\n        originalMessage: lastError?.message || 'Unknown error',\r\n        url: requestUrl,\r\n      },\r\n    },\r\n    success: false,\r\n  };\r\n}\r\n\r\n// Helper function to provide user-friendly error messages\r\nconst getUserFriendlyErrorMessage = (status) => {\r\n  switch (status) {\r\n    case 400:\r\n      return \"The request was invalid. Please try again.\";\r\n    case 401:\r\n      return \"Authentication failed. Please refresh the page and try again.\";\r\n    case 403:\r\n      return \"Access denied. You don't have permission to access this content.\";\r\n    case 404:\r\n      return \"The requested content was not found.\";\r\n    case 408:\r\n      return \"The request timed out. Please try again.\";\r\n    case 429:\r\n      return \"Too many requests. Please wait a moment and try again.\";\r\n    case 500:\r\n      return \"A server error occurred. Please try again later.\";\r\n    case 502:\r\n      return \"The content management system is temporarily unavailable. Please try again later.\";\r\n    case 503:\r\n      return \"The service is temporarily unavailable. Please try again later.\";\r\n    case 504:\r\n      return \"The request timed out. Please try again.\";\r\n    default:\r\n      return \"An unexpected error occurred. Please try again later.\";\r\n  }\r\n};\r\n\r\n// Legacy function for backward compatibility\r\nexport async function fetchStrapiAPILegacy(path, params = {}, options = {}) {\r\n  const result = await fetchStrapiAPI(path, params, options);\r\n\r\n  if (!result.success) {\r\n    throw new Error(result.error?.message || 'Strapi API request failed');\r\n  }\r\n\r\n  return result.data;\r\n}\r\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;AAC5D,MAAM,iBAAiB,6DAA0C;AACjE,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB;AACrD,MAAM,gBAAgB,IAAM,oDAAyB;AAK9C,MAAM,uBAAuB;IAClC,YAAY,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,CAAE;QAChE,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,sBAAsB;AACtB,MAAM,eAAe;IACnB,YAAY;IACZ,YAAY;IACZ,mBAAmB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;AACnD;AAEA,wDAAwD;AACxD,MAAM,mBAAmB,CAAC;IACxB,OAAO,aAAa,iBAAiB,CAAC,QAAQ,CAAC;AACjD;AAEA,gDAAgD;AAChD,MAAM,QAAQ,CAAC;IACb,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,eAAe,eAAe,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IAClE,MAAM,UAAU;QACd,gBAAgB;QAChB,GAAG,QAAQ,OAAO;IACpB;IAEA,IAAI,kBAAkB;QACpB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,kBAAkB;IACzD;IAEA,MAAM,gBAAgB;QACpB,GAAG,OAAO;QACV;IACF;IAEA,MAAM,cAAc,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC5D,MAAM,cAAc,IAAI,gBAAgB,QAAQ,QAAQ;IACxD,MAAM,aAAa,GAAG,eAAe,IAAI,EAAE,cAAc,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE/F,IAAI,YAAY;IAEhB,cAAc;IACd,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAU,EAAE,UAAW;QACnE,IAAI;YACF,IAAI,mBAAmB,UAAU,GAAG;gBAClC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU,EAAE,CAAC,EAAE,aAAa,UAAU,GAAG,EAAE,GAAG,EAAE,YAAY;YACrH;YAEA,MAAM,WAAW,MAAM,MAAM,YAAY;YAEzC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,QAAQ,IAAI,eAChB,CAAC,2BAA2B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACtE,SAAS,MAAM,EACf,SAAS,UAAU,EACnB,YACA;gBAGF,oBAAoB;gBACpB,QAAQ,KAAK,CAAC,qBAAqB;oBACjC,QAAQ,SAAS,MAAM;oBACvB,YAAY,SAAS,UAAU;oBAC/B,KAAK;oBACL,MAAM;oBACN,SAAS,UAAU;gBACrB;gBAEA,2BAA2B;gBAC3B,IAAI,UAAU,aAAa,UAAU,IAAI,iBAAiB,SAAS,MAAM,GAAG;oBAC1E,YAAY;oBACZ,MAAM,MAAM,aAAa,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,sBAAsB;oBACnF;gBACF;gBAEA,mCAAmC;gBACnC,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS,4BAA4B,SAAS,MAAM;wBACpD,MAAM,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;wBACjC,QAAQ,SAAS,MAAM;wBACvB,SAAS;4BACP,iBAAiB,MAAM,OAAO;4BAC9B,KAAK;4BACL,MAAM;wBACR;oBACF;oBACA,SAAS;gBACX;YACF;YAEA,kCAAkC;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,iBAAiB;gBACnB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY;YACnD;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe;YACrB,YAAY;YAEZ,QAAQ,KAAK,CAAC,2CAA2C;gBACvD,OAAO,aAAa,OAAO;gBAC3B,KAAK;gBACL,SAAS,UAAU;YACrB;YAEA,0BAA0B;YAC1B,IAAI,UAAU,aAAa,UAAU,EAAE;gBACrC,MAAM,MAAM,aAAa,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG;gBAClD;YACF;YAEA,sDAAsD;YACtD,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,SAAS;wBACP,iBAAiB,aAAa,OAAO;wBACrC,KAAK;oBACP;gBACF;gBACA,SAAS;YACX;QACF;IACF;IAEA,iDAAiD;IACjD,OAAO;QACL,MAAM;QACN,OAAO;YACL,SAAS;YACT,MAAM;YACN,QAAQ;YACR,SAAS;gBACP,iBAAiB,WAAW,WAAW;gBACvC,KAAK;YACP;QACF;QACA,SAAS;IACX;AACF;AAEA,0DAA0D;AAC1D,MAAM,8BAA8B,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,eAAe,qBAAqB,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACxE,MAAM,SAAS,MAAM,eAAe,MAAM,QAAQ;IAElD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;IAC3C;IAEA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/Github/Pull1106/matches-fashion-clone/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport ProductCarousel from \"@/components/ProductCarousel\"; // Assuming @/ is src/\r\nimport { medusaClient } from \"../../lib/medusa\"; // Corrected path\r\nimport { fetchStrapiAPI } from \"../../lib/strapi\"; // Corrected path\r\nimport {\r\n  StrapiHomepageAttributes,\r\n  MedusaProduct,\r\n  StrapiMedia\r\n} from \"../../lib/types\";\r\n\r\n// Helper to get Strapi media URL\r\nconst getStrapiMediaURL = (mediaObject?: StrapiMedia | null): string | null => {\r\n  if (mediaObject && mediaObject.data && mediaObject.data.attributes && mediaObject.data.attributes.url) {\r\n    const url = mediaObject.data.attributes.url;\r\n    const strapiApiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';\r\n    return url.startsWith('/') ? `${strapiApiUrl}${url}` : url;\r\n  }\r\n  return null;\r\n};\r\n\r\nconst defaultHomepageData: StrapiHomepageAttributes = {\r\n  hero_title: \"SHOP\",\r\n  hero_subtitle: \"24/7 STYLE\",\r\n  editorial_block_title: \"STYLE INSPIRATION\",\r\n  editorial_block_subtitle: \"Discover the latest trends\",\r\n  editorial_block_link: \"/womens/stories\",\r\n  featured_product_block_title: \"THE EDITORS\",\r\n  featured_product_block_subtitle: \"Curated selections\",\r\n  featured_product_block_link: \"/womens/shop/shoes\",\r\n  small_blocks_row: [\r\n    { title: \"DRESSES\", image: \"https://via.placeholder.com/300x400.png?text=Dresses\", href: \"/womens/shop/clothing/dresses\", id:\"sb1\" },\r\n    { title: \"BAGS\", image: \"https://via.placeholder.com/300x400.png?text=Bags\", href: \"/womens/shop/bags\", id:\"sb2\" },\r\n    { title: \"KNITWEAR\", image: \"https://via.placeholder.com/300x400.png?text=Knitwear\", href: \"/womens/shop/clothing/knitwear\", id:\"sb3\" },\r\n  ],\r\n  designer_spotlight_title: \"DESIGNER SPOTLIGHT\",\r\n  designer_spotlight_subtitle: \"Exclusive collections\",\r\n  designer_spotlight_link: \"/womens/designers/the-row\",\r\n  new_arrivals_block_title: \"NEW ARRIVALS\",\r\n  new_arrivals_block_subtitle: \"Just landed\",\r\n  new_arrivals_block_link: \"/womens/just-in\",\r\n};\r\n\r\nexport default async function Home() {\r\n  let homepageData: StrapiHomepageAttributes | null = null;\r\n  let justInProductsMedusa: MedusaProduct[] = [];\r\n\r\n  // Fetch homepage data from Strapi with improved error handling\r\n  const strapiResult = await fetchStrapiAPI(\"/homepage\", { populate: 'deep' });\r\n\r\n  if (strapiResult.success && strapiResult.data?.data?.attributes) {\r\n    homepageData = strapiResult.data.data.attributes;\r\n  } else if (strapiResult.data && strapiResult.data.data && strapiResult.data.data.attributes) {\r\n    // Fallback for legacy API response format\r\n    homepageData = strapiResult.data.data.attributes;\r\n  } else {\r\n    console.warn(\"Homepage data from Strapi failed to load:\", strapiResult.error?.message || \"Unknown error\");\r\n    homepageData = {\r\n      ...defaultHomepageData,\r\n      hero_title: \"SHOP (Content Loading...)\",\r\n      hero_subtitle: \"Please refresh if content doesn't load\",\r\n    };\r\n  }\r\n\r\n  // Fetch products from Medusa with improved error handling\r\n  try {\r\n    const { products } = await medusaClient.products.list({\r\n      limit: 12,\r\n      order: \"-created_at\",\r\n      sales_channel_id: [\"sc_01JXEB1SMN7PBE8Y93SSSQWGNS\"]\r\n    });\r\n    // Use type assertion for now to avoid complex type mapping\r\n    justInProductsMedusa = products as unknown as MedusaProduct[];\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch products from Medusa:\", error);\r\n    // Continue with empty array - the UI will handle the empty state gracefully\r\n  }\r\n\r\n  const justInProductsFormatted = justInProductsMedusa.map(product => ({\r\n    id: product.id,\r\n    name: product.title,\r\n    designer: \"Designer Placeholder\", // Added placeholder for designer\r\n    image: product.thumbnail || '',\r\n    href: `/product/${product.handle || product.id}`,\r\n  }));\r\n\r\n  const heroImageUrl = getStrapiMediaURL(homepageData?.hero_background_image) || \"https://via.placeholder.com/1600x900.png?text=Hero+Image\";\r\n  \r\n  const editorialBlock = {\r\n    title: homepageData?.editorial_block_title || defaultHomepageData.editorial_block_title!,\r\n    subtitle: homepageData?.editorial_block_subtitle || defaultHomepageData.editorial_block_subtitle!,\r\n    image: getStrapiMediaURL(homepageData?.editorial_block_image) || \"https://via.placeholder.com/400x500.png?text=Editorial\",\r\n    href: homepageData?.editorial_block_link || defaultHomepageData.editorial_block_link!,\r\n  };\r\n\r\n  const featuredProductBlock = {\r\n    title: homepageData?.featured_product_block_title || defaultHomepageData.featured_product_block_title!,\r\n    subtitle: homepageData?.featured_product_block_subtitle || defaultHomepageData.featured_product_block_subtitle!,\r\n    image: getStrapiMediaURL(homepageData?.featured_product_block_image) || \"https://via.placeholder.com/400x500.png?text=Product\",\r\n    href: homepageData?.featured_product_block_link || defaultHomepageData.featured_product_block_link!,\r\n  };\r\n  \r\n  const smallBlocks = homepageData?.small_blocks_row || defaultHomepageData.small_blocks_row!;\r\n\r\n  const designerSpotlightBlock = {\r\n    title: homepageData?.designer_spotlight_title || defaultHomepageData.designer_spotlight_title!,\r\n    subtitle: homepageData?.designer_spotlight_subtitle || defaultHomepageData.designer_spotlight_subtitle!,\r\n    image: getStrapiMediaURL(homepageData?.designer_spotlight_image) || \"https://via.placeholder.com/400x500.png?text=Designer\",\r\n    href: homepageData?.designer_spotlight_link || defaultHomepageData.designer_spotlight_link!,\r\n  };\r\n\r\n  const newArrivalsBlock = {\r\n    title: homepageData?.new_arrivals_block_title || defaultHomepageData.new_arrivals_block_title!,\r\n    subtitle: homepageData?.new_arrivals_block_subtitle || defaultHomepageData.new_arrivals_block_subtitle!,\r\n    image: getStrapiMediaURL(homepageData?.new_arrivals_block_image) || \"https://via.placeholder.com/400x500.png?text=New+In\",\r\n    href: homepageData?.new_arrivals_block_link || defaultHomepageData.new_arrivals_block_link!,\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white\">\r\n      <section className=\"relative\">\r\n        <div \r\n          className=\"aspect-[16/9] lg:aspect-[21/9] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center\"\r\n          style={{ backgroundImage: `url(${heroImageUrl})` }}\r\n        >\r\n          {heroImageUrl.includes('placeholder.com') && ( // Show placeholder text if using placeholder URL\r\n            <div className=\"w-full h-full flex items-center justify-center\">\r\n              <span className=\"text-gray-500 text-lg\">Hero Image</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n        <div className=\"absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"text-center text-black bg-white bg-opacity-70 p-4 rounded\">\r\n            <p className=\"text-sm uppercase tracking-wide mb-2\">{homepageData?.hero_subtitle || defaultHomepageData.hero_subtitle}</p>\r\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">{homepageData?.hero_title || defaultHomepageData.hero_title}</h1>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {justInProductsFormatted.length > 0 && (\r\n        <ProductCarousel\r\n          title=\"NEW STYLES\"\r\n          subtitle=\"JUST LANDED\"\r\n          count={justInProductsMedusa.length}\r\n          ctaText=\"Shop Now\"\r\n          ctaHref=\"/womens/just-in/just-in-this-month\"\r\n          products={justInProductsFormatted}\r\n          type=\"just-in\"\r\n        />\r\n      )}\r\n\r\n      <section className=\"max-w-7xl mx-auto px-4 py-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4\">\r\n          <Link href={editorialBlock.href} className=\"group relative overflow-hidden\">\r\n            <div \r\n              className=\"aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center\"\r\n              style={{ backgroundImage: `url(${editorialBlock.image})` }}\r\n            >\r\n               {editorialBlock.image?.includes('placeholder.com') && <span className=\"text-gray-500\">Editorial Image</span>}\r\n            </div>\r\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\r\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">{editorialBlock.title}</h3>\r\n              <p className=\"text-xs text-gray-600 mt-1\">{editorialBlock.subtitle}</p>\r\n            </div>\r\n          </Link>\r\n\r\n          <Link href={featuredProductBlock.href} className=\"group relative overflow-hidden\">\r\n            <div \r\n              className=\"aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center bg-cover bg-center\"\r\n              style={{ backgroundImage: `url(${featuredProductBlock.image})` }}\r\n            >\r\n              {featuredProductBlock.image?.includes('placeholder.com') && <span className=\"text-gray-500\">Product Image</span>}\r\n            </div>\r\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\r\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">{featuredProductBlock.title}</h3>\r\n              <p className=\"text-xs text-gray-600 mt-1\">{featuredProductBlock.subtitle}</p>\r\n            </div>\r\n          </Link>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\r\n          {smallBlocks.map((block) => {\r\n            const imageUrl = typeof block.image === 'string' ? block.image : getStrapiMediaURL(block.image);\r\n            return (\r\n              <Link key={block.id || block.title} href={block.href || '#'} className=\"group relative overflow-hidden\">\r\n                <div \r\n                  className=\"aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center bg-cover bg-center\"\r\n                  style={{ backgroundImage: `url(${imageUrl || 'https://via.placeholder.com/300x400.png?text=Image'})` }}\r\n                >\r\n                  {imageUrl?.includes('placeholder.com') && <span className=\"text-gray-500\">{block.title} Image</span>}\r\n                </div>\r\n                <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3\">\r\n                  <h3 className=\"text-xs font-medium uppercase tracking-wide\">{block.title}</h3>\r\n                </div>\r\n              </Link>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\r\n          <Link href={designerSpotlightBlock.href} className=\"group relative overflow-hidden\">\r\n            <div \r\n              className=\"aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center bg-cover bg-center\"\r\n              style={{ backgroundImage: `url(${designerSpotlightBlock.image})` }}\r\n            >\r\n              {designerSpotlightBlock.image?.includes('placeholder.com') && <span className=\"text-gray-500\">Designer Feature</span>}\r\n            </div>\r\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\r\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">{designerSpotlightBlock.title}</h3>\r\n              <p className=\"text-xs text-gray-600 mt-1\">{designerSpotlightBlock.subtitle}</p>\r\n            </div>\r\n          </Link>\r\n\r\n          <Link href={newArrivalsBlock.href} className=\"group relative overflow-hidden\">\r\n            <div \r\n              className=\"aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center bg-cover bg-center\"\r\n              style={{ backgroundImage: `url(${newArrivalsBlock.image})` }}\r\n            >\r\n              {newArrivalsBlock.image?.includes('placeholder.com') && <span className=\"text-gray-500\">New Arrivals</span>}\r\n            </div>\r\n            <div className=\"absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-4\">\r\n              <h3 className=\"text-sm font-medium uppercase tracking-wide\">{newArrivalsBlock.title}</h3>\r\n              <p className=\"text-xs text-gray-600 mt-1\">{newArrivalsBlock.subtitle}</p>\r\n            </div>\r\n          </Link>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA,4OAA4D,sBAAsB;AAClF,+LAAiD,iBAAiB;AAClE,+LAAmD,iBAAiB;;;;;;AAOpE,iCAAiC;AACjC,MAAM,oBAAoB,CAAC;IACzB,IAAI,eAAe,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;QACrG,MAAM,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG;QAC3C,MAAM,eAAe,6DAA0C;QAC/D,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,eAAe,KAAK,GAAG;IACzD;IACA,OAAO;AACT;AAEA,MAAM,sBAAgD;IACpD,YAAY;IACZ,eAAe;IACf,uBAAuB;IACvB,0BAA0B;IAC1B,sBAAsB;IACtB,8BAA8B;IAC9B,iCAAiC;IACjC,6BAA6B;IAC7B,kBAAkB;QAChB;YAAE,OAAO;YAAW,OAAO;YAAwD,MAAM;YAAiC,IAAG;QAAM;QACnI;YAAE,OAAO;YAAQ,OAAO;YAAqD,MAAM;YAAqB,IAAG;QAAM;QACjH;YAAE,OAAO;YAAY,OAAO;YAAyD,MAAM;YAAkC,IAAG;QAAM;KACvI;IACD,0BAA0B;IAC1B,6BAA6B;IAC7B,yBAAyB;IACzB,0BAA0B;IAC1B,6BAA6B;IAC7B,yBAAyB;AAC3B;AAEe,eAAe;IAC5B,IAAI,eAAgD;IACpD,IAAI,uBAAwC,EAAE;IAE9C,+DAA+D;IAC/D,MAAM,eAAe,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;QAAE,UAAU;IAAO;IAE1E,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE,MAAM,YAAY;QAC/D,eAAe,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU;IAClD,OAAO,IAAI,aAAa,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAC3F,0CAA0C;QAC1C,eAAe,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU;IAClD,OAAO;QACL,QAAQ,IAAI,CAAC,6CAA6C,aAAa,KAAK,EAAE,WAAW;QACzF,eAAe;YACb,GAAG,mBAAmB;YACtB,YAAY;YACZ,eAAe;QACjB;IACF;IAEA,0DAA0D;IAC1D,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,6GAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YACpD,OAAO;YACP,OAAO;YACP,kBAAkB;gBAAC;aAAgC;QACrD;QACA,2DAA2D;QAC3D,uBAAuB;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACvD,4EAA4E;IAC9E;IAEA,MAAM,0BAA0B,qBAAqB,GAAG,CAAC,CAAA,UAAW,CAAC;YACnE,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,KAAK;YACnB,UAAU;YACV,OAAO,QAAQ,SAAS,IAAI;YAC5B,MAAM,CAAC,SAAS,EAAE,QAAQ,MAAM,IAAI,QAAQ,EAAE,EAAE;QAClD,CAAC;IAED,MAAM,eAAe,kBAAkB,cAAc,0BAA0B;IAE/E,MAAM,iBAAiB;QACrB,OAAO,cAAc,yBAAyB,oBAAoB,qBAAqB;QACvF,UAAU,cAAc,4BAA4B,oBAAoB,wBAAwB;QAChG,OAAO,kBAAkB,cAAc,0BAA0B;QACjE,MAAM,cAAc,wBAAwB,oBAAoB,oBAAoB;IACtF;IAEA,MAAM,uBAAuB;QAC3B,OAAO,cAAc,gCAAgC,oBAAoB,4BAA4B;QACrG,UAAU,cAAc,mCAAmC,oBAAoB,+BAA+B;QAC9G,OAAO,kBAAkB,cAAc,iCAAiC;QACxE,MAAM,cAAc,+BAA+B,oBAAoB,2BAA2B;IACpG;IAEA,MAAM,cAAc,cAAc,oBAAoB,oBAAoB,gBAAgB;IAE1F,MAAM,yBAAyB;QAC7B,OAAO,cAAc,4BAA4B,oBAAoB,wBAAwB;QAC7F,UAAU,cAAc,+BAA+B,oBAAoB,2BAA2B;QACtG,OAAO,kBAAkB,cAAc,6BAA6B;QACpE,MAAM,cAAc,2BAA2B,oBAAoB,uBAAuB;IAC5F;IAEA,MAAM,mBAAmB;QACvB,OAAO,cAAc,4BAA4B,oBAAoB,wBAAwB;QAC7F,UAAU,cAAc,+BAA+B,oBAAoB,2BAA2B;QACtG,OAAO,kBAAkB,cAAc,6BAA6B;QACpE,MAAM,cAAc,2BAA2B,oBAAoB,uBAAuB;IAC5F;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;wBAAC;kCAEhD,aAAa,QAAQ,CAAC,oCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;kCAI9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwC,cAAc,iBAAiB,oBAAoB,aAAa;;;;;;8CACrH,8OAAC;oCAAG,WAAU;8CAAuC,cAAc,cAAc,oBAAoB,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAKpH,wBAAwB,MAAM,GAAG,mBAChC,8OAAC,qIAAA,CAAA,UAAe;gBACd,OAAM;gBACN,UAAS;gBACT,OAAO,qBAAqB,MAAM;gBAClC,SAAQ;gBACR,SAAQ;gBACR,UAAU;gBACV,MAAK;;;;;;0BAIT,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,eAAe,IAAI;gCAAE,WAAU;;kDACzC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,CAAC,IAAI,EAAE,eAAe,KAAK,CAAC,CAAC,CAAC;wCAAC;kDAEvD,eAAe,KAAK,EAAE,SAAS,oCAAsB,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAEzF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+C,eAAe,KAAK;;;;;;0DACjF,8OAAC;gDAAE,WAAU;0DAA8B,eAAe,QAAQ;;;;;;;;;;;;;;;;;;0CAItE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,qBAAqB,IAAI;gCAAE,WAAU;;kDAC/C,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,CAAC,IAAI,EAAE,qBAAqB,KAAK,CAAC,CAAC,CAAC;wCAAC;kDAE9D,qBAAqB,KAAK,EAAE,SAAS,oCAAsB,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAE9F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+C,qBAAqB,KAAK;;;;;;0DACvF,8OAAC;gDAAE,WAAU;0DAA8B,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAK9E,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC;4BAChB,MAAM,WAAW,OAAO,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,GAAG,kBAAkB,MAAM,KAAK;4BAC9F,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAA+B,MAAM,MAAM,IAAI,IAAI;gCAAK,WAAU;;kDACrE,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,CAAC,IAAI,EAAE,YAAY,qDAAqD,CAAC,CAAC;wCAAC;kDAEpG,UAAU,SAAS,oCAAsB,8OAAC;4CAAK,WAAU;;gDAAiB,MAAM,KAAK;gDAAC;;;;;;;;;;;;kDAEzF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAA+C,MAAM,KAAK;;;;;;;;;;;;+BARjE,MAAM,EAAE,IAAI,MAAM,KAAK;;;;;wBAYtC;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,uBAAuB,IAAI;gCAAE,WAAU;;kDACjD,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,KAAK,CAAC,CAAC,CAAC;wCAAC;kDAEhE,uBAAuB,KAAK,EAAE,SAAS,oCAAsB,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAEhG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+C,uBAAuB,KAAK;;;;;;0DACzF,8OAAC;gDAAE,WAAU;0DAA8B,uBAAuB,QAAQ;;;;;;;;;;;;;;;;;;0CAI9E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,iBAAiB,IAAI;gCAAE,WAAU;;kDAC3C,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,KAAK,CAAC,CAAC,CAAC;wCAAC;kDAE1D,iBAAiB,KAAK,EAAE,SAAS,oCAAsB,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAE1F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+C,iBAAiB,KAAK;;;;;;0DACnF,8OAAC;gDAAE,WAAU;0DAA8B,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF", "debugId": null}}]}